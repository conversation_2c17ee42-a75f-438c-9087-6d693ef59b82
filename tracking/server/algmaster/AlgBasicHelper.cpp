/**
 * @file AlgBasicHelper.cpp
 * <AUTHOR> (<EMAIL>)
 * @brief
 * @version 0.1
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024
 *
 */
#include "AlgBasicHelper.h"

namespace android::yvr {
AlgBasicHelper::AlgBasicHelper() {
    mDlopenUtils = new DlopenUtils(LIB_ALG_MASTER_CORE);
    initFuncMap();
    loadFunctions();
}

AlgBasicHelper::~AlgBasicHelper() {}

int32_t AlgBasicHelper::InitializeAlgs1(const yvr_algs::AlgsInitializeInfoAM_1 *info) {
    CHECK_FUNC_RETURN(funcInitializeAlgs1);
    return funcInitializeAlgs1(info);
}

int32_t AlgBasicHelper::InitializeAlgs(const char *libs_address, const std::string &thread_json_file) {
    CHECK_FUNC_RETURN(funcInitializeAlgs);
    return funcInitializeAlgs(libs_address, thread_json_file);
}

int32_t AlgBasicHelper::ResetAlgs() {
    CHECK_FUNC_RETURN(funcResetAlgs);
    return funcResetAlgs();
}

int32_t AlgBasicHelper::PutHmdImuDataHost(const ImuDataAM *imu_data) {
    CHECK_FUNC_RETURN(funcPutHmdImuDataHost);
    return funcPutHmdImuDataHost(imu_data);
}

int32_t AlgBasicHelper::PutHmdImuDataQcom(const ImuDataAM *imu_data) {
    CHECK_FUNC_RETURN(funcPutHmdImuDataQcom);
    return funcPutHmdImuDataQcom(imu_data);
}

int32_t AlgBasicHelper::PutBrightImage(const ImageDataAM *image_data) {
    CHECK_FUNC_RETURN(funcPutBrightImage);
    return funcPutBrightImage(image_data);
}

int32_t AlgBasicHelper::PutDarkImage(const ImageDataAM *image_data) {
    CHECK_FUNC_RETURN(funcPutDarkImage);
    return funcPutDarkImage(image_data);
}

int32_t AlgBasicHelper::PutJoysticksImuDataRaw(const CtrlImuDataRawAM *imu_data_raw) {
    CHECK_FUNC_RETURN(funcPutJoysticksImuDataRaw);
    return funcPutJoysticksImuDataRaw(imu_data_raw);
}

int32_t AlgBasicHelper::PutJoysticksHMDPose(const ControllerPoseStateAM *hmd_pose) {
    CHECK_FUNC_RETURN(funcPutJoysticksHMDPose);
    return funcPutJoysticksHMDPose(hmd_pose);
}

int32_t AlgBasicHelper::PutJoysticksHighFreqHMDPose(const HighFreqPoseStateAM *high_freq_pose_state) {
    CHECK_FUNC_RETURN(funcPutJoysticksHighFreqHMDPose);
    return funcPutJoysticksHighFreqHMDPose(high_freq_pose_state);
}

const char *AlgBasicHelper::GetVersion() {
    if (funcGetVersion) {
        return funcGetVersion();
    }
    return nullptr;
}

void AlgBasicHelper::initFuncMap() {
    mFuncMap[FUNC_InitializeAlgs1] = reinterpret_cast<void *>(funcInitializeAlgs1);
    mFuncMap[FUNC_InitializeAlgs] = reinterpret_cast<void *>(funcInitializeAlgs);
    mFuncMap[FUNC_ResetAlgs] = reinterpret_cast<void *>(funcResetAlgs);
    mFuncMap[FUNC_PutBrightImage] = reinterpret_cast<void *>(funcPutBrightImage);
    mFuncMap[FUNC_PutDarkImage] = reinterpret_cast<void *>(funcPutDarkImage);
    mFuncMap[FUNC_PutHmdImuDataHost] = reinterpret_cast<void *>(funcPutHmdImuDataHost);
    mFuncMap[FUNC_PutHmdImuDataQcom] = reinterpret_cast<void *>(funcPutHmdImuDataQcom);
    mFuncMap[FUNC_PutJoysticksHMDPose] = reinterpret_cast<void *>(funcPutJoysticksHMDPose);
    mFuncMap[FUNC_PutJoysticksHighFreqHMDPose] = reinterpret_cast<void *>(funcPutJoysticksHighFreqHMDPose);
    mFuncMap[FUNC_PutJoysticksImuDataRaw] = reinterpret_cast<void *>(funcPutJoysticksImuDataRaw);
    mFuncMap[FUNC_GetVersion] = reinterpret_cast<void *>(funcGetVersion);
}

void AlgBasicHelper::loadFunctions(std::unordered_map<std::string, void *> &funcMap) { mDlopenUtils->loadFunctions(funcMap); }

void AlgBasicHelper::loadFunctions() {
    mDlopenUtils->loadFunctions(mFuncMap);
    funcInitializeAlgs1 = reinterpret_cast<pALGMASTER_InitializeAlgs1>(mFuncMap[FUNC_InitializeAlgs1]);
    funcInitializeAlgs = reinterpret_cast<pALGMASTER_InitializeAlgs>(mFuncMap[FUNC_InitializeAlgs]);
    funcResetAlgs = reinterpret_cast<pALGMASTER_ResetAlgs>(mFuncMap[FUNC_ResetAlgs]);
    funcPutBrightImage = reinterpret_cast<pALGMASTER_PutBrightImage>(mFuncMap[FUNC_PutBrightImage]);
    funcPutDarkImage = reinterpret_cast<pALGMASTER_PutDarkImage>(mFuncMap[FUNC_PutDarkImage]);
    funcPutHmdImuDataHost = reinterpret_cast<pALGMASTER_PutHmdImuDataHost>(mFuncMap[FUNC_PutHmdImuDataHost]);
    funcPutHmdImuDataQcom = reinterpret_cast<pALGMASTER_PutHmdImuDataQcom>(mFuncMap[FUNC_PutHmdImuDataQcom]);
    funcPutJoysticksHMDPose = reinterpret_cast<pALGMASTER_PutJoysticksHMDPose>(mFuncMap[FUNC_PutJoysticksHMDPose]);
    funcPutJoysticksHighFreqHMDPose = reinterpret_cast<pALGMASTER_PutJoysticksHighFreqHMDPose>(mFuncMap[FUNC_PutJoysticksHighFreqHMDPose]);
    funcPutJoysticksImuDataRaw = reinterpret_cast<pALGMASTER_PutJoysticksImuDataRaw>(mFuncMap[FUNC_PutJoysticksImuDataRaw]);
    funcGetVersion = reinterpret_cast<pALGMASTER_GetVersion>(mFuncMap[FUNC_GetVersion]);
}
}  // namespace android::yvr
