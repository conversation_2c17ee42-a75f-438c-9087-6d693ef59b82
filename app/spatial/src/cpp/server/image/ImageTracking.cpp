#include "ImageTracking.h"

namespace yvr {
namespace spatial {
ImageTracking &ImageTracking::getInstance() {
    static ImageTracking s_instance;
    return s_instance;
}

ESpatialError ImageTracking::setImageTrackingEnable(const bool enable) { return SpatialError_None; }

ESpatialError ImageTracking::registerImageTemplate(const std::string imageHash) {
    std::lock_guard<std::mutex> lock(mutex);
    auto it = m_registerCount.find(imageHash);
    if (it == m_registerCount.end()) {
        m_registerCount[imageHash] = 1;
        return SpatialError_None;
    }
    auto count = it->second;
    m_registerCount[imageHash] = count + 1;
    return SpatialError_Duplicate_Register_Template;
}
ESpatialError ImageTracking::unRegisterImageTemplate(const std::string imageHash) {
    std::lock_guard<std::mutex> lock(mutex);
    auto it = m_registerCount.find(imageHash);
    if (it == m_registerCount.end()) return SpatialError_Template_ID_NotFound;
    auto count = it->second;
    if (count == 1) {
        m_registerCount.erase(it);
        return SpatialError_None;
    } else {
        m_registerCount[imageHash] = count - 1;
        return SpatialError_Unregister_Used_Template;
    }
}
}  // namespace spatial
}  // namespace yvr