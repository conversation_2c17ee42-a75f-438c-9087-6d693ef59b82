#pragma once

#include <unordered_map>
#include <string>
#include "SpatialTypes.h"
#include <mutex>

namespace yvr {

namespace spatial {

class ImageTracking {
 public:
    static ImageTracking &getInstance();

    ESpatialError registerImageTemplate(const std::string imageHash);
    ESpatialError setImageTrackingEnable(const bool enable);
    ESpatialError unRegisterImageTemplate(const std::string imageHash);

 private:
    ImageTracking() = default;
    ~ImageTracking() = default;

 private:
    std::unordered_map<std::string, int> m_registerCount;
    std::mutex mutex;
};
}  // namespace spatial
}  // namespace yvr