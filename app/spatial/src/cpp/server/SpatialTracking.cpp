#include "SpatialTracking.h"

#include <md5/md5.hpp>
#include <cutils/properties.h>
#include "screenlog/YvrSLog.h"
#include "yvrutils/DynamicLog.h"
#include "yvrmath/YVRMath.h"

#include "anchor/AnchorCloud.h"
#include "SpatialDatabase.h"
#include "anchor/AnchorMap.h"
#include "SpatialUtils.h"
#include "SpatialService.h"
#include "yvrutils/WPEventLog.h"
#include "yvrutils/ProductUtils.h"

namespace yvr {
namespace spatial {

bool fixAnchorPose(yvrSpatialAnchor *anchor) {
    uint64_t mapId = SpatialTracking::getInstance().currentMapId();
    if (anchor->mapId == mapId) {
        anchor->sysPoseValid = true;
        anchor->sysPose = algToSys(anchor->algPose);
        return true;
    }

    anchor->sysPoseValid = false;

    // 应用Anchor漂移变换
    auto pose = anchor->driftTransform * anchor->algPose;

    // 转换到Mapping坐标系
    if (!mapToMapping(pose, anchor->mapId, SpatialTracking::getInstance().currentMappingId(), &pose)) {
        return false;
    }

    // 转换到6DofMap坐标系
    if (!mappingToMap(pose, SpatialTracking::getInstance().currentMappingId(), mapId, &pose)) {
        return false;
    }

    anchor->sysPoseValid = true;
    anchor->sysPose = algToSys(pose);

    return true;
}

void SpatialTracking::onMapDetect(const MapDetectEventTA *evt) {
    YLOGI("[Spatial] onMapDetect mapId: %" PRIu64, evt->map_id);
    if (m_mapId != evt->map_id) {
        m_mapId = evt->map_id;
        if (auto map = AnchorMap::getInstance().findMap(m_mapId)) {
            m_mappingId = map->mappingId;
        } else {
            m_mappingId = 0;
        }
        notifyMapUpdate();
        // YSLOGI_K(yvrutils::KEY_LINE_1, "mapId: %" PRIu64 ", mappingId: %" PRIu64, m_mapId, m_mappingId);
    }
}

void SpatialTracking::onMapUpdate(const MapUpdateEventTA *evt) {
    // YLOGI("[Spatial] onMapUpdate: mappingId: %llu, length: %zu", (unsigned long long)evt->mapping_id, evt->length);
    AnchorCloud::getInstance().pushMap(evt->mapping_id, evt->data, evt->length);
}

void SpatialTracking::onMapTransform(const MapTransformEventTA *evt) {
    static const char *EVENT_TYPES[] = {"new", "update"};
    YLOGI("[Spatial] onMapTransform: mappingId: %llu, mapId: %llu, transform: %s, type: %s", evt->mapping_id, evt->map_id, utils::poseToString(evt->transform).data(), EVENT_TYPES[evt->type]);

    auto map = AnchorMap::getInstance().findMap(evt->map_id);
    auto mapUuid = utils::stringToUuid(md5(utils::getSerialNo() + "_" + std::to_string(evt->mapping_id)));
    if (!map) {
        yvrSpatialMap map{};
        map.mapId = evt->map_id;
        map.mappingId = evt->mapping_id;
        map.mapUuid = mapUuid;
        map.transform = evt->transform;
        if (AnchorMap::getInstance().addMap(map)) {
            YLOGI("[Spatial] save map success, mapId %llu, mappingId: %llu", evt->map_id, evt->mapping_id);
        } else {
            YLOGE("[Spatial] save map fail, mapId %llu, mappingId: %llu", evt->map_id, evt->mapping_id);
        }
    } else {
        if (evt->type == MTETT_NEW_ITEM) {
            YLOGI("[Spatial] remove expired anchor which map id: %llu", evt->map_id);
            std::vector<Uuid_t> anchorUuids;
            if (SpatialDatabase::getInstance().eraseAnchorByMapId(evt->map_id, &anchorUuids)) {
                for (auto &anchorUuid : anchorUuids) {
                    AnchorMap::getInstance().removeSnapshotMap(anchorUuid);
                }
            }
        }

        map->mapUuid = mapUuid;
        map->transform = evt->transform;
        map->mappingId = evt->mapping_id;
        if (!AnchorMap::getInstance().updateMap(*map)) {
            YLOGE("[Spatial] update map fail: %llu", evt->map_id);
        }
    }

    if (m_mapId == evt->map_id) {
        m_mappingId = evt->mapping_id;
    }

    notifyMapUpdate();
}

void SpatialTracking::onAnchorUpdate(const AnchorUpdateEventTA *evt) {
    YLOGI("[Spatial] onAnchorUpdate: anchorId: %s, transform: %s", evt->anchor_id, utils::poseToString(evt->transform).data());

    // TODO(huhongwei): update snapmap
    auto anchorId = utils::stringToUuid(evt->anchor_id);
    SpatialService::getInstance()->notifyAllClients([&anchorId, evt](SpatialServiceEventListener *l) { l->onAnchorUpdate(anchorId, evt->transform); });
    SpatialDatabase::getInstance().updateAnchorTransform(anchorId, evt->transform);
}

void SpatialTracking::onPlaneUpdate(const PlaneDetectEventTA *evt) {
    YLOGI("[Spatial] onPlaneUpdate: timestamp: %llu, num_planes: %d map_Id:%llu", evt->header.timestamp, evt->num_planes, evt->mapping_id);
    if (m_planeWarehouse != nullptr) {
        auto planes = std::make_unique<PlaneInfo>();
        memset(planes.get(), 0x0, sizeof(PlaneInfo));
        planes->timestamp = evt->header.timestamp;
        planes->mapping_id = evt->mapping_id;
        planes->num_planes = evt->num_planes;
        if (planes->num_planes > 0) {
            memcpy(planes->planes, evt->planes, planes->num_planes * sizeof(PlaneData));
        }
        // 坐标系转换:alg->sys
        for (int i = 0; i < planes->num_planes; i++) {
            planes->planes[i].pose = algToSys(planes->planes[i].pose);
        }
        // 先更新到memory
        m_planeWarehouse->updatePlaneInfo(planes.get());
        // 再通知获取data
        //  TODO 只通知开了feature的client
        SpatialService::getInstance()->notifyAllClients([](SpatialServiceEventListener *l) { l->onPlaneUpdate(); });
    }
}

void SpatialTracking::onMeshBlockUpdate(const MeshBlockUpdateEventTA *evt) {
    // YLOGD("[Spatial] onMeshBlockUpdate: %" PRIu64 ", changeState: %d, mappingId: %" PRIu64, evt->block_id, (int)evt->change_state, evt->mapping_id);

    MeshBlockData data{};
    data.blockId = evt->block_id;
    data.changeState = static_cast<MeshBlockChangeState>(evt->change_state);
    if (evt->change_state != MESH_BLOCK_CHANGE_STATE_REMOVE && evt->change_state != MESH_BLOCK_CHANGE_STATE_CLEAR) {
        // const uint64_t mapId = currentMapId();
        // auto map = AnchorMap::getInstance().findMap(mapId);
        // if (!map || map->mappingId != evt->mapping_id) {
        //     YLOGE("[Spatial] update mesh block fail, map(%d) and mappingId(%" PRIu64 ") not match" PRIu64, mapId, evt->mapping_id);
        //     return;
        // }

        data.pose = yvrPosef{{0, 0, 0, 1}, {evt->origin.x, evt->origin.y, evt->origin.z}};
        data.extent = {evt->extent.x, evt->extent.y, evt->extent.z};
        data.indices = std::vector<uint32_t>{evt->indices, evt->indices + evt->num_indices};
        data.vertices = std::vector<yvrVector3f>{evt->vertices, evt->vertices + evt->num_vertices};

        // data.pose = algToSys(~map->transform * data.pose);
        data.pose = algToSys(data.pose);
    }
    SpatialService::getInstance()->notifyAllClients([&data](SpatialServiceEventListener *l) { l->onMeshBlockUpdate(data); });
}

void SpatialTracking::onRoomSetupUpdate(const RoomSetupUpdateEventTA *evt) {
    // YSLOGI_K(yvrutils::KEY_LINE_2, "room setup: progress: %d%%, state: %d", (int)(evt->progress * 100), (int)evt->change_state);
    SpatialService::getInstance()->notifyAllClients([&evt](SpatialServiceEventListener *l) { l->onRoomSetupUpdate(evt->progress, static_cast<RoomSetupChangeState>(evt->change_state)); });
}

void SpatialTracking::onRoomSetupComplete(const RoomSetupCompleteEventTA *evt) {
    YLOGD("[Spatial] onRoomSetupComplete: mappingId: %" PRIu64 ", ceiling: %.3f, floor: %.3f, wall size: %u", evt->mapping_id, evt->ceiling_level, evt->floor_level, evt->num_planes);

    std::vector<yvrBoundary2d> faces;
    if (evt->success) {
        const uint64_t mapId = currentMapId();
        auto map = AnchorMap::getInstance().findMap(mapId);
        if (!map || map->mappingId != evt->mapping_id) {
            YLOGE("[Spatial] notify room setup complete fail, map(%d) and mappingId(%" PRIu64 ") not match" PRIu64, mapId, evt->mapping_id);
            return;
        }

        // TODO(huhongwei): 构建天花板
        {
            yvrBoundary2d ceiling{};
            ceiling.pose = yvrPosef{{0, 0, 0, 1}, {evt->ceiling_level, 0, 0}};
            // 绕y轴旋转90度
            ceiling.pose = YVRMath::rotate(YVRMath::mat4(1.0f), YVRMath::radians(90.0f), YVRMath::vec3(0.0f, 1.0f, 0.0f)) * ceiling.pose;
            // alg -> sys
            ceiling.pose = algToSys(ceiling.pose);
            ceiling.vertices = std::vector<yvrVector2f>{{-1, -1}, {-1, 1}, {1, 1}, {1, -1}};
            faces.push_back(ceiling);
        }

        // TODO(huhongwei): 构建地板
        {
            yvrBoundary2d floor{};
            floor.pose = yvrPosef{{0, 0, 0, 1}, {evt->floor_level, 0, 0}};
            // 绕y轴旋转90度
            floor.pose = YVRMath::rotate(YVRMath::mat4(1.0f), YVRMath::radians(90.0f), YVRMath::vec3(0.0f, 1.0f, 0.0f)) * floor.pose;
            // alg -> sys
            floor.pose = algToSys(floor.pose);
            floor.vertices = std::vector<yvrVector2f>{{-1, -1}, {-1, 1}, {1, 1}, {1, -1}};
            faces.push_back(floor);
        }

        // 构建墙面
        for (uint32_t i = 0; i < evt->num_planes; i++) {
            Plane3DTA plane = evt->planes[i];
            YVRMath::vec3 xAxis = YVRMath::vec3(plane.vertices[1]) - YVRMath::vec3(plane.vertices[0]);
            xAxis = YVRMath::normalize(xAxis);
            YVRMath::vec3 zAxis = YVRMath::cross(xAxis, YVRMath::vec3(plane.vertices[3]) - YVRMath::vec3(plane.vertices[0]));
            zAxis = YVRMath::normalize(zAxis);
            YVRMath::vec3 yAxis = YVRMath::cross(zAxis, xAxis);  // 保证y轴垂直于xy平面
            yAxis = YVRMath::normalize(yAxis);
            YVRMath::quat rotation = YVRMath::quat_cast(YVRMath::mat3(xAxis, yAxis, zAxis));

            yvrBoundary2d wall{};
            wall.pose = yvrPosef{{rotation.x, rotation.y, rotation.z, rotation.w}, {plane.vertices[0].x, plane.vertices[0].y, plane.vertices[0].z}};
            std::transform(plane.vertices, plane.vertices + 4, std::back_inserter(wall.vertices), [wallPose = wall.pose](const yvrVector3f &v) {
                auto pose = ~wallPose * yvrPosef{{0, 0, 0, 1}, {v.x, v.y, v.z}};
                return yvrVector2f{pose.position.x, pose.position.y};
            });
            // alg -> sys
            wall.pose = algToSys(wall.pose);

            faces.push_back(wall);
        }
    }

    SpatialService::getInstance()->notifyAllClients([&faces](SpatialServiceEventListener *l) { l->onRoomSetupComplete(faces); });
}

void SpatialTracking::onBoundaryUpdate(const BoundaryUpdateEventTA *evt) {
    YLOGD("[Spatial] onBoundaryUpdate: vertex size: %" PRIu64 ", mappingId: %" PRIu64, evt->num_vertices, evt->mapping_id);

    const uint64_t mapId = currentMapId();
    auto map = AnchorMap::getInstance().findMap(mapId);
    if (!map || map->mappingId != evt->mapping_id) {
        YLOGE("[Spatial] update mesh boundary fail, map(%d) and mappingId(%" PRIu64 ") not match" PRIu64, mapId, evt->mapping_id);
        return;
    }

    yvrBoundary2d boundary2d{};

    // slam坐标系下
    const float floorLevel = evt->num_vertices > 0 ? evt->vertices[0].x : -1.7;
    boundary2d.pose = yvrPosef{{0, 0, 0, 1}, {floorLevel, 0, 0}};
    // mapping -> map
    boundary2d.pose = ~map->transform * boundary2d.pose;
    // 绕y轴旋转90度
    boundary2d.pose = YVRMath::rotate(YVRMath::mat4(1.0f), YVRMath::radians(90.0f), YVRMath::vec3(0.0f, 1.0f, 0.0f)) * boundary2d.pose;
    // alg -> sys
    boundary2d.pose = algToSys(boundary2d.pose);
    // 顶点 mapping -> map
    std::transform(evt->vertices, evt->vertices + evt->num_vertices, std::back_inserter(boundary2d.vertices), [transform = map->transform](const yvrVector3f &v) {
        auto pose = ~transform * yvrPosef{{0, 0, 0, 1}, {v.x, v.y, v.z}};
        return yvrVector2f{-pose.position.z, pose.position.y};
    });

    SpatialService::getInstance()->notifyAllClients([&boundary2d](SpatialServiceEventListener *l) { l->onMeshBoundaryUpdate(boundary2d); });
}

void SpatialTracking::onMeshGroundDistanceUpdate(const MeshGroundDistanceUpdateEventTA *evt) { m_meshGroundDistance = evt->distance; }

void SpatialTracking::notifyMapUpdate() {
    static int times = 0;
    // YSLOGI("[%d] notifyMapUpdate mapId: %" PRIu64 ", mappingId: %" PRIu64, ++times, currentMapId(), currentMappingId());
    auto gAnchor = m_currentGroundAnchor.get();
    if (gAnchor && fixAnchorPose(gAnchor)) {
        YLOGD("[Spatial] ground distance update: [%.3f -> %.3f] cause fixAnchorPose", m_groundDistance, gAnchor->sysPose.position.y);
        std::lock_guard lk(m_groundDistanceMutex);
        m_groundDistance = gAnchor->sysPose.position.y;
        notifyGroundDistanceUpdate(m_groundDistance);
    }
    SpatialService::getInstance()->notifyAllClients([](SpatialServiceEventListener *l) { l->onMapUpdate(); });
}

void onTrackingServiceEventsNotify(EventHeaderTA *evt) {
    switch (evt->type) {
        case EVENT_TYPE_MAP_DETECT: {
            SpatialTracking::getInstance().onMapDetect(reinterpret_cast<MapDetectEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_MAP_UPDATE: {
            SpatialTracking::getInstance().onMapUpdate(reinterpret_cast<MapUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_MAP_TRANSFORM: {
            SpatialTracking::getInstance().onMapTransform(reinterpret_cast<MapTransformEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_ANCHOR_UPDATE: {
            SpatialTracking::getInstance().onAnchorUpdate(reinterpret_cast<AnchorUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_PLANE_DETECT: {
            SpatialTracking::getInstance().onPlaneUpdate(reinterpret_cast<PlaneDetectEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_MESH_BLOCK_UPDATE: {
            SpatialTracking::getInstance().onMeshBlockUpdate(reinterpret_cast<MeshBlockUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_ROOM_SETUP_UPDATE: {
            SpatialTracking::getInstance().onRoomSetupUpdate(reinterpret_cast<RoomSetupUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_ROOM_SETUP_COMPLETE: {
            SpatialTracking::getInstance().onRoomSetupComplete(reinterpret_cast<RoomSetupCompleteEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_BOUNDARY_UPDATE: {
            SpatialTracking::getInstance().onBoundaryUpdate(reinterpret_cast<BoundaryUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_MESH_GROUND_DISTANCE_UPDATE: {
            SpatialTracking::getInstance().onMeshGroundDistanceUpdate(reinterpret_cast<MeshGroundDistanceUpdateEventTA *>(evt));
            break;
        }
        case EVENT_TYPE_LARGE_SPACE_MAP_RECOGNIZE_RESULT: {
            auto evtP = reinterpret_cast<LargeSpaceMapStateChangeEventTA *>(evt);
            if (evtP->result == 1) {
                SpatialTracking::getInstance().setLargeSpaceGroundDistance(evtP->lageSpaceGroundDistance);
            }
            break;
        }
        case EVENT_TYPE_MARKER_DETECTION_VARJO: {
            auto evtP = reinterpret_cast<MarkerDetectionEventTA *>(evt);
            SpatialTracking::getInstance().onMarkerDetected(evtP->info);
            break;
        }
        case EVENT_TYPE_IMAGE_TRACKING: {
            auto evtP = reinterpret_cast<ImageTrackingEventTA *>(evt);
            SpatialTracking::getInstance().onImageDetected(evtP->info);
            break;
        }
        default: {
            break;
        }
    }
}

SpatialTracking &SpatialTracking::getInstance() {
    static SpatialTracking s_instance;
    return s_instance;
}

void SpatialTracking::init() {
    CHECK_ZERO_RETURN(TRACKINGAPI_RegisterForNotification(&onTrackingServiceEventsNotify));

    if (!isMeshDetectionEnabled()) {
        // 把地面锚点加入缓存
        auto groundAnchors = SpatialDatabase::getInstance().queryGroundAnchors();
        if (!groundAnchors.empty()) {
            m_currentGroundAnchor = groundAnchors[0];
        }
    }
}

uint64_t SpatialTracking::currentMappingId() { return m_mappingId; }

uint64_t SpatialTracking::currentMapId() { return m_mapId; }

void SpatialTracking::enableShareMap(bool on) {
    if (on) {
        AnchorCloud::getInstance().start();
    } else {
        AnchorCloud::getInstance().stop();
    }
}

void SpatialTracking::startMapUpdate() {
    m_enableMapUpdateTimer.expires_after(std::chrono::minutes(10));
    m_enableMapUpdateTimer.async_wait([](const asio::error_code &ec) {
        if (ec) {
            return;
        }
        YLOGD("[Spatial] disable map update");
        CHECK_ZERO_RETURN(TRACKINGAPI_SlamSetMapUpdateEnable(false));
    });
    YLOGD("[Spatial] enable map update");
    CHECK_ZERO_RETURN(TRACKINGAPI_SlamSetMapUpdateEnable(true));
}

bool SpatialTracking::createAnchor(const yvrSpatialAnchor &anchor, bool createSnapmap) {
    if (AnchorMap::getInstance().checkSnapshotMap(anchor.anchorId)) {
        return true;
    }

    auto anchorUuidStr = utils::uuidToString(anchor.anchorId);
    CreateAnchorInfoTA info{};
    info.anchor_id = anchorUuidStr.data();
    info.anchor_type = ANCHOR_TYPE_MR_ANCHOR;
    info.map_id = anchor.mapId;
    info.pose = anchor.algPose;
    info.create_snapmap = createSnapmap;

    CreateAnchorResultTA result{};
    CHECK_ZERO_RETURN(TRACKINGAPI_SlamCreateAnchor(&info, &result), false);

    if (createSnapmap) {
        std::unique_ptr<void, int32_t (*)(void *)> _(result.snapshot_map_data, TRACKINGAPI_ReleaseMemory);
        if (!AnchorMap::getInstance().saveSnapshotMap(anchor.anchorId, result.snapshot_map_data, result.snapshot_map_length)) {
            YLOGE("[Spatial] save snapshot map fail: %s", anchorUuidStr.data());
            return false;
        }
    }
    return true;
}

bool SpatialTracking::destroyAnchor(const yvrSpatialAnchor &anchor) {
    auto anchorUuidStr = utils::uuidToString(anchor.anchorId);
    CreateAnchorInfoTA info{};
    info.anchor_id = anchorUuidStr.data();
    info.anchor_type = ANCHOR_TYPE_MR_ANCHOR;
    info.map_id = anchor.mapId;
    CHECK_ZERO(TRACKINGAPI_SlamDestroyAnchor(&info));
    CHECK_TRUE(AnchorMap::getInstance().removeSnapshotMap(anchor.anchorId));
    return true;
}

SpatialTracking::SpatialTracking() : m_ioContext(1), m_enableMapUpdateTimer(m_ioContext) {
    CHECK_ZERO_RETURN(TRACKINGAPI_Init());
    CHECK_ZERO(TRACKINGAPI_SlamSetMapUpdateEnable(false));
    m_inLargeSpace = property_get_bool("persist.yvr.large_space_enable", false);
    m_planeWarehouse = new PlaneWarehouse();
    m_ioContext.get_executor().on_work_started();
    std::thread([this] {
        YLOGI("[Spatial] spatial tracking io context start");
        m_ioContext.run();
        YLOGI("[Spatial] spatial tracking io context stop");
    }).detach();
}

SpatialTracking::~SpatialTracking() {
    m_ioContext.stop();
    m_ioContext.get_executor().on_work_finished();

    if (m_detectThread != NULL) {
        m_quitDetect.store(true, std::memory_order_release);
        m_detectorsCondition.notify_one();
        if (m_detectThread->joinable()) m_detectThread->join();
    }
    m_detectThread = nullptr;
    if (m_planeWarehouse != nullptr) {
        m_planeWarehouse->clear();
        m_planeWarehouse = nullptr;
    }
    CHECK_ZERO(TRACKINGAPI_DeInit());
}

void SpatialTracking::subscribe(PlaneWarehouse::Publisher *per) {
    m_planeWarehouse->subscribe(per);
    m_detectorsCondition.notify_one();
}

void SpatialTracking::unSubscribe(PlaneWarehouse::Publisher *per) { m_planeWarehouse->unSubscribe(per); }

/**
 * @deprecated
 */
int SpatialTracking::setPlaneDetectionEnable(const bool enable) {
    int ret = TRACKINGAPI_SetPlaneDetectionEnable(enable);
    if (ret == 0) {
        m_quitDetect.store(!enable, std::memory_order_release);
        if (!m_quitDetect.load(std::memory_order_acquire) && m_detectThread == nullptr) {
            m_detectThread = new std::thread([this]() {
                while (!m_quitDetect.load(std::memory_order_acquire)) {
                    std::unique_lock<std::mutex> lock(m_detectorsMutex);
                    m_detectorsCondition.wait(lock, [this]() { return !m_planeWarehouse->empty() || m_quitDetect.load(std::memory_order_acquire); });
                    if (m_quitDetect.load(std::memory_order_acquire)) {
                        YLOGI("[Spatial] spatial tracking plane detect loop return");
                        return;
                    }
                    TRACKINGAPI_RequestPlaneDetectionInfo();
                    std::this_thread::sleep_for(std::chrono::milliseconds(500));
                }
            });
        } else if (m_quitDetect.load(std::memory_order_acquire)) {
            m_detectorsCondition.notify_one();  // 保证检测线程执行完成
            YLOGI("[Spatial] spatial tracking plane detect stop");
            if (m_detectThread != nullptr && m_detectThread->joinable()) {
                m_detectThread->join();
                delete m_detectThread;
                m_detectThread = nullptr;
            }
        }
    }
    return ret;
}

bool SpatialTracking::setMeshDetectorEnable(const Uuid_t &appId, bool enable) {
    if (enable) {
        if (m_meshDetectorUsers.empty()) {
            CHECK_ZERO_RETURN(TRACKINGAPI_MeshSetDetectorEnable(true), false);
            m_meshDetectorUsers.insert(appId);
        }
    } else {
        m_meshDetectorUsers.erase(appId);
        if (m_meshDetectorUsers.empty()) {
            CHECK_ZERO_RETURN(TRACKINGAPI_MeshSetDetectorEnable(false), false);
        }
    }
    return true;
}

bool SpatialTracking::setPlaneDetectorEnable(const Uuid_t &appId, bool enable) {
    if (enable) {
        BeginPlaneDetectionInfoTA info{};
        info.max_planes = 10;
        info.min_area = 0.09;
        CHECK_ZERO_RETURN(TRACKINGAPI_MeshBeginPlaneDetection(&info), false);
    } else {
        CHECK_ZERO_RETURN(TRACKINGAPI_MeshEndPlaneDetection(), false);
    }
    return true;
}

bool SpatialTracking::beginRoomSetup(const Uuid_t &appId, const RoomSetupBeginInfo *info) {
    MeshRoomSetupBeginInfoTA beginInfo{};
    beginInfo.max_area = info->maxArea;
    beginInfo.min_area = info->minArea;
    CHECK_ZERO_RETURN(TRACKINGAPI_MeshBeginRoomSetup(&beginInfo), false);
    return true;
}

bool SpatialTracking::endRoomSetup(const Uuid_t &appId) {
    CHECK_ZERO_RETURN(TRACKINGAPI_MeshEndRoomSetup(), false);
    return true;
}
bool SpatialTracking::setLargeSpaceGroundDistance(const float distance) {
    if (!m_inLargeSpace) return false;
    YLOGD("[Spatial] setGroundDistance: %.3f cause largespace map recognize", distance);
    m_largeSpaceGroundDistance = distance;
    notifyGroundDistanceUpdate(m_largeSpaceGroundDistance);
    return true;
}

bool SpatialTracking::setGroundDistance(float distance) {
    if (m_inLargeSpace) {
        m_largeSpaceGroundDistance = distance;
        TRACKINGAPI_SetLargeSpaceGroundDistance(m_largeSpaceGroundDistance);
        notifyGroundDistanceUpdate(m_largeSpaceGroundDistance);
        return true;
    }
    auto pose = yvrPosef{{0.f, 0.f, 0.f, 1.f}, {0.f, distance, 0.f}};
    {
        YLOGI("[Spatial] ground distance update:[%.2f -> %.2f] in mapid %llu,mappingid %llu cause manually set", m_groundDistance, distance, currentMapId(), currentMappingId());
        std::lock_guard lk(m_groundDistanceMutex);
        m_groundDistance = distance;
    }
    // 通知所有客户端地图高度发生改变
    notifyGroundDistanceUpdate(m_groundDistance);

    std::lock_guard lk(m_groundAnchorsMutex);
    if (m_currentGroundAnchor) {
        SpatialTracking::getInstance().destroyAnchor(*m_currentGroundAnchor);
        SpatialDatabase::getInstance().eraseAnchor(*m_currentGroundAnchor);
        m_currentGroundAnchor = nullptr;
    }

    // 创建地面高度锚点
    static uint64_t s_seqId = 0;
    std::string token = utils::getSerialNo() + "_" + std::to_string(std::chrono::system_clock::now().time_since_epoch().count()) + "_" + std::to_string(s_seqId++);

    auto currentTimeMs = utils::currentTimeMs();
    yvrSpatialAnchorPtr anchor = std::make_shared<yvrSpatialAnchor>();
    anchor->anchorId = utils::stringToUuid(md5(token));
    anchor->appId = {};
    anchor->mapId = currentMapId();
    anchor->userId = {};
    anchor->algPose = sysToAlg(pose);
    anchor->componentFlags = ANCHOR_COMPONENT_TYPE_LOCATABLE | ANCHOR_COMPONENT_TYPE_STORABLE;
    anchor->components = {};
    anchor->createTimeMs = currentTimeMs;
    anchor->updateTimeMs = currentTimeMs;
    anchor->driftTransform = YVRPoseIdentity();
    anchor->anchorFlags = ANCHOR_PROPERTY_STORAGE_LOCAL_BIT | ANCHOR_PROPERTY_GROUND_BIT;
    anchor->sysPoseValid = true;
    anchor->sysPose = pose;

    if (!SpatialTracking::getInstance().createAnchor(*anchor, false)) {
        YLOGE("[Spatial] create ground anchor failed, spatial tracking create anchor fail: %s", utils::uuidToString(anchor->anchorId).data());
        return false;
    }
    if (!isMeshDetectionEnabled()) {
        if (!SpatialDatabase::getInstance().saveAnchor(*anchor)) {
            YLOGE("[Spatial] create ground anchor failed, save anchor fail: %s", utils::uuidToString(anchor->anchorId).data());
            return false;
        }
    }
    YLOGI("[Spatial] create ground anchor success: %s", utils::uuidToString(anchor->anchorId).data());
    m_currentGroundAnchor = anchor;
    return true;
}

bool SpatialTracking::setGroundDistanceWithMeshDetect() {
    if (m_inLargeSpace) {
        YLOGE("[Spatial] set ground distance with mesh detect failed, in largespace");
        return false;
    }
    auto map = AnchorMap::getInstance().findMap(currentMapId());
    if (!map) {
        YLOGE("[Spatial] set ground distance with mesh detect failed, current map[%llu] in not re", currentMapId());
        return false;
    }

    if (!isMeshDetectionEnabled()) {
        auto groundAnchor = m_currentGroundAnchor;
        if (groundAnchor != NULL) {
            if (groundAnchor->mapId == map->mapId) {
                YLOGD("[Spatial] skip set ground distance with mesh detect, use ground anchor height: %f mapinfo[%llu,%llu]", groundAnchor->sysPose.position.y, map->mapId, map->mappingId);
                {
                    std::lock_guard lk(m_groundDistanceMutex);
                    m_groundDistance = groundAnchor->sysPose.position.y;
                }

                notifyGroundDistanceUpdate(m_groundDistance);
                return true;
            }

            auto groundAnchorMap = AnchorMap::getInstance().findMap(groundAnchor->mapId);
            if (groundAnchorMap && groundAnchorMap->mappingId == map->mappingId) {
                // anchormap to mapping
                auto pose = groundAnchor->algPose;
                pose = groundAnchorMap->transform * pose;
                pose = ~map->transform * pose;
                // mapping to currentmap
                // 通知所有客户端地图高度发生改变
                YLOGD("[Spatial] skip set ground distance with mesh detect, use ground anchor height(%llu -> %llu): (%f -> %f)", groundAnchor->mapId, map->mapId, groundAnchor->sysPose.position.y,
                      pose.position.x);

                {
                    std::lock_guard lk(m_groundDistanceMutex);
                    m_groundDistance = groundAnchor->sysPose.position.y;
                }
                notifyGroundDistanceUpdate(m_groundDistance);
                return true;
            }
        }
    }
    YLOGD("[Spatial] ground distance update:[%.3f - >%.3f] current mappingid[%llu],mapid[%llu] cause use mesh ground distance ", m_groundDistance, m_meshGroundDistance, map->mappingId, map->mapId);
    return setGroundDistance(m_meshGroundDistance);
}

void SpatialTracking::onClientAdded(SpatialServiceEventListener *client) { client->onGroundDistanceUpdate(currentMapId(), m_inLargeSpace ? m_largeSpaceGroundDistance : m_groundDistance); }

// 通知所有客户端地图高度发生改变
void SpatialTracking::notifyGroundDistanceUpdate(const float groundDistance) {
    SpatialService::getInstance()->notifyAllClients(
        [this, groundDistance](SpatialServiceEventListener *l) { l->onGroundDistanceUpdate(currentMapId(), m_inLargeSpace ? m_largeSpaceGroundDistance : groundDistance); });
}

void SpatialTracking::onMarkerDetected(const MarkerDetectionInfoTA markerInfo) {
    // for (int i = 0; i < 4; i++) {
    //     YLOGE("EVENT_TYPE_MARKER_DETECTION 2 %d [%f,%f,%f]", i, markerInfo.pts[i].x, markerInfo.pts[i].y, markerInfo.pts[i].z);
    // }
    // YLOGE("onMarkerDetected pose[%f,%f,%f:%f,%f,%f,%f]", markerInfo.map_marker.position.x, markerInfo.map_marker.position.y, markerInfo.map_marker.position.z, markerInfo.map_marker.orientation.x,
    //       markerInfo.map_marker.orientation.y, markerInfo.map_marker.orientation.z, markerInfo.map_marker.orientation.w);
    SpatialService::getInstance()->notifyAllClients([this, markerInfo](SpatialServiceEventListener *l) {
        MarkerData markerData;
        markerData.id = markerInfo.marker_id;
        markerData.pose = algToSys(markerInfo.map_marker);
        markerData.timestamp = markerInfo.timestamp;
        markerData.type = markerInfo.marker_type;
        memcpy(markerData.pts, markerInfo.pts, 4 * sizeof(yvrVector3f));
        markerData.confidence = markerInfo.confidence;
        l->onMarkerUpdate(markerData);
    });
}

void SpatialTracking::onImageDetected(const ImageTrackingInfoTA imageInfoTA) {
    SpatialService::getInstance()->notifyAllClients([this, imageInfoTA](SpatialServiceEventListener *l) {
        ImageTrackingInfo imageInfo;
        imageInfo.confidence = imageInfoTA.confidence;
        // imageInfo.imageId = imageInfoTA.image_id;
        memcpy(imageInfo.imageId, imageInfoTA.image_id, 128);
        imageInfo.map_id = imageInfoTA.map_id;
        imageInfo.pose = algToSys(imageInfoTA.pose);
        imageInfo.timestamp = imageInfoTA.timestamp;
        imageInfo.width = imageInfoTA.width;
        imageInfo.height = imageInfoTA.height;
        YLOGI("onImageDetected imageId[%s] pose[%f,%f,%f:%f,%f,%f,%f]", imageInfo.imageId, imageInfo.pose.position.x, imageInfo.pose.position.y, imageInfo.pose.position.z,
              imageInfo.pose.orientation.x, imageInfo.pose.orientation.y, imageInfo.pose.orientation.z, imageInfo.pose.orientation.w);
        l->onImageTrackingUpdate(imageInfo);
    });
}

void SpatialTracking::scheduleMarkerPoseRefresh() {
    YLOGE("scheduleMarkerPoseRefresh");
    SpatialService::getInstance()->notifyAllClients([](SpatialServiceEventListener *l) { l->onMarkerPoseRefresh(); });
}

std::string SpatialTracking::dumpSpatial(AnchorDumpFlags_t flags) {
    std::stringstream ss;
    if (flags & ANCHOR_DUMP_GROUND) {
        ss << "\nGround: \n";
        auto groundAnchor = m_currentGroundAnchor.get();
        auto algPoseRender = sysToAlg(groundAnchor->sysPose);
        if (groundAnchor != nullptr) {
            ss << "\n"
               << "anchor: " << utils::uuidToString(groundAnchor->anchorId) << "\n"
               << "mapId: " << groundAnchor->mapId << "\n"
               << "sysPose(Render): " << (groundAnchor->sysPoseValid ? utils::dumpPose(groundAnchor->sysPose) : "invalid") << "\n"
               << "algPose(Create): " << utils::dumpPose(groundAnchor->algPose) << "\n"
               << "algPose(Render): " << (groundAnchor->sysPoseValid ? utils::dumpPose(algPoseRender) : "invalid") << "\n"
               << "driftTransform: " << utils::dumpPose(groundAnchor->driftTransform) << "\n"
               << "createTime: " << utils::dumpTimestamp(groundAnchor->createTimeMs) << "\n"
               << "anchorFlags: " << utils::dumpAnchorFlags(groundAnchor->anchorFlags) << "\n"
               << "componentFlags: " << utils::dumpAnchorComponentFlags(groundAnchor->componentFlags) << "\n"
               << "components: " << utils::componentsToString(groundAnchor->components) << "\n";
        } else {
            ss << "groundAnchor not set!" << "\n";
        }
    }
    return ss.str();
}

}  // namespace spatial
}  // namespace yvr
