#pragma once
#include <cstdint>
#include "Statusbitmap.h"
#include "foo/Logger.h"

class ScreenOn {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxxx0 -> 0x xxxxx1
        return currentState & SCREEN_STATUS_BIT - previouState & SCREEN_STATUS_BIT;
    }
};

class ScreenOff {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxxx1 -> 0x xxxxx0
        return previouState & SCREEN_STATUS_BIT - currentState & SCREEN_STATUS_BIT;
    }
};

class TrackingEnabled {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxx0x -> 0x xxxx1x
        return currentState & TRACKING_STATUS_BIT - previouState & TRACKING_STATUS_BIT;
    }
};

class TrackingDisabled {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxx1x -> 0x xxxx0x
        return previouState & TRACKING_STATUS_BIT - currentState & TRACKING_STATUS_BIT;
    }
};

// ScreenOn && TrackingEnabled
class TrackingLoss {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxx011 -> 0x xxx111
        uint32_t screenonAnTrackingEnable = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT;
        uint32_t trackingLoss = screenonAnTrackingEnable | TRACKING_STATUS_BIT;
        bool preRecover = (previouState & (trackingLoss)) == screenonAnTrackingEnable;
        bool curLoss = (currentState ^ trackingLoss) == trackingLoss;
        return preRecover && curLoss;
    }
};

// ScreenOn && TrackingEnabled
class TrackingRecover {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxx111 -> 0x xxx011
        uint32_t screenonAnTrackingEnable = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT;
        uint32_t trackingLoss = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT | TRACKING_STATUS_BIT;
        bool preLoss = (currentState ^ trackingLoss) == trackingLoss;
        bool curRecover = (currentState & (trackingLoss)) == screenonAnTrackingEnable;
        return preLoss && curRecover;
    }
};

// ScreenOn && TrackingEnabled
class BoundaryEnabled {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxx0011 -> 0x xxxx1011
        bool preDisable;
        bool curEnable;
        return preDisable && curEnable;
    }
};

// boundaryDisable || TrackingDisable
class BoundaryDisabled {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x xxxx0xxx
        bool trackingDisable = (currentState & TRACKING_ENABLE_BIT) == 0;  // 0x xxxx0x
        if (trackingDisable) return true;
        bool boundaryDisable = (currentState & BOUNDARY_ENABLE_BIT) == 0;  // 0x xx0xxx
        if (boundaryDisable) return true;
        return false;
    }
};

// ScreenOn && TrackingEnabled && BoundaryEnabled
class BoundaryResume {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        // 0x 11011
        uint32_t resumeBit = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT | BOUNDARY_ENABLE_BIT | BOUNDARY_RESUME_BIT;
        // uint32_t all = resumeBit | TRACKING_STATUS_BIT;
        bool curResume = currentState == resumeBit;
        FOO_LOG("BoundaryResume curResume %d,%d,%d", curResume ? 1 : 0, resumeBit | currentState, resumeBit);
        return curResume;
    }
};

//   TrackingDisable ||  BoundaryDisable || BoundaryPause || trackingloss
class BoundaryPause {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        bool trackingDisable = (currentState & TRACKING_ENABLE_BIT) == 0;  // 0x xxxx0x
        FOO_LOG("BoundaryPause trackingDisable:%d", trackingDisable ? 1 : 0);
        if (trackingDisable) return true;
        bool trackingloss = currentState & TRACKING_STATUS_BIT;  // 0x xxx1xx
        FOO_LOG("BoundaryPause trackingloss:%d", trackingloss ? 1 : 0);
        if (trackingloss) return true;
        bool boundaryDisable = (currentState & BOUNDARY_ENABLE_BIT) == 0;  // 0x xx0xxx
        FOO_LOG("BoundaryPause boundaryDisable:%d", boundaryDisable ? 1 : 0);
        if (boundaryDisable) return true;
        bool pause = (currentState & BOUNDARY_RESUME_BIT) == 0;  // 0x x0xxxx
        FOO_LOG("BoundaryPause boundaryPause:%d,%d,%d,%d,", pause ? 1 : 0, currentState & BOUNDARY_RESUME_BIT, currentState, BOUNDARY_RESUME_BIT);
        if (pause) return true;
        return false;
    }
};
// (ScreenOn && TrackingEnabled && homepurevirtual) || BoundaryResume
class MeshNearestObjectEnable {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        uint32_t resumeBit = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT | BOUNDARY_RESUME_BIT;
        bool bounaryResume = (resumeBit & currentState) == resumeBit;
        if (!bounaryResume) {
            uint32_t enableBit = SCREEN_STATUS_BIT | TRACKING_ENABLE_BIT | HOME_PURE_VIRTUAL_ENABLE_BIT;
            bool curResume = (enableBit & currentState) == enableBit;
            FOO_LOG("MeshNearestObjectEnable curResume %d", curResume ? 1 : 0);
            return curResume;
        }
        FOO_LOG("MeshNearestObjectEnable bounaryResume %d", bounaryResume ? 1 : 0);
        return bounaryResume;
    }
};

class MeshNearestObjectDisable {
 public:
    bool isSatisfy(const uint32_t previouState, const uint32_t currentState) {
        bool trackingDisable = (currentState & TRACKING_ENABLE_BIT) == 0;
        FOO_LOG("MeshNearestObjectDisable trackingDisable:%d", trackingDisable ? 1 : 0);
        if (trackingDisable) return true;
        bool screenOff = (currentState & SCREEN_STATUS_BIT) == 0;
        FOO_LOG("MeshNearestObjectDisable screen_off:%d", screenOff ? 1 : 0);
        if (screenOff) return true;
        bool trackingloss = currentState & TRACKING_STATUS_BIT;  // 0x xxx1xx
        FOO_LOG("MeshNearestObjectDisable trackingloss:%d", trackingloss ? 1 : 0);
        if (trackingloss) return true;
        bool pause = (currentState & BOUNDARY_RESUME_BIT) == 0;  // 0x x0xxxx
        bool pureVirtualDisable = (currentState & HOME_PURE_VIRTUAL_ENABLE_BIT) == 0;
        FOO_LOG("MeshNearestObjectDisable boundaryResume:%d,pureVirtualDisable %d", pause ? 1 : 0, pureVirtualDisable ? 1 : 0);
        return pause && pureVirtualDisable;
    }
};