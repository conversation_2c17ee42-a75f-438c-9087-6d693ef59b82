#pragma once
#include <cstdint>

const uint32_t SCREEN_STATUS_BIT = 1 << 0;             // 1亮屏, 0灭屏
const uint32_t TRACKING_ENABLE_BIT = 1 << 1;           // 1开启追踪, 0关闭追踪
const uint32_t TRACKING_STATUS_BIT = 1 << 2;           // 1追踪丢失，0追踪正常
const uint32_t BOUNDARY_ENABLE_BIT = 1 << 3;           // 1开启边界，0关闭边界
const uint32_t BOUNDARY_RESUME_BIT = 1 << 4;           // 1边界启用，0边界不启用
const uint32_t DEVICE_VRMODE_BIT = 1 << 5;             // 1进入VR模式，0退出VR模式
const uint32_t HOME_PURE_VIRTUAL_ENABLE_BIT = 1 << 6;  // 1进入虚拟场景，0退出虚拟场景