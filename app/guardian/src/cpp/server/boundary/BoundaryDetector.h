#pragma once
#include "BoundaryDefine.h"
#include "BoundaryCommon.h"
#include "trackingapi/tracking_types.h"
#include "foo/MacroUtils.h"
#include "IBoundaryDetector.h"

namespace yvr {

namespace systemux {

class BoundaryDetector : public IBoundaryDetector {
 public:
    static BoundaryDetectorPtr Create();
    void AddBoundary(BoundaryWeakPtr aBoundary);
    void SetGroundOffSet(const float aGroundOffSet) const;
    void SetEffectDistance(float distance) const;
    int32_t TestCollision(const yvrPosef pointPose, CollisionResult& outResult, bool wordSpace) override;
    int32_t TestCollision(const PoseStateTA pointPoseState, CollisionResult& outResult, bool wordSpace) override;
    bool IsActive() override;

 protected:
    struct State;
    BoundaryDetector(State& aState);
    ~BoundaryDetector();
    // ITick

 private:
    State& m;
    BoundaryDetector() = delete;
    FOO_NO_DEFAULTS(BoundaryDetector)
};
}  // namespace systemux
}  // namespace yvr