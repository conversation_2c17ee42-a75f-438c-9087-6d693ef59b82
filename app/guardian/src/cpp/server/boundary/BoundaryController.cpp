#include "BoundaryController.h"
#include <foo/ConcreteClass.h>
#include <functional>
#include <trackingapi/TrackingAPI_Helper.h>
#include <foo/Logger.h>
#include "yvrmath/YVRMath.h"
#include <cmath>
#include "GuardianBoundary.h"
#include "SystemUXMessageType.h"
#include "BoundaryFactory.h"
#define LOCAL_BOUNDARY_DATA_LENGTH 90

namespace {
#define SECONDS_TO_NANOSECONDS 1e9
#define NANOSECONDS_TO_SECONDS 1e-9
double GetTime_Sec() {
    struct timespec now;
    clock_gettime(CLOCK_MONOTONIC, &now);
    return (now.tv_sec * SECONDS_TO_NANOSECONDS + now.tv_nsec) * NANOSECONDS_TO_SECONDS;
}
}  // namespace

namespace yvr {

namespace systemux {

const int NUM_BOUNDARY_FRAME = 4;

const int BOUNDARY_DATA_LENGTH = 512;

const float HEIGHT_OF_BOUNDARY = 3.0f;

const float CD_BEGIN_DISPLAY_BOUNDARY = 0.4f;

const float TESSELLATION_FOR_BOUNDARY_DATA = 0.05f;

const float SPEED_RATE_FOR_CD = 0.0f;

const float WIDTH_OF_GROUND_MESH = 0.0375f;

const float RADIUS_FOR_LOCAL_MESH = 1.0f;

BoundaryControllerPtr BoundaryController::Create(android::sp<yvrutils::MyLooper> looper, const android::sp<yvrutils::MessageHandler>& handler, BoundaryFactoryPtr factory) {
    BoundaryControllerPtr result = std::make_shared<foo::ConcreteClassSimple<BoundaryController> >();
    result->m_Handler = handler;
    result->m_Looper = looper;
    result->m_BoundaryFactory = factory;
    result->Init();
    return result;
}

void BoundaryController::Init() {
    auto listener = std::bind(&BoundaryController::onSpatialEvent, this, std::placeholders::_1);
    // bInitMesh.store(false);
    yvr::spatial::System()->setEventListener(listener);

    yvr::spatial::Guardian()->getEffectiveBoundaries(&m_BoundaryAnchorUuids);
    // FOO_LOG("BoundaryController::Init() m_BoundaryAnchorUuids.size() = %d", m_BoundaryAnchorUuids.size());
}
void BoundaryController::onTick() {
    float distance = 11100.f;
    yvr::spatial::Guardian()->getGroundDistance(&distance);
    if (m_GroundDistance != distance) {
        m_GroundDistance = distance;
        yvrutils::Message msg;
        msg.what = SystemUXMessageType::GROUND_DISTANCE_UPDATA;
        msg.data = reinterpret_cast<void*>(&m_GroundDistance);
        m_Looper->sendMessage(m_Handler, msg);
    }
}

BoundaryController::BoundaryController() : m_GroundDistance(100.f) {}

BoundaryController::~BoundaryController() {}

int BoundaryController::CreateLocalBoundary(const float radius) {
    PoseStateTA pose;
    int32_t ret = TRACKINGAPI_GetHeadPose(&pose);
    yvrPosef recenterPose;
    TRACKINGAPI_GetRecenterPose(&recenterPose);
    auto headPose = algToSys(pose.pose);
    headPose = ~recenterPose * headPose;
    YVRMath::vec2 centerPoint(headPose.position.x, headPose.position.z);
    m_BoundaryFactory->CreateLocalBoundary(centerPoint, radius);
    yvrutils::Message msg;
    msg.what = SystemUXMessageType::ADD_RENDER_OBJECT_BOUNDARY;
    m_Looper->sendMessage(m_Handler, msg);

    // ADD_RENDER_OBJECT_BOUNDARY
    return 0;
}

void BoundaryController::CreateGameBoundary(std::vector<float> points) {
    // PoseStateTA pose;
    // int32_t ret = TRACKINGAPI_GetHeadPose(&pose);

    yvrPosef recenterPose;
    TRACKINGAPI_GetRecenterPose(&recenterPose);
    std::vector<float> worldPoints;
    for (int i = 0; i < points.size(); i += 2) {
        yvrPosef tempPose;
        tempPose.position.x = points[i];
        tempPose.position.y = 0.0f;
        tempPose.position.z = points[i + 1];
        tempPose.orientation.x = 0.0;
        tempPose.orientation.y = 0.0;
        tempPose.orientation.z = 0.0;
        tempPose.orientation.w = 1.0;
        tempPose = ~recenterPose * tempPose;
        worldPoints.emplace_back(tempPose.position.x);
        worldPoints.emplace_back(tempPose.position.z);
    }
    m_BoundaryFactory->CreateGameBoundary(worldPoints);
    yvrutils::Message msg;
    msg.what = SystemUXMessageType::ADD_RENDER_OBJECT_BOUNDARY;
    m_Looper->sendMessage(m_Handler, msg);
}

void BoundaryController::SetBoundaryUpdateEnable(bool enable) {
    auto Iguardian = yvr::spatial::Guardian();
    Iguardian->setBoundaryUpdateEnable(enable);
}

void BoundaryController::onSpatialEvent(const ::yvr::spatial::Event_t& event) {
    if (event.eventType == yvr::spatial::EventType::EventType_GuardianBoundaryUpdate) {
        switch (event.GuardianBoundaryUpdate.changeState) {
            case yvr::spatial::BoundaryChangeState::BOUNDARY_CHANGE_STATE_ADD: {
                // FOO_LOG("onSpatialEvent BOUNDARY_CHANGE_STATE_ADD");

                yvr::spatial::Guardian()->getEffectiveBoundaries(&m_BoundaryAnchorUuids);
                // FOO_LOG("BoundaryController::Init() m_BoundaryAnchorUuids.size() = %d", m_BoundaryAnchorUuids.size());

                for (auto uuid : m_BoundaryAnchorUuids) {
                    std::vector<yvrVector2f> m_Boundary2d;
                    yvr::spatial::Scene()->getBoundary2d(uuid, &m_Boundary2d);
                    // FOO_LOG("BoundaryController::Init() m_Boundary2d.size() = %d", m_Boundary2d.size());
                    // for (auto point : m_Boundary2d) {
                    //     FOO_LOG("BoundaryController::Init() point = %f, %f", point.x, point.y);
                    // }
                }
                // TODO 添加
                // IGuardian::getEffectiveBoundaries(std::vector<Uuid_t> *uuids)
                // IScene::getBoundary2d(const Uuid_t &anchorUuid, std::vector<yvrVector2f> *boundary2d)
                // IAnchor::getAnchorPose（const Uuid_t *anchorUuid, Event_AnchorPoseUpdate_t *pose）
            }

            break;
            case yvr::spatial::BoundaryChangeState::BOUNDARY_CHANGE_STATE_REMOVE: {
                FOO_LOG("onSpatialEvent BOUNDARY_CHANGE_STATE_REMOVE");
            } break;
            case yvr::spatial::BoundaryChangeState::BOUNDARY_CHANGE_STATE_UPDATE:
                FOO_LOG("onSpatialEvent BOUNDARY_CHANGE_STATE_UPDATE");
                break;

            default:
                break;
        }
    }
}

}  // namespace systemux
}  // namespace yvr
