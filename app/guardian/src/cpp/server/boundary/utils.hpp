#pragma once
#include "yvrmath/YVRMath.h"
#include <vector>

static double distanceOfVec2(YVRMath::vec2 a, YVRMath::vec2 b) {
    double ax = a.x;
    double ay = a.y;
    double bx = b.x;
    double by = b.y;
    double dist = (ax - bx) * (ax - bx) + (ay - by) * (ay - by);
    return dist;
}

// 计算多边形中心
static YVRMath::vec2 calculateCentroid(const std::vector<YVRMath::vec2>& polygon) {
    YVRMath::vec2 centroid = {0.0f, 0.0f};
    size_t numVertices = polygon.size();

    if (numVertices == 0) {
        return centroid;  // 返回原点作为空多边形的质心
    }

    for (const auto& vertex : polygon) {
        centroid.x += vertex.x;
        centroid.y += vertex.y;
    }

    centroid.x /= numVertices;
    centroid.y /= numVertices;

    return centroid;
}

static YVRMath::vec3 pointToLine(YVRMath::vec2 lineStartPoint, YVRMath::vec2 lineEndPoint, YVRMath::vec2 testPoint) {
    YVRMath::vec2 closestPoint;
    YVRMath::vec3 result;

    YVRMath::vec2 vecEndToStart = lineStartPoint - lineEndPoint;
    YVRMath::vec2 vecStartToEnd = -vecEndToStart;
    YVRMath::vec2 vecEndToTest = testPoint - lineEndPoint;
    YVRMath::vec2 vecStartToTest = testPoint - lineStartPoint;

    float cosOfStart = 0.0f;
    float cosOfEnd = 0.0f;
    float disOfStartToEnd = YVRMath::length(vecEndToStart);
    float disOfTestToEnd = YVRMath::length(vecEndToTest);
    float disOfTestToStart = YVRMath::length(vecStartToTest);

    if (disOfTestToEnd < 0.0001) {
        result.x = lineEndPoint.x;
        result.y = lineEndPoint.y;
        result.z = 0.0f;
        return result;
    }

    if (disOfTestToStart < 0.0001) {
        result.x = lineStartPoint.x;
        result.y = lineStartPoint.y;
        result.z = 0.0f;
        return result;
    }

    cosOfStart = YVRMath::dot(vecStartToEnd, vecStartToTest) / (disOfStartToEnd * disOfTestToStart);
    if (cosOfStart <= 0.0f) {
        result.x = lineStartPoint.x;
        result.y = lineStartPoint.y;
        result.z = disOfTestToStart;
        return result;
    }

    cosOfEnd = YVRMath::dot(vecEndToStart, vecEndToTest) / (disOfStartToEnd * disOfTestToEnd);
    if (cosOfEnd <= 0.0f) {
        result.x = lineEndPoint.x;
        result.y = lineEndPoint.y;
        result.z = disOfTestToEnd;
        return result;
    }

    float rate = (disOfTestToStart * cosOfStart) / disOfStartToEnd;
    closestPoint.x = lineStartPoint.x + (lineEndPoint.x - lineStartPoint.x) * rate;
    closestPoint.y = lineStartPoint.y + (lineEndPoint.y - lineStartPoint.y) * rate;
    result.x = closestPoint.x;
    result.y = closestPoint.y;
    result.z = YVRMath::distance(closestPoint, testPoint);
    return result;
}
