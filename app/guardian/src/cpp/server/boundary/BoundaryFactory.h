#pragma once

#include <memory>
#include <vector>
#include <foo/MacroUtils.h>
#include "BoundaryDefine.h"
#include "yvrmath/YVRMath.h"
#include "BoundaryCommon.h"

namespace yvr {

namespace systemux {

class BoundaryFactory {
 public:
    static BoundaryFactoryPtr Create();
    BoundaryPtr CreateGameBoundary(std::vector<float> boundaryPoints);
    BoundaryPtr CreateLocalBoundary(YVRMath::vec2 boundaryCenter, const float boundaryRadius, uint32_t pointCount = 90);
    BoundaryPtr CreateLocalBoundary(YVRMath::vec2 boundaryCenter, uint32_t pointCount = 90);
    void SetLocalBoundaryRadio(float radius);
    BoundaryWeakPtr GetWeakLocalBoundary() const;
    void ClearAllBoundary();

 protected:
    struct State;
    BoundaryFactory(State& aState);
    ~BoundaryFactory();

 private:
    State& m;
    BoundaryFactory() = delete;
    FOO_NO_DEFAULTS(BoundaryFactory)
};

}  // namespace systemux
}  // namespace yvr