#include "BoundaryFactory.h"
#include "Boundary.h"
#include <unordered_map>
#include <mutex>
#include "foo/ConcreteClass.h"
#include "utils.hpp"

static uint64_t boundaryId = 0;

namespace yvr {

namespace systemux {

class BoundaryBuilder;
typedef std::shared_ptr<BoundaryBuilder> BoundaryBuilderPtr;

class BoundaryBuilder {
 private:
    BoundaryPtr boundary;
};

struct BoundaryFactory::State {
    std::mutex lock;
    std::unordered_map<std::string, BoundaryBuilderPtr> gameBoundarys;
    BoundaryPtr currentLocalBoundary;
    float localBoundaryRadius;
    State() : localBoundaryRadius(1.5f) {}
    ~State() {}
};

BoundaryFactoryPtr BoundaryFactory::Create() { return std::make_shared<foo::ConcreteClass<BoundaryFactory, BoundaryFactory::State>>(); }

BoundaryPtr BoundaryFactory::CreateGameBoundary(std::vector<float> boundaryPoints) {
    if (boundaryPoints.size() < 6) return nullptr;
    std::lock_guard<std::mutex> guard(m.lock);
    std::vector<YVRMath::vec2> points;
    points.reserve(boundaryPoints.size() / 2);
    for (int i = 0; i < boundaryPoints.size(); i += 2) {
        points.emplace_back(YVRMath::vec2(boundaryPoints[i], boundaryPoints[i + 1]));
    }
    auto center = calculateCentroid(points);
    m.currentLocalBoundary = Boundary::Create(points, center, boundaryId++, BOUNDAY_TYPE_GAME, m.localBoundaryRadius);
    return m.currentLocalBoundary;
}
BoundaryPtr BoundaryFactory::CreateLocalBoundary(YVRMath::vec2 boundaryCenter, const float boundaryRadius, uint32_t pointCount) {
    // std::lock_guard<std::mutex> guard(m.lock);
    m.localBoundaryRadius = boundaryRadius;
    return CreateLocalBoundary(boundaryCenter, pointCount);
}

BoundaryPtr BoundaryFactory::CreateLocalBoundary(YVRMath::vec2 boundaryCenter, uint32_t pointCount) {
    std::lock_guard<std::mutex> guard(m.lock);
    std::vector<YVRMath::vec2> points;
    points.reserve(pointCount);
    float angle, x, y;
    for (int i = 0; i < pointCount; i++) {
        angle = (2.0f * YVRMath::PI() * i) / pointCount;
        x = boundaryCenter.x + m.localBoundaryRadius * cos(angle);
        y = boundaryCenter.y + m.localBoundaryRadius * sin(angle);
        points.emplace_back(YVRMath::vec2(x, y));
    }
    m.currentLocalBoundary = Boundary::Create(points, boundaryCenter, boundaryId++, BOUNDAY_TYPE_LOCAL, m.localBoundaryRadius);
    return m.currentLocalBoundary;
}
BoundaryWeakPtr BoundaryFactory::GetWeakLocalBoundary() const { return m.currentLocalBoundary; }
void BoundaryFactory::SetLocalBoundaryRadio(float radius) {}

void BoundaryFactory::ClearAllBoundary() {
    if (m.currentLocalBoundary) m.currentLocalBoundary.reset();
}

BoundaryFactory::BoundaryFactory(State& aState) : m(aState) {}
BoundaryFactory::~BoundaryFactory() {}

}  // namespace systemux
}  // namespace yvr