#include <memory>
#include "BoundaryDetector.h"
#include "foo/ConcreteClass.h"
#include "trackingapi/TrackingAPI_Helper.h"
#include "yvrmath/YVRMath.h"
#include "foo/Logger.h"
#include "Boundary.h"
#include "utils.hpp"

using namespace foo;

namespace yvr {

namespace systemux {
#define CHECK_BOUNDARY_VALID(RESULT) \
    if (!boundary.lock()) return RESULT;
struct BoundaryDetector::State {
    BoundaryWeakPtr boundary;

    float distaceParamForCD = 2.0f;
    float groundDistance = 0.0f;
    State() = default;
    ~State() = default;
    bool isOutside(YVRMath::vec2 testPos);
};

BoundaryDetectorPtr BoundaryDetector::Create() { return std::make_shared<ConcreteClass<BoundaryDetector, BoundaryDetector::State>>(); }
void BoundaryDetector::AddBoundary(BoundaryWeakPtr aBoundary) { m.boundary = aBoundary; }
void BoundaryDetector::SetGroundOffSet(const float aGroundOffSet) const { m.groundDistance = aGroundOffSet; }

bool BoundaryDetector::State::isOutside(YVRMath::vec2 testPos) {
    CHECK_BOUNDARY_VALID(false);
    auto boundaryPoints = boundary.lock()->GetPoints();
    if (boundaryPoints.empty()) {
        FOO_LOG("Boundary::isOutside: boundary data is empty");
        return false;
    }
    bool isOut = true;
    int j = boundaryPoints.size() - 1;
    for (int i = 0; i < boundaryPoints.size(); i++) {
        if (((boundaryPoints[i].y < testPos.y && boundaryPoints[j].y >= testPos.y) || (boundaryPoints[j].y < testPos.y && boundaryPoints[i].y >= testPos.y)) &&
            (boundaryPoints[i].x <= testPos.x || boundaryPoints[j].x <= testPos.x)) {
            isOut ^= (boundaryPoints[i].x + (testPos.y - boundaryPoints[i].y) / (boundaryPoints[j].y - boundaryPoints[i].y) * (boundaryPoints[j].x - boundaryPoints[i].x) < testPos.x);
        }
        j = i;
    }
    return isOut;
}

int32_t BoundaryDetector::TestCollision(const PoseStateTA pointPoseState, CollisionResult& outResult, bool wordSpace) { return TestCollision(pointPoseState.pose, outResult, wordSpace); }

int32_t BoundaryDetector::TestCollision(const yvrPosef pointPose, CollisionResult& outResult, bool wordSpace) {
    auto targetBoundary = m.boundary.lock();
    if (!targetBoundary) return -1;

    outResult.boundaryId = targetBoundary->GetBoundaryId();
    yvrPosef testPos = pointPose;
    testPos.position.y -= m.groundDistance;
    int32_t ret = 0;
    // if (wordSpace) {
    //     yvrPosef recenterPose;
    //     ret = TRACKINGAPI_GetRecenterPose(&recenterPose);
    //     testPos = ~recenterPose * testPos;
    // }
    auto defaultResult = [=](YVRMath::vec3 headPos) {
        CollisionResult result;
        result.ID = -1;
        result.distance = 100.0f;
        result.closestPosition = YVRMath::vec3(0.0f, 0.0f, 0.0f);
        result.testPosition = headPos;
        result.closestPosNormal = YVRMath::vec3(0.0f, 0.0f, 0.0f);
        return result;
    };
    auto position = testPos.position;

    if (isnan(position.x) || isnan(position.y) || isnan(position.z) || isinf(position.x) || isinf(position.y) || isinf(position.z)) {
        FOO_LOG("BoundaryController::collisionDetection: test pos is nan or infinity, collisionDetection con't deal with it");
        outResult = defaultResult(position);
        return -1;
    }
    auto posForCD = targetBoundary->GetPosForCD();
    int posSize = posForCD.size();
    if (posSize <= 0) {
        FOO_LOG("BoundaryController::collisionDetection: data for CD is empty");
        outResult = defaultResult(position);
        return -1;
    }

    double distance = FLT_MAX;
    double tmp;
    int ID = INT32_MAX;
    int tmpP = 0;
    for (int whichP = 0; whichP < posSize; whichP += 5) {
        tmp = distanceOfVec2(YVRMath::vec2(position.x, position.z), posForCD[whichP]);
        if (tmp < distance) {
            ID = whichP;
            distance = tmp;
        }
    }
    for (int whichP = ID - 5; whichP <= ID + 5; whichP++) {
        if (ID == whichP) {
            continue;
        }
        if (whichP < 0) {
            tmpP = whichP + posSize;
        } else {
            tmpP = whichP;
        }
        tmp = distanceOfVec2(YVRMath::vec2(position.x, position.z), posForCD[tmpP % posSize]);
        if (tmp < distance) {
            ID = tmpP;
            distance = tmp;
        }
    }
    YVRMath::vec3 verticlePoint1;
    YVRMath::vec3 verticlePoint2;
    YVRMath::vec3 verticlePoint;
    int secondID;
    if (ID == 0) {
        verticlePoint1 = pointToLine(posForCD[posSize - 1], posForCD[0], YVRMath::vec2(position.x, position.z));
        verticlePoint2 = pointToLine(posForCD[0], posForCD[1], YVRMath::vec2(position.x, position.z));
        if (verticlePoint1.z < verticlePoint2.z) {
            verticlePoint = verticlePoint1;
            ID = posSize - 1;
            secondID = 0;
        } else {
            verticlePoint = verticlePoint2;
            secondID = 1;
        }
    } else {
        verticlePoint1 = pointToLine(posForCD[ID - 1], posForCD[ID], YVRMath::vec2(position.x, position.z));
        verticlePoint2 = pointToLine(posForCD[ID], posForCD[(ID + 1) % posSize], YVRMath::vec2(position.x, position.z));
        if (verticlePoint1.z < verticlePoint2.z) {
            verticlePoint = verticlePoint1;
            secondID = ID;
            ID = ID - 1;
        } else {
            verticlePoint = verticlePoint2;
            secondID = (ID + 1) % posSize;
        }
    }
    YVRMath::vec2 closestPoint;

    closestPoint.x = verticlePoint.x;
    closestPoint.y = verticlePoint.y;
    float resultDistance = verticlePoint.z;

    if (ID + 1 > posSize) {
        FOO_LOG("BoundaryController::collisionDetection: collision detection is out of range");
        outResult.ID = -1;
        outResult.distance = 100.0f;
        outResult.closestPosition = YVRMath::vec3(0.0f, 0.0f, 0.0f);
        outResult.testPosition = position;
        outResult.closestPosNormal = YVRMath::vec3(0.0f, 0.0f, 0.0f);
        return -1;
    }

    outResult.ID = ID;
    outResult.distance = resultDistance;  // * m.distaceParamForCD;
    outResult.closestPosition = YVRMath::vec3(closestPoint.x, position.y, closestPoint.y);
    outResult.testPosition = position;
    int num = 0;
    auto points = targetBoundary->GetPoints();
    auto boundarySegmentLengths = targetBoundary->GetSegmentLengths();
    auto lengthOfEachPosToOrigin = targetBoundary->GetLengthOfEachPosToOrigin();
    auto boundaryLineNormal = targetBoundary->GetLineNormal();

    for (int i = 0; i < points.size(); ++i) {
        if (boundarySegmentLengths[i] > lengthOfEachPosToOrigin[ID]) {
            num = i;
            break;
        }
    }
    outResult.closestPosNormal = YVRMath::vec3(boundaryLineNormal[num].x, 0.0f, boundaryLineNormal[num].y);

    if (targetBoundary->GetBoundaryType() == BOUNDAY_TYPE_LOCAL) {
        float length = YVRMath::length(YVRMath::vec2(position.x, position.z) - targetBoundary->GetCenter());
        outResult.distance = std::abs(length - targetBoundary->GetRadius());
        outResult.isOut = length > targetBoundary->GetRadius();
    } else {
        outResult.isOut = m.isOutside(YVRMath::vec2(position.x, position.z));
    }

    // FOO_LOG("kidi_test closestPosition[%f,%f,%f] testPosition[%f,%f,%f] distance:%f,isOut %d", outResult.closestPosition.x, outResult.closestPosition.y, outResult.closestPosition.z,
    //         outResult.testPosition.x, outResult.testPosition.y, outResult.testPosition.z, outResult.distance, outResult.isOut);

    return ret;
}
void BoundaryDetector::SetEffectDistance(float distance) const {}

bool BoundaryDetector::IsActive() { return m.boundary.lock() != nullptr; }

BoundaryDetector::BoundaryDetector(State& aState) : m(aState) {}
BoundaryDetector::~BoundaryDetector() {}

}  // namespace systemux
}  // namespace yvr