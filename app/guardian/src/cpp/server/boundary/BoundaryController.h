#pragma once
#include <yvrutils/MyLooper.h>
#include <memory>
#include <SpatialClientInterface.h>
#include <thread>
#include <atomic>
#include <condition_variable>
#include <chrono>
#include <optional>
#include "yvrmath/YVRMath.h"
#include "ITick.h"
#include <atomic>
#include "GuardianBoundary.h"
#include "BoundaryDefine.h"

namespace yvr {

namespace systemux {
class BoundaryController : public foo::ITick {
 public:
    static BoundaryControllerPtr Create(android::sp<yvrutils::MyLooper> looper, const android::sp<yvrutils::MessageHandler>& handler, BoundaryFactoryPtr factory);
    int CreateLocalBoundary(const float radius);
    void SetBoundaryUpdateEnable(bool enable);
    void CreateGameBoundary(std::vector<float> points);

 protected:
    BoundaryController();
    ~BoundaryController();

 private:
    android::sp<yvrutils::MyLooper> m_Looper;
    android::sp<yvrutils::MessageHandler> m_Handler;
    std::thread* m_DetectorThread;
    int m_DetectionInterval;
    std::vector<::yvr::spatial::Uuid_t> m_BoundaryAnchorUuids;
    BoundaryFactoryPtr m_BoundaryFactory;

    std::condition_variable m_BoundaryUpdateEnableChangedCV;
    float m_GroundDistance;

 private:
    void onTick() override;

 private:
    void Init();

    //  void BoundaryDetectionLoop();
    //  void StopDetector();
    void onSpatialEvent(const ::yvr::spatial::Event_t& event);
};

}  // namespace systemux
}  // namespace yvr
