#pragma once
#include <memory>

namespace yvr {
namespace systemux {
class BoundaryController;
typedef std::shared_ptr<BoundaryController> BoundaryControllerPtr;

class Boundary;
using BoundaryPtr = std::shared_ptr<Boundary>;
using BoundaryWeakPtr = std::weak_ptr<Boundary>;

class BoundaryFactory;
using BoundaryFactoryPtr = std::shared_ptr<BoundaryFactory>;
using BoundaryFactoryWeakPtr = std::weak_ptr<BoundaryFactory>;

class BoundaryDetector;
using BoundaryDetectorPtr = std::shared_ptr<BoundaryDetector>;
using BoundaryDetectorWeakPtr = std::weak_ptr<BoundaryDetector>;

class BoundaryController;
using BoundaryControllerPtr = std::shared_ptr<BoundaryController>;
using BoundaryControllerWeakPtr = std::weak_ptr<BoundaryController>;
}  // namespace systemux
}  // namespace yvr