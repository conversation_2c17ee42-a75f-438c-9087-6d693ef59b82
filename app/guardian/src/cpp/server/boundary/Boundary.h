#pragma once
#include <foo/MacroUtils.h>
#include "BoundaryDefine.h"
#include "yvrmath/YVRMath.h"
#include <vector>
#include "BoundaryCommon.h"
#include "GuardianBoundary.h"

namespace yvr {

namespace systemux {

class Boundary {
 public:
    static BoundaryPtr Create(std::vector<YVRMath::vec2> points, const YVRMath::vec2 center, const uint32_t boundaryId, const int boundaryType, const float radius);
    std::vector<YVRMath::vec2> const GetPosForCD() const;

    std::vector<YVRMath::vec2> const GetPoints() const;
    std::vector<float> const GetSegmentLengths() const;
    std::vector<float> const GetLengthOfEachPosToOrigin() const;
    std::vector<YVRMath::vec2> const GetLineNormal() const;
    uint32_t const GetBoundaryId() const;
    int const GetBoundaryType() const;
    foo::BoundaryMesh const GetMesh() const;
    foo::BoundaryMesh const GetGroundMesh() const;
    foo::BoundaryMesh const GetSphereMesh() const;
    float const GetRadius() const;
    YVRMath::vec2 const GetCenter() const;

 protected:
    struct State;
    Boundary(State& aState);
    ~Boundary();

 private:
    State& m;
    Boundary() = delete;
    FOO_NO_DEFAULTS(Boundary)
};

}  // namespace systemux
}  // namespace yvr