#pragma once
#include "yvrmath/YVRMath.h"

const int BOUNDAY_TYPE_LOCAL = 0;
const int BOUNDAY_TYPE_GAME = 1;
struct CollisionResult {
    int ID = 0;
    uint64_t boundaryId;
    float distance;
    YVRMath::vec3 closestPosition;
    YVRMath::vec3 testPosition;
    YVRMath::vec3 closestPosNormal;
    bool isOut;
    bool enable;
};
struct CDForRender {
    YVRMath::vec2 uv;
    float distance;
};

struct AvatorSpeed {
    float headSpeed;
    float leftSpeed;
    float rightSpeed;
};

struct BoundingBox {
    float minX;
    float minY;
    float maxX;
    float maxY;
    float width;
    float height;
};
