#include "Boundary.h"
#include "foo/Logger.h"
#include "GuardianBoundary.h"
#include <foo/ConcreteClass.h>

namespace yvr {

namespace systemux {

const float TESSELLATION_FOR_BOUNDARY_DATA = 0.05f;
const float WIDTH_OF_GROUND_MESH = 0.0375f;
const float HEIGHT_OF_BOUNDARY = 3.0f;

struct Boundary::State {
    std::vector<YVRMath::vec2> posForCD;
    std::vector<YVRMath::vec2> points;
    std::vector<float> segmentLengths;
    std::vector<float> lengthOfEachPosToOrigins;
    std::vector<YVRMath::vec2> pointNormals;
    std::vector<YVRMath::vec2> edgeNormals;
    BoundingBox boundingBox;
    float lengthOfboundary;
    uint32_t id;
    int type;
    foo::BoundaryMesh mesh;
    foo::BoundaryMesh groundMesh;
    foo::BoundaryMesh sphereMesh;
    float radius;

    YVRMath::vec2 center;
    State() : center(YVRMath::vec2(0.0f, 0.0f)), radius(1.5f) {}

    void Init();
    void GenerateBoundaryMesh();
    void GenerateShereMesh();
    void GeneratePoselengths();
    void GenerateLineNormal();
    void GenerateNormal();
    void GenerateBoundingBox();
    void GenerateBoundingAndGroundCircleMesh();
    void GenerateGroundMesh();
    void computeVectorNormal(YVRMath::vec2& a, YVRMath::vec2& b, YVRMath::vec2& normal);
    void GenerateSegemntLengths();
    YVRMath::vec2 getUVFromBoundingBox(YVRMath::vec2 tmpPos);

    ~State() {}
};

void Boundary::State::Init() {
    GenerateSegemntLengths();
    GeneratePoselengths();
    GenerateLineNormal();
    GenerateNormal();
    GenerateBoundingBox();
    GenerateBoundaryMesh();
    if (type == BOUNDAY_TYPE_LOCAL) {
        GenerateShereMesh();
    }
    GenerateGroundMesh();
    GenerateBoundingAndGroundCircleMesh();
}

void Boundary::State::GenerateBoundaryMesh() {
    unsigned int numRows = 1;
    unsigned int numCols = points.size();
    unsigned int numVerts = (numCols + 1) * (numRows + 1);
    // position
    std::vector<YVRMath::vec3> verts;
    // UV
    std::vector<YVRMath::vec2> uvs;

    float vpart = 1.0f / numRows;
    float x, y, z, u, v;
    for (unsigned int i = 0; i <= numCols; ++i) {
        x = points[i % numCols].x;
        z = points[i % numCols].y;
        u = segmentLengths[i] / lengthOfboundary;
        y = 0.0f;
        for (unsigned int row = 0; row <= numRows; ++row) {
            v = row * vpart;
            verts.emplace_back(YVRMath::vec3(x, y, z));
            uvs.emplace_back(YVRMath::vec2(u, v));
            y += HEIGHT_OF_BOUNDARY;
        }
    }

    unsigned int maxIndices = numRows * numCols * 2 * 3;
    // indicate
    std::vector<uint32_t> indices;

    unsigned int numIndices = 0;

    unsigned int currentIndex = 0;

    for (int whichY = numRows - 1; whichY >= 0; whichY--) {
        for (unsigned int whichX = 0; whichX < numCols; whichX++) {
            // What index is the bottom left corner of this quad
            currentIndex = whichX * (numRows + 1) + whichY + 1;  // Columns
            int a = currentIndex;
            int b = currentIndex + 1;
            int c = currentIndex + numRows + 1;
            indices.emplace_back(a);
            indices.emplace_back(b);
            indices.emplace_back(c);

            a = currentIndex + 1;
            b = currentIndex + 1 + numRows + 1;
            c = currentIndex + numRows + 1;
            indices.emplace_back(a);
            indices.emplace_back(b);
            indices.emplace_back(c);
            numIndices += 6;

        }  // Each Column
    }  // Each Row

    FOO_LOG("boundary mesh numVerts = %d, numIndices = %d,%d", verts.size(), numIndices, indices.size());
    mesh.vertices.clear();
    std::copy(verts.begin(), verts.end(), std::back_inserter(mesh.vertices));
    mesh.texCoords.clear();
    std::copy(uvs.begin(), uvs.end(), std::back_inserter(mesh.texCoords));
    mesh.indices.clear();
    std::copy(indices.begin(), indices.end(), std::back_inserter(mesh.indices));
}

void Boundary::State::GenerateShereMesh() {
    float radiuss = 0.3;
    int sectorCount = 18;  // longitude, # of slices
    int stackCount = 18;   // latitude, # of stacks
    // bool smooth;
    // int upAxis;                             // +X=1, +Y=2, +z=3 (default)
    std::vector<YVRMath::vec3> vertices;
    // std::vector<float> normals;
    std::vector<YVRMath::vec2> texCoords;
    std::vector<uint32_t> indices;

    const float PI = acos(-1.0f);

    // tmp vertex definition (x,y,z,s,t)
    struct Vertex {
        float x, y, z, s, t;
    };
    std::vector<Vertex> tmpVertices;

    float sectorStep = 2 * PI / sectorCount;
    float stackStep = PI / stackCount;
    float sectorAngle, stackAngle;

    // compute all vertices first, each vertex contains (x,y,z,s,t) except normal
    for (int i = 0; i <= stackCount; ++i) {
        stackAngle = PI / 2 - i * stackStep;    // starting from pi/2 to -pi/2
        float xy = radiuss * cosf(stackAngle);  // r * cos(u)
        float z = radiuss * sinf(stackAngle);   // r * sin(u)

        // add (sectorCount+1) vertices per stack
        // the first and last vertices have same position and normal, but different tex coords
        for (int j = 0; j <= sectorCount; ++j) {
            sectorAngle = j * sectorStep;  // starting from 0 to 2pi

            Vertex vertex;
            vertex.x = xy * cosf(sectorAngle);  // x = r * cos(u) * cos(v)
            vertex.y = xy * sinf(sectorAngle);  // y = r * cos(u) * sin(v)
            vertex.z = z;                       // z = r * sin(u)
            vertex.s = (float)j / sectorCount;  // s
            vertex.t = (float)i / stackCount;   // t
            tmpVertices.push_back(vertex);
        }
    }

    Vertex v1, v2, v3, v4;  // 4 vertex positions and tex coords
    std::vector<float> n;   // 1 face normal

    int i, j, k, vi1, vi2;
    int index = 1;  // index for vertex
    for (i = 0; i < stackCount; ++i) {
        vi1 = i * (sectorCount + 1);  // index of tmpVertices
        vi2 = (i + 1) * (sectorCount + 1);

        for (j = 0; j < sectorCount; ++j, ++vi1, ++vi2) {
            // get 4 vertices per sector
            //  v1--v3
            //  |    |
            //  v2--v4
            v1 = tmpVertices[vi1];
            v2 = tmpVertices[vi2];
            v3 = tmpVertices[vi1 + 1];
            v4 = tmpVertices[vi2 + 1];

            // if 1st stack and last stack, store only 1 triangle per sector
            // otherwise, store 2 triangles (quad) per sector
            if (i == 0)  // a triangle for first stack ==========================
            {
                // put a triangle
                vertices.push_back(YVRMath::vec3(v1.x, v1.y, v1.z));
                vertices.push_back(YVRMath::vec3(v2.x, v2.y, v2.z));
                vertices.push_back(YVRMath::vec3(v4.x, v4.y, v4.z));
                // addVertex(v1.x, v1.y, v1.z);
                // addVertex(v2.x, v2.y, v2.z);
                // addVertex(v4.x, v4.y, v4.z);

                // put tex coords of triangle
                texCoords.push_back(YVRMath::vec2(v1.s, v1.t));
                texCoords.push_back(YVRMath::vec2(v2.s, v2.t));
                texCoords.push_back(YVRMath::vec2(v4.s, v4.t));
                // addTexCoord(v1.s, v1.t);
                // addTexCoord(v2.s, v2.t);
                // addTexCoord(v4.s, v4.t);

                // put normal
                // n = computeFaceNormal(v1.x,v1.y,v1.z, v2.x,v2.y,v2.z, v4.x,v4.y,v4.z);
                // for(k = 0; k < 3; ++k)  // same normals for 3 vertices
                // {
                //     addNormal(n[0], n[1], n[2]);
                // }

                // put indices of 1 triangle
                // addIndices(index, index+1, index+2);
                indices.push_back(index);
                indices.push_back(index + 1);
                indices.push_back(index + 2);

                index += 3;                    // for next
            } else if (i == (stackCount - 1))  // a triangle for last stack =========
            {
                // // put a triangle
                vertices.push_back(YVRMath::vec3(v1.x, v1.y, v1.z));
                vertices.push_back(YVRMath::vec3(v2.x, v2.y, v2.z));
                vertices.push_back(YVRMath::vec3(v3.x, v3.y, v3.z));
                // addVertex(v1.x, v1.y, v1.z);
                // addVertex(v2.x, v2.y, v2.z);
                // addVertex(v3.x, v3.y, v3.z);

                // put tex coords of triangle
                texCoords.push_back(YVRMath::vec2(v1.s, v1.t));
                texCoords.push_back(YVRMath::vec2(v2.s, v2.t));
                texCoords.push_back(YVRMath::vec2(v3.s, v3.t));
                // addTexCoord(v1.s, v1.t);
                // addTexCoord(v2.s, v2.t);
                // addTexCoord(v3.s, v3.t);

                // put normal
                // n = computeFaceNormal(v1.x,v1.y,v1.z, v2.x,v2.y,v2.z, v3.x,v3.y,v3.z);
                // for(k = 0; k < 3; ++k)  // same normals for 3 vertices
                // {
                //     addNormal(n[0], n[1], n[2]);
                // }

                // put indices of 1 triangle
                // addIndices(index, index+1, index+2);
                indices.push_back(index);
                indices.push_back(index + 1);
                indices.push_back(index + 2);

                index += 3;  // for next
            } else           // 2 triangles for others ====================================
            {
                // put quad vertices: v1-v2-v3-v4
                vertices.push_back(YVRMath::vec3(v1.x, v1.y, v1.z));
                vertices.push_back(YVRMath::vec3(v2.x, v2.y, v2.z));
                vertices.push_back(YVRMath::vec3(v3.x, v3.y, v3.z));
                vertices.push_back(YVRMath::vec3(v4.x, v4.y, v4.z));
                // addVertex(v1.x, v1.y, v1.z);
                // addVertex(v2.x, v2.y, v2.z);
                // addVertex(v3.x, v3.y, v3.z);
                // addVertex(v4.x, v4.y, v4.z);

                // put tex coords of quad
                texCoords.push_back(YVRMath::vec2(v1.s, v1.t));
                texCoords.push_back(YVRMath::vec2(v2.s, v2.t));
                texCoords.push_back(YVRMath::vec2(v3.s, v3.t));
                texCoords.push_back(YVRMath::vec2(v4.s, v4.t));
                // addTexCoord(v1.s, v1.t);
                // addTexCoord(v2.s, v2.t);
                // addTexCoord(v3.s, v3.t);
                // addTexCoord(v4.s, v4.t);

                // put normal
                // n = computeFaceNormal(v1.x,v1.y,v1.z, v2.x,v2.y,v2.z, v3.x,v3.y,v3.z);
                // for(k = 0; k < 4; ++k)  // same normals for 4 vertices
                // {
                //     addNormal(n[0], n[1], n[2]);
                // }

                // put indices of quad (2 triangles)
                // addIndices(index, index+1, index+2);
                // addIndices(index+2, index+1, index+3);

                indices.push_back(index);
                indices.push_back(index + 1);
                indices.push_back(index + 2);

                indices.push_back(index + 2);
                indices.push_back(index + 1);
                indices.push_back(index + 3);

                index += 4;  // for next
            }
        }
    }

    // generate interleaved vertex array as well
    // buildInterleavedVertices();

    // change up axis from Z-axis to the given
    // if(this->upAxis != 3)
    //     changeUpAxis(3, this->upAxis);

    sphereMesh.vertices.clear();
    std::copy(vertices.begin(), vertices.end(), std::back_inserter(sphereMesh.vertices));
    sphereMesh.texCoords.clear();
    std::copy(texCoords.begin(), texCoords.end(), std::back_inserter(sphereMesh.texCoords));
    sphereMesh.indices.clear();
    std::copy(indices.begin(), indices.end(), std::back_inserter(sphereMesh.indices));
    // sphereMesh.normals.clear();
}

YVRMath::vec2 Boundary::State::getUVFromBoundingBox(YVRMath::vec2 tmpPos) {
    YVRMath::vec2 uv;
    uv.x = (tmpPos.x - boundingBox.minX) / boundingBox.width;
    uv.y = (tmpPos.y - boundingBox.minY) / boundingBox.height;
    return uv;
}

void Boundary::State::GenerateGroundMesh() {
    std::vector<YVRMath::vec2> insidePosArray;
    std::vector<YVRMath::vec2> outsidePosArray;
    YVRMath::vec2 insidePos;
    YVRMath::vec2 outsidePos;
    YVRMath::vec2 originPos;

    YVRMath::vec2 pointNormal;
    YVRMath::vec2 lineNormal;
    int pointSize = points.size();

    for (int i = 0; i < pointSize; ++i) {
        originPos = points[i];
        pointNormal = pointNormals[i];
        lineNormal = edgeNormals[i];
        float dis = WIDTH_OF_GROUND_MESH / (YVRMath::dot(pointNormal, lineNormal));
        insidePos.x = originPos.x - dis * pointNormal.x * 0.5f;
        insidePos.y = originPos.y - dis * pointNormal.y * 0.5f;
        outsidePos.x = originPos.x + dis * pointNormal.x * 0.5f;
        outsidePos.y = originPos.y + dis * pointNormal.y * 0.5f;

        insidePosArray.emplace_back(insidePos);
        outsidePosArray.emplace_back(outsidePos);
    }
    float ground = 0.0f;
    std::vector<YVRMath::vec3> groundVertices;
    std::vector<YVRMath::vec2> groundUVs;
    for (int i = 0; i <= pointSize; ++i) {
        groundVertices.emplace_back(YVRMath::vec3(insidePosArray[i % pointSize].x, ground + 0.01f, insidePosArray[i % pointSize].y));
        groundUVs.emplace_back(getUVFromBoundingBox(insidePosArray[i % pointSize]));
        groundVertices.emplace_back(YVRMath::vec3(outsidePosArray[i % pointSize].x, ground + 0.01f, outsidePosArray[i % pointSize].y));
        groundUVs.emplace_back(getUVFromBoundingBox(outsidePosArray[i % pointSize]));
    }

    // indicate
    int numRows = 1;
    int numCols = pointSize;
    int maxIndices = numRows * numCols * 2 * 3;
    std::vector<uint32_t> indices;
    int currentIndex;
    int numIndices = 0;
    for (int whichY = numRows - 1; whichY >= 0; whichY--) {
        for (unsigned int whichX = 0; whichX < numCols; whichX++) {
            currentIndex = whichX * (numRows + 1) + whichY + 1;  // Columns
            int a = currentIndex;
            int b = currentIndex + 1;
            int c = currentIndex + numRows + 1;
            indices.emplace_back(a);
            indices.emplace_back(b);
            indices.emplace_back(c);

            a = currentIndex + 1;
            b = currentIndex + 1 + numRows + 1;
            c = currentIndex + numRows + 1;
            indices.emplace_back(a);
            indices.emplace_back(b);
            indices.emplace_back(c);
            numIndices += 6;
        }
    }
    FOO_LOG("ground mesh numVerts = %d, numIndices = %d,%d", groundVertices.size(), numIndices, indices.size());

    groundMesh.vertices.clear();
    std::copy(groundVertices.begin(), groundVertices.end(), std::back_inserter(groundMesh.vertices));
    groundMesh.texCoords.clear();
    std::copy(groundUVs.begin(), groundUVs.end(), std::back_inserter(groundMesh.texCoords));
    groundMesh.indices.clear();
    std::copy(indices.begin(), indices.end(), std::back_inserter(groundMesh.indices));
}
void Boundary::State::GenerateSegemntLengths() {
    auto numCols = points.size();
    segmentLengths.resize(numCols + 1);
    // segmentLengths.clear();
    segmentLengths[0] = 0.0f;
    lengthOfboundary = 0.0f;
    for (size_t i = 0; i < numCols; i++) {
        lengthOfboundary += YVRMath::distance(points[i], points[(i + 1) % numCols]);
        segmentLengths[i + 1] = lengthOfboundary;
    }
}

void Boundary::State::computeVectorNormal(YVRMath::vec2& a, YVRMath::vec2& b, YVRMath::vec2& normal) {
    YVRMath::vec2 vectorBA = b - a;
    YVRMath::vec2 result = YVRMath::vec2(-vectorBA.y, vectorBA.x);
    normal = YVRMath::normalize(result);
}
void Boundary::State::GeneratePoselengths() {
    posForCD.clear();
    lengthOfEachPosToOrigins.clear();
    YVRMath::vec2 startPos;
    YVRMath::vec2 endPos;
    float dis;
    float partOfDis;
    float numOfPart;
    YVRMath::vec2 tmpPos;
    float rate;
    float wholeDistance = 0.0f;

    int vsize = points.size();
    for (int i = 0; i < vsize; ++i) {
        startPos = points[i];
        endPos = points[(i + 1) % vsize];
        dis = YVRMath::distance(startPos, endPos);
        numOfPart = ceilf(dis / TESSELLATION_FOR_BOUNDARY_DATA);
        partOfDis = dis / numOfPart;
        for (int j = 0; j < numOfPart; ++j) {
            rate = static_cast<float>(j) / numOfPart;
            tmpPos.x = startPos.x + (endPos.x - startPos.x) * rate;
            tmpPos.y = startPos.y + (endPos.y - startPos.y) * rate;
            posForCD.emplace_back(tmpPos);
            wholeDistance += partOfDis;
            lengthOfEachPosToOrigins.emplace_back(wholeDistance);
        }
    }
}
void Boundary::State::GenerateLineNormal() {
    YVRMath::vec2 normal;
    YVRMath::vec2 a;
    YVRMath::vec2 b;
    int vsize = points.size();
    // edgeNormals.clear();
    edgeNormals.reserve(vsize);
    for (int i = 0; i < vsize; ++i) {
        a = points[i];
        b = points[(i + 1) % vsize];
        computeVectorNormal(a, b, normal);
        edgeNormals.emplace_back(normal);
    }
}
void Boundary::State::GenerateNormal() {
    YVRMath::vec2 normal;
    YVRMath::vec2 a;
    YVRMath::vec2 b;
    int vsize = edgeNormals.size();
    pointNormals.reserve(vsize);
    // pointNormals.clear();
    a = edgeNormals[vsize - 1];
    b = edgeNormals[0];
    normal = YVRMath::normalize(a + b);
    pointNormals.emplace_back(normal);
    // pointNormals[0] = normal;
    for (int i = 0; i < vsize - 1; ++i) {
        a = edgeNormals[i];
        b = edgeNormals[i + 1];
        normal = YVRMath::normalize(a + b);
        pointNormals.emplace_back(normal);
    }
}
void Boundary::State::GenerateBoundingBox() {
    float minX = FLT_MAX;
    float minY = FLT_MAX;
    float maxX = -FLT_MAX;
    float maxY = -FLT_MAX;
    for (int i = 0; i < points.size(); ++i) {
        if (points[i].x > maxX) {
            maxX = points[i].x;
        }

        if (points[i].x < minX) {
            minX = points[i].x;
        }

        if (points[i].y > maxY) {
            maxY = points[i].y;
        }

        if (points[i].y < minY) {
            minY = points[i].y;
        }
    }

    minX -= WIDTH_OF_GROUND_MESH * 0.5f;
    minY -= WIDTH_OF_GROUND_MESH * 0.5f;
    maxX += WIDTH_OF_GROUND_MESH * 0.5f;
    maxY += WIDTH_OF_GROUND_MESH * 0.5f;

    boundingBox.minX = minX - WIDTH_OF_GROUND_MESH * 0.5f;
    boundingBox.minY = minY - WIDTH_OF_GROUND_MESH * 0.5f;
    boundingBox.maxX = maxX + WIDTH_OF_GROUND_MESH * 0.5f;
    boundingBox.maxY = maxY + WIDTH_OF_GROUND_MESH * 0.5f;
    boundingBox.width = maxX - minX + WIDTH_OF_GROUND_MESH;
    boundingBox.height = maxY - minY + WIDTH_OF_GROUND_MESH;
    FOO_LOG("ResetdBoundingBox done! (%f, %f, %f, %f)", boundingBox.minX, boundingBox.minY, boundingBox.maxX, boundingBox.maxY);
}
void Boundary::State::GenerateBoundingAndGroundCircleMesh() {}
BoundaryPtr Boundary::Create(std::vector<YVRMath::vec2> points, const YVRMath::vec2 center, const uint32_t boundaryId, const int boundaryType, const float radius) {
    BoundaryPtr result = std::make_shared<foo::ConcreteClass<Boundary, Boundary::State>>();
    result->m.points.swap(points);
    result->m.id = boundaryId;
    result->m.type = boundaryType;
    result->m.center = center;
    result->m.radius = radius;
    result->m.Init();
    return result;
}
float const Boundary::GetRadius() const { return m.radius; }
foo::BoundaryMesh const Boundary::GetMesh() const { return m.mesh; }
foo::BoundaryMesh const Boundary::GetGroundMesh() const { return m.groundMesh; }
foo::BoundaryMesh const Boundary::GetSphereMesh() const { return m.sphereMesh; }

YVRMath::vec2 const Boundary::GetCenter() const { return m.center; }
uint32_t const Boundary::GetBoundaryId() const { return m.id; }
int const Boundary::GetBoundaryType() const { return m.type; }
std::vector<YVRMath::vec2> const Boundary::GetPosForCD() const { return m.posForCD; }
std::vector<YVRMath::vec2> const Boundary::GetPoints() const { return m.points; }
std::vector<float> const Boundary::GetSegmentLengths() const { return m.segmentLengths; }
std::vector<float> const Boundary::GetLengthOfEachPosToOrigin() const { return m.lengthOfEachPosToOrigins; }
std::vector<YVRMath::vec2> const Boundary::GetLineNormal() const { return m.edgeNormals; }

Boundary::Boundary(State& aState) : m(aState) {}
Boundary::~Boundary() {}

};  // namespace systemux
}  // namespace yvr