#pragma once

#include "yvrutils/MyLooper.h"
#include "yvrutils/HandlerThread.h"
#include <mutex>

#include "yvrutils/extModuleLoader.h"
#include "IWearingDetectionRequest.h"
#include "IWearingDetectionStatusChange.h"
#include "trackingapi/tracking_types.h"

using android::sp;
using android::yvr::extModuleLoader;
using yvrutils::HandlerThread;
using yvrutils::Message;
using yvrutils::MessageHandler;
using yvrutils::MyLooper;

namespace yvr {

namespace systemux {
struct WearingDetectionResult {
    WEARING_DETECTION_CMD_ACTION cmd;
    int interAction;
};
class DetectionHandler : public MessageHandler, public IWearingDetectionRequest {
 public:
    DetectionHandler();
    ~DetectionHandler();
    void sendMessage(const Message& message);

    void sendMessageDelayed(nsecs_t uptimeDelay, const Message& message);

    void sendMessageAtTime(nsecs_t uptime, const Message& message);

    void removeMessages();

    void removeMessages(int what);

    inline void SetWearingDetectionStatusCallback(std::shared_ptr<IWearingDetectionStatusChange> callback) { m_WearingDetectionStatus = callback; }
    inline IWearingDetectionRequest& WearingDetectionRequest() { return *this; }

 private:
    sp<HandlerThread> m_DetectionThread;
    bool m_IsInited;
    bool m_IsAutoIPD;
    int m_AutoIPDFailCount;
    int m_positionCheckFailCount;
    std::weak_ptr<IWearingDetectionStatusChange> m_WearingDetectionStatus;
    std::shared_ptr<extModuleLoader> m_WearingDetectionModulePtr;

 private:
    void handleMessage(const Message& message) override;
    void loadWearingDetectionModule();
    void changeWearingDetectionModuleState(WearingDetectionModuleState state);
    void handleWearingDetectionCallback(WearingDetectionResult result);

 private:
    void MoveIpd(const int step) override;
    void LoadETCalibrationData() override;
    void SweepForPositionGuide() override;
    void StageAction(WEARING_DETECTION_CMD_ACTION action) override;
    void ForceStop() override;
};
}  // namespace systemux
}  // namespace yvr