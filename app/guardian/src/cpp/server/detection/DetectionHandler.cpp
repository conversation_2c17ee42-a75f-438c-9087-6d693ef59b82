#include "DetectionHandler.h"
#include "extModuleWearingDetection.h"
#include "foo/Logger.h"

#define EXT_MODULE_WEARING_DETECTION_LIB_PATH "libwearingdetection.so"

#define CHECK_FAIL_COUNT_LIMIT 3

namespace yvr {
namespace systemux {

android::wp<DetectionHandler> self;

void sendCallBackMsg(WearingDetectionResult result) {
    auto weakDetectionHandler = self.promote();

    if (weakDetectionHandler) {
        yvrutils::Message msg;
        msg.what = DetectionThreadMessage::DT_MSG_WEARING_DETECTION_CALLBACK_HANDLE;
        msg.data = malloc(sizeof(WearingDetectionResult));
        if (msg.data == nullptr) {
            FOO_ERROR("malloc failed");
            return;
        }
        memcpy(msg.data, &result, sizeof(WearingDetectionResult));
        weakDetectionHandler->sendMessage(msg);
    } else {
        FOO_ERROR("WearingDetection module  not init");
    }
}

void positionYCallback(int inter_action) {
    FOO_LOG("positionYCallback %d", inter_action);
    WearingDetectionResult result{.cmd = WEARING_DETECTION_CMD_ACTION::POSITION_Y_JUDGEMENT, .interAction = inter_action};
    sendCallBackMsg(result);
}

void positionXCallback(int inter_action) {
    FOO_LOG("positionXCallback %d", inter_action);
    WearingDetectionResult result{.cmd = WEARING_DETECTION_CMD_ACTION::POSITION_X_JUDGEMENT, .interAction = inter_action};
    sendCallBackMsg(result);
}

void positionYXCallback(int inter_action) {
    FOO_LOG("positionYXCallback %d", inter_action);
    WearingDetectionResult result{.cmd = WEARING_DETECTION_CMD_ACTION::POSITION_Y_X_JUDGEMENT, .interAction = inter_action};
    sendCallBackMsg(result);
}

void ipdAdjustmentCallback(int inter_action) {
    FOO_LOG("ipdAdjustmentCallback %d", inter_action);
    WearingDetectionResult result{.cmd = WEARING_DETECTION_CMD_ACTION::IPD_ADJUSTMENT, .interAction = inter_action};
    sendCallBackMsg(result);
}

void diopterLensCheckCallback(int inter_action) {
    FOO_LOG("diopterLensCheckCallback %d", inter_action);
    WearingDetectionResult result{.cmd = WEARING_DETECTION_CMD_ACTION::DIOPTER_LENS_CHECK, .interAction = inter_action};
    sendCallBackMsg(result);
}

DetectionHandler::DetectionHandler() : m_IsInited(false), m_IsAutoIPD(false), m_AutoIPDFailCount(0), m_positionCheckFailCount(0) {
    m_DetectionThread = new HandlerThread();
    m_DetectionThread->start("SystemUX::DetectionThread");
    m_WearingDetectionModulePtr = std::make_shared<extModuleLoader>();
}

void DetectionHandler::handleMessage(const Message& message) {
    switch (message.what) {
        case DT_MSG_INIT: {
            m_IsInited = true;
            loadWearingDetectionModule();
        } break;

        case DT_MSG_WEARING_DETECTION_MODULE_STATE_CHANGE: {
            if (!m_WearingDetectionModulePtr->isExtModuleReady()) {
                FOO_ERROR("WearingDetectionModule not loaded");
                return;
            }
            changeWearingDetectionModuleState(static_cast<WearingDetectionModuleState>(message.arg1));
        } break;

        case DT_MSG_WEARING_DETECTION_CALLBACK_HANDLE: {
            WearingDetectionResult result = *((WearingDetectionResult*)message.data);
            handleWearingDetectionCallback(result);
            free(message.data);
        } break;
        case DT_MSG_WEARING_DETECTION_TAKE_PICTURE: {
            Cmd_Param param;
            Cmd_Param output;

            EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(3, param, output);
            FOO_LOG("moduleControl  take picture return + %d", ret);
            break;
        }

        default:
            break;
    }
}
void DetectionHandler::loadWearingDetectionModule() {
    FOO_LOG("Try to load WearingDetectionModule in %s.\n", EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    bool isLoaded = false;
    m_WearingDetectionModulePtr->loadExtModule(EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    if (m_WearingDetectionModulePtr->isExtModuleReady()) {
        isLoaded = true;
    }

    if (isLoaded) {
        FOO_LOG("WearingDetectionModule load successfully");
        std::string moduleName;
        m_WearingDetectionModulePtr->getModuleName(moduleName);
        FOO_LOG("WearingDetectionModul Name: %s\n", moduleName.c_str());

        yvrutils::Message initMsg;
        initMsg.what = DetectionThreadMessage::DT_MSG_WEARING_DETECTION_MODULE_STATE_CHANGE;
        initMsg.arg1 = WearingDetectionModuleState::INIT;
        sendMessage(initMsg);
    } else {
        FOO_ERROR("Fail to load WearingDetectionModul.");
    }
}
void DetectionHandler::changeWearingDetectionModuleState(WearingDetectionModuleState state) {
    int ret = EXT_MODULE_ERROR_FAILED;

    switch (state) {
        case WearingDetectionModuleState::INIT: {
            WD_Module_Init_Input_Param init_input_param;
            WD_Module_Init_Output_Param init_output_param;
            ret = m_WearingDetectionModulePtr->initModule((void*)&init_input_param, (void*)&init_output_param);
            if (EXT_MODULE_ERROR_NONE == ret || EXT_MODULE_ERROR_ALREADY_DONE == ret) {
                self = this;
                FOO_LOG("WearingDetectionModul Init successfully");
            } else {
                FOO_ERROR("WearingDetectionModul Init fail");
            }
        } break;
        case WearingDetectionModuleState::START: {
            // WD_Module_Start_Input_Param start_input_param;
            // WD_Module_Start_Output_Param start_output_param;
            //
            // start_input_param.new_thread = true;
            // start_input_param.ia_cb = ia_callback;
            // start_input_param.start_stage = WEARING_DETECTION_STAGE_START;
            // ret = m_WearingDetectionModulePtr->startModule((void*)&start_input_param, (void*)&start_output_param);
            // if (EXT_MODULE_ERROR_NONE == ret) {
            //     FOO_LOG("WearingDetectionModul start successfully");
            // } else
            //     YLOGE("WearingDetectionModul start fail %d", ret);

        } break;
        case WearingDetectionModuleState::PAUSE: {
            m_WearingDetectionModulePtr->pauseModule();
            FOO_LOG("WearingDetectionModul pause");
        } break;
        case WearingDetectionModuleState::RESUME: {
            m_WearingDetectionModulePtr->resumeModule();
            FOO_LOG("WearingDetectionModul resume");
        } break;
        case WearingDetectionModuleState::STOP: {
            m_WearingDetectionModulePtr->stopModule();
            FOO_LOG("WearingDetectionModul stop");
        } break;
        case WearingDetectionModuleState::DEINIT: {
            m_WearingDetectionModulePtr->deinitModule();
            FOO_LOG("WearingDetectionModul deinit");
        } break;
    }
}
void DetectionHandler::handleWearingDetectionCallback(WearingDetectionResult result) {
    FOO_LOG("WearingDetectionCallback: %d, %d", result.cmd, result.interAction);
    auto statusChageListener = m_WearingDetectionStatus.lock();

    switch (result.cmd) {
        case WEARING_DETECTION_CMD_ACTION::POSITION_Y_JUDGEMENT: {
            switch (result.interAction) {
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKING: {
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_DOWNWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::Y_DOWNWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_UPWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::Y_UPWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::Y_DETECTION_SUCCEED);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FORCE_STOP:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FORCE_STOP);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FINISHED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_DONE);
                    }
                } break;
            }
        } break;
        case WEARING_DETECTION_CMD_ACTION::POSITION_X_JUDGEMENT: {
            switch (result.interAction) {
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING: {
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_OUTWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_OUTWARD_ADJUSTMENT);
                        // statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_WAITING_AUTOMATIC_ADJUSTMENT);
                    }
                }
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_INWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_INWARD_ADJUSTMENT);
                        // statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_WAITING_AUTOMATIC_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_LEFTWARDS:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_RIGHTWARDS:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_DETECTION_SUCCEED);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FORCE_STOP:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FORCE_STOP);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FINISHED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_DONE);
                    }
                } break;
            }
        } break;
        case WEARING_DETECTION_CMD_ACTION::POSITION_Y_X_JUDGEMENT: {
            switch (result.interAction) {
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKING: {
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_DOWNWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::Y_DOWNWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_UPWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::Y_UPWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_WAITING_DETECTION);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_Y_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING: {
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_OUTWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_OUTWARD_ADJUSTMENT);
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_WAITING_AUTOMATIC_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_INWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_INWARD_ADJUSTMENT);
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_WAITING_AUTOMATIC_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_LEFTWARDS:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKING_RIGHTWARDS:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_DETECTION_SUCCEED);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_POSITION_X_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FORCE_STOP:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FORCE_STOP);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FINISHED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_DONE);
                    }
                } break;
            }
        } break;

        case WEARING_DETECTION_CMD_ACTION::IPD_ADJUSTMENT: {
            switch (result.interAction) {
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKING: {
                } break;
                /* case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_OUTWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_OUTWARD_ADJUSTMENT);
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_AUTOMATIC_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_INWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::X_INWARD_ADJUSTMENT);
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_AUTOMATIC_ADJUSTMENT);
                    }
                } break; */
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_OUTWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_OUTWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_INWARDS: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_INWARD_ADJUSTMENT);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::IPD_DETECTION_SUCCEED);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_IPD_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FORCE_STOP:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FORCE_STOP);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FINISHED: {
                    if (m_IsAutoIPD) {
                    }
                    m_AutoIPDFailCount = 0;
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_DONE);
                    }
                } break;
            }
        } break;

        case WEARING_DETECTION_CMD_ACTION::DIOPTER_LENS_CHECK: {
            switch (result.interAction) {
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKING: {
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_GOOD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DIOPTER_LENS_CHECK_SUCCEED);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_FAILED: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FAIL);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_LEFT_BAD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DIOPTER_LENS_CHECK_LEFT_BAD);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_RIGHT_BAD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DIOPTER_LENS_CHECK_RIGHT_BAD);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_ALL_BAD: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DIOPTER_LENS_CHECK_ALL_BAD);
                    }
                } break;

                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FORCE_STOP:
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID: {
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_FORCE_STOP);
                    }
                } break;
                case WEARING_DETECTION_INTER_ACTION::WEARING_DETECTION_IA_FINISHED: {
                    m_AutoIPDFailCount = 0;
                    if (statusChageListener) {
                        statusChageListener->OnWearingDetectionStatusChange(WearingDetectionStatus::DETECTION_DONE);
                    }
                } break;
            }
        } break;
    }
}

void DetectionHandler::MoveIpd(const int step) {
    if (!m_IsInited) {
        return;
    }
    Cmd_Param param = (Cmd_Param)(step);
    Cmd_Param output;
    EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(WEARING_DETECTION_CMD::WEARING_DETECTION_CMD_MOVE_IPD, param, output);
    FOO_LOG("moduleControl MoveIpd return  %d", ret);
}

void DetectionHandler::LoadETCalibrationData() {
    if (!m_IsInited) {
        return;
    }
    Cmd_Param param = (Cmd_Param)(0);
    Cmd_Param output;
    EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(WEARING_DETECTION_CMD::WEARING_DETECTION_CMD_LOAD_ET_CALIBRATION_DATA, param, output);
    FOO_LOG("moduleControl LoadETCalibrationData return  %d", ret);
}

void DetectionHandler::ForceStop() {
    if (!m_IsInited) {
        return;
    }
    Cmd_Param param;
    Cmd_Param output;
    EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(WEARING_DETECTION_CMD::WEARING_DETECTION_CMD_FORCE_STOP, param, output);
    FOO_LOG("moduleControl ForceStop return  %d", ret);
}
void DetectionHandler::SweepForPositionGuide() {
    if (!m_IsInited) {
        return;
    }
    Cmd_Param param;
    Cmd_Param output;
    EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(3, param, output);
    FOO_LOG("moduleControl  SweepForPositionGuide return + %d", ret);
}
void DetectionHandler::StageAction(WEARING_DETECTION_CMD_ACTION action) {
    if (!m_IsInited) {
        return;
    }
    Cmd_Param param;
    Cmd_Param output;
    switch (action) {
        case WEARING_DETECTION_CMD_ACTION::POSITION_Y_JUDGEMENT: {
            param = (Cmd_Param)(WEARING_DETECTION_STAGE::WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT);
            output = (Cmd_Param)(&positionYCallback);

        } break;

        case WEARING_DETECTION_CMD_ACTION::POSITION_X_JUDGEMENT: {
            param = (Cmd_Param)(WEARING_DETECTION_STAGE::WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT);
            output = (Cmd_Param)(&positionXCallback);

        } break;

        case WEARING_DETECTION_CMD_ACTION::POSITION_Y_X_JUDGEMENT: {
            param = (Cmd_Param)(WEARING_DETECTION_STAGE::WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT);
            output = (Cmd_Param)(&positionYXCallback);

        } break;

        case WEARING_DETECTION_CMD_ACTION::IPD_ADJUSTMENT: {
            param = (Cmd_Param)(WEARING_DETECTION_STAGE::WEARING_DETECTION_STAGE_IPD_ADJUSTMENT);
            output = (Cmd_Param)(&ipdAdjustmentCallback);
        } break;

        case WEARING_DETECTION_CMD_ACTION::DIOPTER_LENS_CHECK: {
            param = (Cmd_Param)(WEARING_DETECTION_STAGE::WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK);
            output = (Cmd_Param)(&diopterLensCheckCallback);
        } break;

        default:
            FOO_LOG("StageAction is not supported. Action %d", action);
            return;
    }
    EXT_MODULE_ERROR ret = m_WearingDetectionModulePtr->moduleControl(WEARING_DETECTION_CMD::WEARING_DETECTION_CMD_STAGE_ACTION, param, output);
    FOO_LOG("StageAction return %d,%d,%p", ret, action, output);
}

void DetectionHandler::sendMessage(const Message& message) { m_DetectionThread->getLooper()->sendMessage(this, message); }

void DetectionHandler::sendMessageDelayed(nsecs_t uptimeDelay, const Message& message) { m_DetectionThread->getLooper()->sendMessageDelayed(uptimeDelay, this, message); }

void DetectionHandler::sendMessageAtTime(nsecs_t uptime, const Message& message) { m_DetectionThread->getLooper()->sendMessageAtTime(uptime, this, message); }

void DetectionHandler::removeMessages() { m_DetectionThread->getLooper()->removeMessages(this); }

void DetectionHandler::removeMessages(int what) { m_DetectionThread->getLooper()->removeMessages(this, what); }

DetectionHandler::~DetectionHandler() {
    // dtor
}
}  // namespace systemux
}  // namespace yvr
