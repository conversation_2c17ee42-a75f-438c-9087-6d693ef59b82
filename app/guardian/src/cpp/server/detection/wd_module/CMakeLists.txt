add_library(
	wearingdetection SHARED
	extModuleWearingDetection.cpp
	WearingDetection.cpp
	EyeData.cpp
	MotorOps.cpp
	IPDOps.cpp
	)

find_package(YVRUtils 1.0.0 REQUIRED)
find_package(ScreenLog 1.0.0 REQUIRED)
find_package(TrackingHelper 1.0.0 REQUIRED)
target_include_directories(wearingdetection PRIVATE ${TrackingHelper_INCLUDE_DIRS})
target_include_directories(
	wearingdetection
	PRIVATE ${CMAKE_SOURCE_DIR}/../tracking/external/algorithm/et_Tobii/wrapper
	)
target_include_directories(
	wearingdetection
	PRIVATE ${CMAKE_SOURCE_DIR}/../tracking/external/algorithm/et_Tobii/include
	)
target_link_libraries(
	wearingdetection
	aosp_utils
	aosp_cutils
	fuxi-external-rapidjson
	log
	TrackingHelper::TrackingHelper
	YVRUtils::YVRUtils
	ScreenLog::ScreenLog
	${UTILS_LIBRARY}
	${CUTILS_LIBRARY}
	)

target_compile_definitions(wearingdetection PRIVATE ENABLE_DEBUG_LOG)

add_executable(extModuleWDTest test/extModuleWDtest.cpp)

target_link_libraries(extModuleWDTest TrackingHelper::TrackingHelper)
target_include_directories(extModuleWDTest PRIVATE ${TrackingHelper_INCLUDE_DIRS})

find_package(YVRUtils 1.0.0 REQUIRED)
target_link_libraries(extModuleWDTest YVRUtils::YVRUtils)

target_link_libraries(
	extModuleWDTest
	aosp_utils
	aosp_cutils
	log
	${UTILS_LIBRARY}
	${CUTILS_LIBRARY}
	)

target_compile_definitions(extModuleWDTest PRIVATE ENABLE_DEBUG_LOG)

install(TARGETS extModuleWDTest RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})

add_executable(
	ipdAdjustmentCalibration test/ipdAdjustmentCalibration.cpp EyeData.cpp MotorOps.cpp
				 IPDOps.cpp
	)

target_link_libraries(ipdAdjustmentCalibration TrackingHelper::TrackingHelper)
target_include_directories(ipdAdjustmentCalibration PRIVATE ${TrackingHelper_INCLUDE_DIRS})

find_package(GuardianHelper 1.0.0 REQUIRED)
target_link_libraries(ipdAdjustmentCalibration GuardianHelper::GuardianHelper)
target_include_directories(ipdAdjustmentCalibration PRIVATE ${GuardianHelper_INCLUDE_DIRS})

target_include_directories(
	ipdAdjustmentCalibration
	PRIVATE ${CMAKE_SOURCE_DIR}/../tracking/external/algorithm/et_Tobii/wrapper
	)
target_include_directories(
	ipdAdjustmentCalibration
	PRIVATE ${CMAKE_SOURCE_DIR}/../tracking/external/algorithm/et_Tobii/include
	)

find_package(YVRUtils 1.0.0 REQUIRED)
target_link_libraries(ipdAdjustmentCalibration YVRUtils::YVRUtils)

target_link_libraries(
	ipdAdjustmentCalibration
	aosp_utils
	aosp_cutils
	log
	fuxi-external-rapidjson
	TrackingHelper::TrackingHelper
	YVRUtils::YVRUtils
	${UTILS_LIBRARY}
	${CUTILS_LIBRARY}
	)

target_compile_definitions(ipdAdjustmentCalibration PRIVATE ENABLE_DEBUG_LOG)

install(TARGETS ipdAdjustmentCalibration RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR})
