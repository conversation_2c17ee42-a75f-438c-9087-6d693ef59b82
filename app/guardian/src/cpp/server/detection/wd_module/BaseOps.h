#ifndef _BASE_OPS
#define _BASE_OPS

#include "trackingapi/eyetracking_types.h"
#include "trackingapi/TrackingAPI_Helper.h"
#include "yvrutils/DebugLog.h"

class BaseOps {
 public:
    BaseOps() : mDebugLogFlags(), mIsTrackingApiReady(false) { mIsTrackingApiReady = (0 == TRACKINGAPI_Init()); }
    ~BaseOps() {
        if (mIsTrackingApiReady) {
            TRACKINGAPI_DeInit();
            mIsTrackingApiReady = false;
        }
    };

    void setDebugFlag(Debug_Log_Flags dFlags) { mDebugLogFlags = dFlags; }

 protected:
    Debug_Log_Flags mDebugLogFlags;
    bool mIsTrackingApiReady;
};

#endif  // _BASE_OPS