#include <fstream>
#include <sstream>
#include <string>
#include <unistd.h>
#include <utils/Log.h>
#include <cutils/properties.h>
#include <math.h>
#include <atomic>
#include <vector>
#include <pthread.h>

#include "trackingapi/TrackingAPI_Helper.h"
#include "rapidjson/document.h"
#include "rapidjson/prettywriter.h"
#include "screenlog/YvrSLog.h"
#include "trackingapi/ipd_configuration.h"

#include "WearingDetection.h"

extern EXT_MODULE_ERROR getModuleName(std::string& moduleName);
extern EXT_MODULE_ERROR getModuleStatus(WEARING_DETECTION_STATUS& status);
extern EXT_MODULE_ERROR setModuleStatus(const WEARING_DETECTION_STATUS status);

using namespace rapidjson;
using namespace std;

namespace android {
namespace yvr {

Debug_Log_Flags WearingDetection::mDebugLogFlags = {0};

/* #define EYE_TRACKING_CALIBRATION_DATA_FILE_PATH "/data/misc/trackingservice/et/eye_calibration.bin"
#define EYE_TRACKING_CALIBRATION_DATA_S_FILE_PATH "/vendor/etc/eye_calibration_s.bin"
#define EYE_TRACKING_CALIBRATION_DATA_M_FILE_PATH "/vendor/etc/eye_calibration_m.bin"
#define EYE_TRACKING_CALIBRATION_DATA_L_FILE_PATH "/vendor/etc/eye_calibration_l.bin" */
#define WEARING_DETECTION_DATA_FILE_PATH "/backup/wddata.json"

#define SAFE_CALL_IA_CB(ia_cb, ia)                                      \
    do {                                                                \
        if (ia_cb) {                                                    \
            ia_cb(ia);                                                  \
        } else {                                                        \
            DLOGW(WearingDetection::mModuleName.c_str(), "NULL ia_cb"); \
        }                                                               \
    } while (0);

#define IDEAL_EYE_POSITION_X 0.5
#define IDEAL_EYE_POSITION_X_L 0.541679  // 0.413
#define IDEAL_EYE_POSITION_X_R 0.595516  // 0.557
#define IDEAL_EYE_POSITION_Y 0.5
#define IDEAL_EYE_POSITION_Y_L 0.5
#define IDEAL_EYE_POSITION_Y_R 0.5
#define INVALID_EYE_POSITION_X 0.1
#define INVALID_EYE_POSITION_Y 0.1
#define IDEAL_EYE_POSITION_X_COEF_A 90.0

#define ET_DATA_COLLECTION_FREQUENCY (90)
#define ET_DATA_COLLECTION_PERCENTAGE (0.5)
#define ET_DATA_COLLECTION_DURATION (0.5)

// #define SHOW_OVERLAY_INFO

enum MOTOR_MOVING_DIR {
    MOTOR_MOVING_DIR_NO_MOVING,
    MOTOR_MOVING_DIR_OUTWARDS,
    MOTOR_MOVING_DIR_INWARDS,
};

auto is_inside = [](float value, float center, float range) -> bool { return (value <= (center + range) && value >= (center - range)); };
auto is_too_low = [](float value, float center, float range) -> bool { return (value < (center - range)); };
auto is_too_high = [](float value, float center, float range) -> bool { return (value > (center + range)); };
auto get_diff = [](float value, float center) -> float { return (value - center); };
// auto get_abs_diff= [](float value, float center)->float{return (fabs(value-center));};
auto get_abs_diff = [](float value, float center) -> float { return (fabs(get_diff(value, center))); };
auto get_max = [](float value1, float value2) -> float { return (value1 > value2) ? value1 : value2; };
auto get_min = [](float value1, float value2) -> float { return (value1 < value2) ? value1 : value2; };
auto get_average = [](float value1, float value2) -> float { return (value1 + value2) / 2; };

/* typedef struct IPD_Range {
    float min;
    float max;
} IPD_Range; */

const IPD_Range kMOTOR_RANGE_TOLERENCE = {0.3f, 0.3f};

// const IPD_Range kET_MOTOR_CONFIG_RANGE = {IPD_FULL_RANGE_MIN, IPD_FULL_RANGE_MAX};
const IPD_Range kET_POS_LEVEL_RANGE[] = {
    {IPD_RANGE_S_MIN, IPD_RANGE_S_MAX},
    {IPD_RANGE_M_MIN, IPD_RANGE_M_MAX},
    {IPD_RANGE_L_MIN, IPD_RANGE_L_MAX},
    {IPD_RANGE_XL_MIN, IPD_RANGE_XL_MAX},
};
const int kET_POS_LEVEL_MAX = sizeof(kET_POS_LEVEL_RANGE) / sizeof(IPD_Range);
const char* kCALIBRATION_DATA_PATH[kET_POS_LEVEL_MAX] = {
    EYE_TRACKING_CALIBRATION_DATA_S_FILE_PATH,
    EYE_TRACKING_CALIBRATION_DATA_M_FILE_PATH,
    EYE_TRACKING_CALIBRATION_DATA_L_FILE_PATH,
    EYE_TRACKING_CALIBRATION_DATA_XL_FILE_PATH,
};

std::string WearingDetection::mModuleName = "";

WearingDetection::WearingDetectionParams WearingDetection::mWDParam = {
    .collection_frequency = ET_DATA_COLLECTION_FREQUENCY,
    .collection_percentage = ET_DATA_COLLECTION_PERCENTAGE,
    .collection_duration = ET_DATA_COLLECTION_DURATION,
};

WearingDetection::WearingDetectionJudegment WearingDetection::mWDJData = {
    .range_Y1 = 0.4,
    .range_Y2 = 0.2,
    .range_Xr = 0.1,
    .range_Xt = 0.2,
    .range_IPD = 2.0,
};

WearingDetection::WearingDetectionCalibration WearingDetection::mWDCData = {
    .left_X1 = IDEAL_EYE_POSITION_X,
    .left_Ax1 = IDEAL_EYE_POSITION_X_COEF_A,
    .left_X2 = IDEAL_EYE_POSITION_X,
    .left_Ax2 = IDEAL_EYE_POSITION_X_COEF_A,
    .right_X1 = IDEAL_EYE_POSITION_X,
    .right_Ax1 = IDEAL_EYE_POSITION_X_COEF_A,
    .right_X2 = IDEAL_EYE_POSITION_X,
    .right_Ax2 = IDEAL_EYE_POSITION_X_COEF_A,
};

// WEARING_DETECTION_STAGE WearingDetection::mStartStage = WEARING_DETECTION_STAGE_FINISHED;
// WEARING_DETECTION_STAGE WearingDetection::mNextStage  = WEARING_DETECTION_STAGE_FINISHED;
std::atomic<WEARING_DETECTION_STAGE> WearingDetection::mCurrStage = WEARING_DETECTION_STAGE_FINISHED;
uint8_t WearingDetection::mCurrStageIndex = 0;
uint8_t WearingDetection::mLastStageIndex = 0;
vector<WEARING_DETECTION_STAGE> WearingDetection::mTestStages;

static vector<WEARING_DETECTION_STAGE> MULTI_STAGES_MODE_DEFAULT = {
    WEARING_DETECTION_STAGE_START, WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT, WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT, WEARING_DETECTION_STAGE_IPD_ADJUSTMENT, WEARING_DETECTION_STAGE_FINISHED,
};

static vector<WEARING_DETECTION_STAGE> MULTI_STAGES_MODE_TEST = {
    WEARING_DETECTION_STAGE_START,          WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT,     WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT,
    WEARING_DETECTION_STAGE_IPD_ADJUSTMENT, WEARING_DETECTION_STAGE_EYETRACKIHNG_CALIBRATION, WEARING_DETECTION_STAGE_FINISHED,
};

const uint32_t MAX_EYE_TRACKING_INTERFACE_FAILURE_COUNT = 3;
const uint32_t MAX_EYE_TRACKING_COLLECTING_FAILURE_COUNT = 10;
const uint32_t MAX_STAGE_ACTION_FAILURE_COUNT = 20;

WearingDetection::WearingDetection() {
    getModuleName(mModuleName);
    mIsTrackingApiReady = (0 == TRACKINGAPI_Init());
    // mIsFirstWearing = !getEyeTrackingCalData();
    mIsRunning = false;
    // mIsETDataCollecting = false;
    // memset(&mWDJData, 0, sizeof(mWDJData));

    loadProjectMode();

    getJudgementData();
    getCalibrationData();
    getParam();

    // init log flags
    int32_t logMask = property_get_int32(PROPERTY_WEARINGDETECTION_LOG_MASK, 0);
    parseLogMask(logMask, &mDebugLogFlags);
}

WearingDetection::~WearingDetection() {
    stopJob();
    if (mIsTrackingApiReady) {
        TRACKINGAPI_DeInit();
        mIsTrackingApiReady = false;
    }
}

WearingDetection* WearingDetection::getInstance() {
    static WearingDetection moduleSingleton;

    return &moduleSingleton;
}

auto getStage = [](uint8_t stageIndex, vector<WEARING_DETECTION_STAGE>& stages) -> WEARING_DETECTION_STAGE {
    if (stageIndex < stages.size()) {
        return stages[stageIndex];
    } else {
        DLOGE("", "Invalid stage index.");
        return WEARING_DETECTION_STAGE_NONE;
    }
};

void WearingDetection::job(const WEARING_DETECTION_MULTI_STAGES_MODE mode, const interAction_callback ia_cb, uint32_t startIndex) {
    prepareJob();

    WearingDetection* module = WearingDetection::getInstance();

    bool bIsFirstWearing = module->isFirstWearing();

    switch (mode) {
        default:
        case WEARING_DETECTION_MULTI_STAGES_MODE_DEFAULT:
            mTestStages = MULTI_STAGES_MODE_DEFAULT;
            break;
        case WEARING_DETECTION_MULTI_STAGES_MODE_TEST:
            mTestStages = MULTI_STAGES_MODE_TEST;
            break;
    }

    mCurrStageIndex = startIndex;
    mLastStageIndex = startIndex;
    mCurrStage = getStage(mCurrStageIndex, mTestStages);

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "start stage %d", mCurrStage.load());
    while (canJobRun()) {
        stopJob();
    }

    jobEnds();
}

void WearingDetection::job_s(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb) {
    prepareJob();

    std::string threadName = WearingDetection::mModuleName.c_str();
    threadName += "-single-job";
    pthread_setname_np(pthread_self(), threadName.c_str());

    WearingDetection* module = WearingDetection::getInstance();

    mCurrStage = stage;
    uint32_t eye_tracking_interface_failure_count = 0;
    uint32_t eye_tracking_collecting_failure_count = 0;
    uint32_t stage_action_failure_count = 0;

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "start stage %d", mCurrStage.load());
    while (canJobRun()) {
        if (mCurrStage == WEARING_DETECTION_STAGE_START) {
            break;
        } else if (mCurrStage == WEARING_DETECTION_STAGE_FINISHED) {
            break;
        }

        int retStageAction = WEARING_DETECTION_RESULT_OK;

        retStageAction = module->stageAction(mCurrStage, ia_cb);
        if (WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED == (retStageAction & WEARING_DETECTION_RESULT_MASK)) {
            if (eye_tracking_interface_failure_count < MAX_EYE_TRACKING_INTERFACE_FAILURE_COUNT) {
                eye_tracking_interface_failure_count++;
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Failed to collect ET data. Waiting for EyeTracking's wake-up. Count %d",
                         eye_tracking_interface_failure_count);
                sleep(1);
            } else {
                DLOGE(WearingDetection::mModuleName.c_str(), "Failed to collect ET data overtime. Quit wearing detection.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID);

                break;
            }
        } else if (WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED == (retStageAction & WEARING_DETECTION_RESULT_MASK)) {
            if (eye_tracking_collecting_failure_count < MAX_EYE_TRACKING_COLLECTING_FAILURE_COUNT) {
                eye_tracking_collecting_failure_count++;
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "ET data is invalid. Count %d", eye_tracking_collecting_failure_count);
            } else {
                DLOGE(WearingDetection::mModuleName.c_str(), "ET data is invalid overtime. Quit wearing detection.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID);

                break;
            }
        } else if (WEARING_DETECTION_RESULT_FORCE_STOP == (retStageAction & WEARING_DETECTION_RESULT_MASK)) {
            DLOGW(WearingDetection::mModuleName.c_str(), "Force Stop collecting Eye Pose State. Quit wearing detection.");
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FORCE_STOP);

            break;
        } else if (WEARING_DETECTION_RESULT_Y_UPWARDS == (retStageAction & WEARING_DETECTION_RESULT_Y_MASK) ||
                   WEARING_DETECTION_RESULT_Y_DOWNWARDS == (retStageAction & WEARING_DETECTION_RESULT_Y_MASK)) {
            // redo
            if (stage_action_failure_count >= MAX_STAGE_ACTION_FAILURE_COUNT) {
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Stage action failure for %d times", stage_action_failure_count);
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FORCE_STOP);
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

                break;
            } else {
                stage_action_failure_count++;
                usleep(11);
            }
        } else if (WEARING_DETECTION_RESULT_LEFT_LENS_MOUNTING_BAD == (retStageAction & WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK) ||
                   WEARING_DETECTION_RESULT_RIGHT_LENS_MOUNTING_BAD == (retStageAction & WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK) ||
                   WEARING_DETECTION_RESULT_ALL_LENS_MOUNTING_BAD == (retStageAction & WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK)) {
            // redo
            if (stage_action_failure_count >= MAX_STAGE_ACTION_FAILURE_COUNT) {
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Stage action failure for %d times", stage_action_failure_count);
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FORCE_STOP);
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

                break;
            } else {
                stage_action_failure_count++;
                usleep(11);
            }
        } else if (WEARING_DETECTION_RESULT_X_LEFTWARDS == (retStageAction & WEARING_DETECTION_RESULT_X_MASK) ||
                   WEARING_DETECTION_RESULT_X_RIGHTWARDS == (retStageAction & WEARING_DETECTION_RESULT_X_MASK)) {
            // take this as WEARING_DETECTION_RESULT_OK
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

            break;
        } else if (WEARING_DETECTION_RESULT_OK == (retStageAction & WEARING_DETECTION_RESULT_MASK)) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

            break;
        } else if (WEARING_DETECTION_RESULT_FAILED == (retStageAction & WEARING_DETECTION_RESULT_MASK)) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

            break;
        } else {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);

            break;
        }
    }

    jobEnds();
}

void WearingDetection::stopJob() {
    WearingDetection* module = WearingDetection::getInstance();

    module->mIsRunning = false;
}

void WearingDetection::prepareJob() {
    setModuleStatus(WEARING_DETECTION_STATUS_STARTING);

    WearingDetection* module = WearingDetection::getInstance();

    module->mIsRunning = true;
}

void WearingDetection::jobEnds() { setModuleStatus(WEARING_DETECTION_STATUS_INITIALIZED); }

bool WearingDetection::canJobRun() {
    WearingDetection* module = WearingDetection::getInstance();

    return module->mIsRunning.load();
}

WEARING_DETECTION_STAGE WearingDetection::getCurrentStage() { return mCurrStage; }

uint32_t WearingDetection::getCurrentStageIndex() { return mCurrStageIndex; }

bool WearingDetection::moveIPD(float offset) {
    bool bRet = false;
    WearingDetection* module = WearingDetection::getInstance();

    if (module && module->mIsTrackingApiReady) {
        bRet = module->moveIpd(offset);
    } else {
        DLOGE(WearingDetection::mModuleName.c_str(), "module is not ready");
    }

    return bRet;
}

bool WearingDetection::setIPD(float targetPos) {
    bool bRet = false;
    WearingDetection* module = WearingDetection::getInstance();

    if (module && module->mIsTrackingApiReady) {
        bRet = module->setIpd(targetPos);
    } else {
        DLOGE(WearingDetection::mModuleName.c_str(), "module is not ready");
    }

    return bRet;
}

void WearingDetection::sweepMotorAndSaveETData() {
    prepareJob();

    WearingDetection* module = WearingDetection::getInstance();

    const float IPD_DEFAULT = 64.0;
    const float IPD_MIN = 59.0, IPD_MAX = 69.5;
    const float IPD_STEP = 1.0;
    tobii_lens_configuration_t lens_config_default;
    tobii_lens_configuration_t lens_config_curr;
    int retLens = -1;

    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "start sweep");

    double ipd;
    retLens = TRACKINGAPI_GetLensConfig(&ipd);
    if (retLens == 0) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "initial ipd: %f mm", ipd);
    }

    for (float currIPD = IPD_MIN; currIPD < IPD_MAX; currIPD += IPD_STEP) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "IPD: %fmm", currIPD);

        bool bRet = false;
        int ret = -1;

        bRet = module->setMotorDistance(currIPD);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "setMotorDistance bRet: %d", bRet);
        // sleep(2);

        retLens = TRACKINGAPI_GetLensConfig(&ipd);
        if (retLens == 0) {
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "read-out lens config ipd: %f mm", ipd);
        }

        EyeData wdData;
        ret = module->collectingEyeTrackingData(mWDParam.collection_frequency, mWDParam.collection_duration, mWDParam.collection_percentage, wdData);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "collectingEyeTrackingData ret %d, Average Left: PG_X %f, PG_Y %f, Average Right: PG_X %f, PG_Y %f", ret,
                 wdData.left_pupil_position_X, wdData.left_pupil_position_Y, wdData.right_pupil_position_X, wdData.right_pupil_position_Y);
    }
    for (float currIPD = IPD_MAX; currIPD > IPD_MIN; currIPD -= IPD_STEP) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "IPD: %fmm", currIPD);

        bool bRet = false;
        int ret = -1;

        bRet = module->setMotorDistance(currIPD);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "setMotorDistance bRet: %d", bRet);
        // sleep(2);

        retLens = TRACKINGAPI_GetLensConfig(&ipd);
        if (retLens == 0) {
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "read-out lens config ipd: %f mm", ipd);
        }

        EyeData wdData;
        ret = module->collectingEyeTrackingData(mWDParam.collection_frequency, mWDParam.collection_duration, mWDParam.collection_percentage, wdData);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "collectingEyeTrackingData ret %d, Average Left: PG_X %f, PG_Y %f, Average Right: PG_X %f, PG_Y %f", ret,
                 wdData.left_pupil_position_X, wdData.left_pupil_position_Y, wdData.right_pupil_position_X, wdData.right_pupil_position_Y);
    }

    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "stop sweep");
    // stopJob();
    jobEnds();
}

bool WearingDetection::getCurrentPGData(float& leftX, float& leftY, float& rightX, float& rightY) {
    bool bRet = false;
    WearingDetection* module = WearingDetection::getInstance();

    if (module && module->mIsTrackingApiReady) {
        float collection_frequency = ET_DATA_COLLECTION_FREQUENCY;
        float collection_duration = ET_DATA_COLLECTION_DURATION, collection_percentage = ET_DATA_COLLECTION_PERCENTAGE;
        EyeData wdData;
        int result = module->collectingEyeTrackingData(collection_frequency, collection_duration, collection_percentage, wdData);
        if (WEARING_DETECTION_RESULT_OK == result) {
            leftX = wdData.left_pupil_position_X;
            leftY = wdData.left_pupil_position_Y;
            rightX = wdData.right_pupil_position_X;
            rightY = wdData.right_pupil_position_Y;

            bRet = true;
        } else if ((WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED == result) || (WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED == result)) {
            DLOGE(WearingDetection::mModuleName.c_str(), "Failed to collect eye data. result: %d", result);
        } else if (WEARING_DETECTION_RESULT_FORCE_STOP == result) {
            DLOGW(WearingDetection::mModuleName.c_str(), "Force Stop collecting eye data. Quit current stage.");
        } else {
            DLOGE(WearingDetection::mModuleName.c_str(), "Shouldn't happen from collectingEyeTrackingData with code %d.", result);
        }
    } else {
        DLOGE(WearingDetection::mModuleName.c_str(), "module is not ready");
    }

    return bRet;
}

bool WearingDetection::isFirstWearing() {
    // return mIsFirstWearing;
    return (!getEyeTrackingCalData());
}

int WearingDetection::stageInit(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb) {
    int ret = WEARING_DETECTION_RESULT_OK;

    // interaction at beginning
    switch (stage) {
        case WEARING_DETECTION_STAGE_START:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_START);
            break;
        case WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_AND_X_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_IPD_ADJUSTMENT:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_EYETRACKIHNG_CALIBRATION:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_EYETRACKIHNG_CALIBRATION_CHECKING);
            break;
        case WEARING_DETECTION_STAGE_FINISHED:
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_FINISHED);
            break;
        default:
            break;
    }

    return ret;
}

int WearingDetection::stageJudgement(const WEARING_DETECTION_STAGE stage, const EyeData wdData) {
    int ret = WEARING_DETECTION_RESULT_FAILED;

    switch (stage) {
        case WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT: {
            ret = checkPositionY(wdData, mWDJData, mWDCData, isFirstWearing());

            break;
        }
        case WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT: {
            ret = checkPositionX(wdData, mWDJData, mWDCData, true);
            if ((PROJECT_BUILD_SPEC_BIZ == mProjectMode) && (WEARING_DETECTION_RESULT_X_OUTWARDS == ret || WEARING_DETECTION_RESULT_X_INWARDS == ret)) {
                ret = reCheckPositionXForBizMode(wdData, mWDJData, mWDCData);
            }

            break;
        }
        case WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT: {
            ret = checkPositionY(wdData, mWDJData, mWDCData, isFirstWearing());
            if ((ret & WEARING_DETECTION_RESULT_Y_MASK) == WEARING_DETECTION_RESULT_Y_GOOD) {
                int xRet = checkPositionX(wdData, mWDJData, mWDCData, true);
                if ((PROJECT_BUILD_SPEC_BIZ == mProjectMode) && (WEARING_DETECTION_RESULT_X_OUTWARDS == xRet || WEARING_DETECTION_RESULT_X_INWARDS == xRet)) {
                    ret |= reCheckPositionXForBizMode(wdData, mWDJData, mWDCData);
                } else {
                    ret |= xRet;
                }
            }

            break;
        }
        case WEARING_DETECTION_STAGE_IPD_ADJUSTMENT: {
            float len = 0.0;
            // ret = compareIPD(wdData, mWDJdata);
            ret = checkPositionX(wdData, mWDJData, mWDCData, false);

            break;
        }
        case WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK: {
            ret = checkLensMounting();

            break;
        }
        case WEARING_DETECTION_STAGE_EYETRACKIHNG_CALIBRATION: {
            // nothing to do now
            ret = WEARING_DETECTION_RESULT_OK;

            break;
        }
        default:
            ret = WEARING_DETECTION_RESULT_OK;

            break;
    }

    return ret;
}

int WearingDetection::stageProcess(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb, const int result, const EyeData wdData) {
    int ret = WEARING_DETECTION_RESULT_OK;

    int res = result & WEARING_DETECTION_RESULT_MASK;
    int yResult = result & WEARING_DETECTION_RESULT_Y_MASK;
    int xResult = result & WEARING_DETECTION_RESULT_X_MASK;
    int lmResult = result & WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK;

    if (res == WEARING_DETECTION_RESULT_OK) {
        // interaction before processing
        switch (xResult) {
            case WEARING_DETECTION_RESULT_X_OUTWARDS: {
                if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    // if (isWDDataValid(mWdDataOfLastXCheck)) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Process moving IPD outwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_OUTWARDS);
                    /* } else {
                        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need moving IPD outwards.");
                        SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_OUTWARDS);
                    } */
                }
                break;
            }
            case WEARING_DETECTION_RESULT_X_INWARDS: {
                if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    // if (isWDDataValid(mWdDataOfLastXCheck)) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Process moving IPD inwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_INWARDS);
                    /* } else {
                        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need moving IPD inwards.");
                        SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_INWARDS);
                    } */
                }
                break;
            }
            case WEARING_DETECTION_RESULT_X_GOOD: {
                if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "IPD is OK.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_GOOD);
                }
                break;
            }
            default:
                break;
        }

        // processing
        switch (xResult) {
            case WEARING_DETECTION_RESULT_X_OUTWARDS: {
                if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    float len = 0.0;
                    calculateIPDMoving(wdData, mWDJData, mWDCData, len);
                    moveIpd(len);
                }

                break;
            }
            case WEARING_DETECTION_RESULT_X_INWARDS: {
                if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    float len = 0.0;
                    calculateIPDMoving(wdData, mWDJData, mWDCData, len);
                    moveIpd(len);
                }

                break;
            }
            default:
                break;
        }
    }

    return ret;
}

int WearingDetection::stageEnd(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb, const int result) {
    int ret = WEARING_DETECTION_RESULT_OK;

    int res = result & WEARING_DETECTION_RESULT_MASK;
    int yResult = result & WEARING_DETECTION_RESULT_Y_MASK;
    int xResult = result & WEARING_DETECTION_RESULT_X_MASK;
    int lmResult = result & WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK;

    // interaction at ending
    if (res == WEARING_DETECTION_RESULT_OK) {
        switch (yResult) {
            case WEARING_DETECTION_RESULT_Y_UPWARDS: {
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move hmd upwards.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_CHECKED_UPWARDS);
                break;
            }
            case WEARING_DETECTION_RESULT_Y_DOWNWARDS: {
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move hmd downwards.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_CHECKED_DOWNWARDS);
                break;
            }
            case WEARING_DETECTION_RESULT_Y_GOOD: {
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Y check OK.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_CHECKED_GOOD);
                break;
            }
            default: {
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Unsupported result for Y check.");
                break;
            }
        }

        switch (xResult) {
            case WEARING_DETECTION_RESULT_X_LEFTWARDS:
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move hmd leftwards.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKING_LEFTWARDS);
                break;
            case WEARING_DETECTION_RESULT_X_RIGHTWARDS:
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move hmd rightwards.");
                SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKING_RIGHTWARDS);
                break;
            case WEARING_DETECTION_RESULT_X_OUTWARDS: {
                if (stage == WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT || stage == WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move IPD outwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKING_OUTWARDS);
                } /* else if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Has moved IPD outwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_GOOD);
                } */
                break;
            }
            case WEARING_DETECTION_RESULT_X_INWARDS: {
                if (stage == WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT || stage == WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Need to move IPD inwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKING_INWARDS);
                } /* else if (stage == WEARING_DETECTION_STAGE_IPD_ADJUSTMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Has moved IPD inwards.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_GOOD);
                } */
                break;
            }
            case WEARING_DETECTION_RESULT_X_GOOD: {
                if (stage == WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT || stage == WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "X check OK.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKED_GOOD);
                }
                break;
            }
            default:
                DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Unsupported result for X check.");
                break;
        }

        switch (lmResult) {
            case WEARING_DETECTION_RESULT_LEFT_LENS_MOUNTING_BAD: {
                if (stage == WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "lens mounting left NG.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_LEFT_BAD);
                }
                break;
            }
            case WEARING_DETECTION_RESULT_RIGHT_LENS_MOUNTING_BAD: {
                if (stage == WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "lens mounting right NG.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_RIGHT_BAD);
                }
                break;
            }
            case WEARING_DETECTION_RESULT_ALL_LENS_MOUNTING_BAD: {
                if (stage == WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "lens mounting all NG.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_ALL_BAD);
                }
                break;
            }
            case WEARING_DETECTION_RESULT_LENS_MOUNTING_GOOD: {
                if (stage == WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "lens mounting good.");
                    SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_GOOD);
                }
                break;
            }
        }
    } else if (res == WEARING_DETECTION_RESULT_FAILED || res == WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED || res == WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED) {
        // don't send out failure IA after every stage action, cuz items may redo and IA will be sent at the the end of job
        /* if (WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT == stage) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_CHECKED_FAILED);
        } else if (WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT == stage) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_X_CHECKED_FAILED);
        } else if (WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT == stage) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_POSITION_Y_AND_X_CHECKED_FAILED);
        } else if (WEARING_DETECTION_STAGE_IPD_ADJUSTMENT == stage) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_IPD_CHECKED_FAILED);
        } else if (WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK == stage) {
            SAFE_CALL_IA_CB(ia_cb, WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_FAILED);
        } */
    } else {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Nothing to do.");
    }

    return ret;
}

int WearingDetection::stageAction(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb) {
    int ret = WEARING_DETECTION_RESULT_OK;
    EyeData wdData;
    /* int retDataCollecting = WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED;
    int retStageJudgement = WEARING_DETECTION_RESULT_FAILED;
    int retStageProcessing = WEARING_DETECTION_RESULT_FAILED; */
    int result = WEARING_DETECTION_RESULT_FAILED;
    bool bSkipCollecting = false;

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "current stage %d.\n", stage);

    stageInit(stage, ia_cb);

    /* if (WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT == stage || WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT == stage || WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT == stage ||
        WEARING_DETECTION_STAGE_IPD_ADJUSTMENT == stage || WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK == stage) { */
    if (WEARING_DETECTION_STAGE_START < stage && WEARING_DETECTION_STAGE_NUM >= stage) {
        if (WEARING_DETECTION_STAGE_IPD_ADJUSTMENT == stage) {
            if (isWDDataValid(mWdDataOfLastXCheck)) {
                result = WEARING_DETECTION_RESULT_OK;  // skip collecting
                bSkipCollecting = true;

                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "mWdDataOfLastXCheck: L_X %f, L_Y %f, R_X %f, R_Y %f", mWdDataOfLastXCheck.left_pupil_position_X,
                         mWdDataOfLastXCheck.left_pupil_position_Y, mWdDataOfLastXCheck.right_pupil_position_X, mWdDataOfLastXCheck.right_pupil_position_Y);
                wdData = mWdDataOfLastXCheck;
            } else {
                // mops.setMotorDistance((mops.MOTOR_RANGE.min + mops.MOTOR_RANGE.max) / 2);
                setIpd((mops.MOTOR_RANGE.min + mops.MOTOR_RANGE.max) / 2);
            }
            /* } else if (WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK == stage) {
                    result = WEARING_DETECTION_RESULT_OK;  // skip collecting
                    bSkipCollecting = true; */
        } else {
        }

        if (!bSkipCollecting) {
            result = collectingEyeTrackingData(mWDParam.collection_frequency, mWDParam.collection_duration, mWDParam.collection_percentage, wdData);
        }

        if (WEARING_DETECTION_RESULT_OK == result) {
            /* if (WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK == stage) {
                return WEARING_DETECTION_RESULT_LENS_MOUNTING_GOOD;
            } */

            result = stageJudgement(stage, wdData);
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "stageJudgement return 0x%x", result);
            if (WEARING_DETECTION_RESULT_FAILED == (result & WEARING_DETECTION_RESULT_MASK)) {
                DLOGE(WearingDetection::mModuleName.c_str(), "Unexpected failure in stageJudgement.");
            } else if (WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED == (result & WEARING_DETECTION_RESULT_MASK)) {
                DLOGE(WearingDetection::mModuleName.c_str(), "ET Interface failure in stageJudgement.");
            } else {
                result |= stageProcess(stage, ia_cb, result, wdData);
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "stageProcess return 0x%x", result);
                if (WEARING_DETECTION_RESULT_FAILED == (result & WEARING_DETECTION_RESULT_MASK)) {
                    DLOGE(WearingDetection::mModuleName.c_str(), "Unexpected failure in stageProcess.");
                }

                if (WEARING_DETECTION_STAGE_IPD_ADJUSTMENT == stage && isWDDataValid(mWdDataOfLastXCheck)) {
                    clearWDData(mWdDataOfLastXCheck);
                } else if (WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT == stage || WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT == stage) {
                    int xResult = result & WEARING_DETECTION_RESULT_X_MASK;
                    if (xResult == WEARING_DETECTION_RESULT_X_OUTWARDS || xResult == WEARING_DETECTION_RESULT_X_INWARDS) {
                        mWdDataOfLastXCheck = wdData;
                    }
                }
            }
        } else if (WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED == result) {
            DLOGE(WearingDetection::mModuleName.c_str(), "ET Interface failure in collectingEyeTrackingData. result: %d", result);
        } else if (WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED == result) {
            DLOGE(WearingDetection::mModuleName.c_str(), "Failed to collect eye data. result: %d", result);
            if (WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK == stage) {
                result = stageJudgement(stage, wdData);
            }
        } else if (WEARING_DETECTION_RESULT_FORCE_STOP == result) {
            DLOGW(WearingDetection::mModuleName.c_str(), "Force Stop collecting eye data. Quit current stage.");
        } else {
            DLOGE(WearingDetection::mModuleName.c_str(), "Shouldn't happen from collectingEyeTrackingData with code %d.", result);
        }
    } else {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Do nothing.");
        result = WEARING_DETECTION_RESULT_OK;
    }

    stageEnd(stage, ia_cb, result);

    return result;
}

void WearingDetection::stageActions(const vector<WEARING_DETECTION_STAGE> stages, const interAction_callback ia_cb, uint32_t startIndex) {}

bool WearingDetection::getEyeTrackingCalData() {
    // read calibration data
    ifstream infile(EYE_TRACKING_CALIBRATION_DATA_FILE_PATH);
    if (!infile.is_open()) return false;
    // apply data

    infile.close();
    return true;
}

int WearingDetection::getEyeTrackingPosLevel(float ipd) {
    int level = 0;
    for (; level < kET_POS_LEVEL_MAX; level++) {
        if (ipd >= kET_POS_LEVEL_RANGE[level].min && ipd < kET_POS_LEVEL_RANGE[level].max) break;
    }
    if (level < kET_POS_LEVEL_MAX) {
        return level;
    } else {
        return -1;
    }
}

bool WearingDetection::applyEyeTrackingCalData() {
    bool bRet = false;
    WearingDetection* module = WearingDetection::getInstance();

    if (module && module->mIsTrackingApiReady) {
        float ipd = 0.0;
        if (module->getEyeTrackingCalData()) {
            bRet = module->applyEyeTrackingCalData(EYE_TRACKING_CALIBRATION_DATA_FILE_PATH);
        } else if (module->getMotorDistance(ipd)) {
            int level = module->getEyeTrackingPosLevel(ipd);
            if (level >= 0) {
                DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Try to apply %s data.", kCALIBRATION_DATA_PATH[level]);
                bRet = module->applyEyeTrackingCalData(kCALIBRATION_DATA_PATH[level]);
            } else {
                DLOGE(WearingDetection::mModuleName.c_str(), "Ipd is out of range.");
            }
        } else {
            DLOGE(WearingDetection::mModuleName.c_str(), "Can NOT apply calibration data.");
        }
    } else {
        DLOGE(WearingDetection::mModuleName.c_str(), "module is not ready");
    }

    return bRet;
}

bool WearingDetection::applyEyeTrackingCalData(std::string path) {
    bool bRet = false;
    if (mIsTrackingApiReady) {
        bRet = (0 == TRACKINGAPI_LoadEyeTrackingCalibrationData(path.c_str()));
    }
    return bRet;
}

bool WearingDetection::getMotorDistance(float& distance) {  // unit: mm
    return mops.getMotorDistance(distance);
}

bool WearingDetection::setMotorDistance(const float distance) {  // unit: mm
    return mops.setMotorDistance(std::move(distance));
}

bool WearingDetection::getIpdConfig(float& ipd) {  // unit: mm
    return iops.getIpdConfig(ipd);
}

bool WearingDetection::setIpdConfig(const float ipd) {  // unit: mm
    return iops.setIpdConfig(ipd);
}

float WearingDetection::getLastIPD() {
    float ipd = 64.0f;

    iops.getIpd(ipd);

    return ipd;
}

bool WearingDetection::moveIpd(const float offset) {
    bool bRet = iops.moveIpd(offset);
    if (bRet) {
        // if (PROJECT_BUILD_SPEC_BIZ == mProjectMode) {
        //     applyEyeTrackingCalData();
        // } this feature is moved out as an command, and it needs to be called from outside
    }

    return bRet;
}
bool WearingDetection::setIpd(const float ipd) {
    bool bRet = iops.setIpd(ipd);
    if (bRet) {
        // if (PROJECT_BUILD_SPEC_BIZ == mProjectMode) {
        //     applyEyeTrackingCalData();
        // } this feature is moved out as an command, and it needs to be called from outside
    }

    return bRet;
}

void WearingDetection::getJudgementData() {
    // init
    mWDJData.range_Y1 = 0.0;
    mWDJData.range_Y2 = 0.0;
    mWDJData.range_Xr = 0.0;
    mWDJData.range_Xt = 0.0;
    mWDJData.range_IPD = 0.0;

    // read judgement data
    ifstream infile(WEARING_DETECTION_DATA_FILE_PATH);
    if (infile.is_open()) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "load data from %s", WEARING_DETECTION_DATA_FILE_PATH);

        stringstream buffer;
        buffer << infile.rdbuf();
        infile.close();

        string strJData = buffer.str();
        Document doc;
        doc.Parse(strJData.c_str());
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "strJData %s", strJData.c_str());

        if (doc.HasMember("Y1")) {
            mWDJData.range_Y1 = doc["Y1"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get Y1 %f", mWDJData.range_Y1);
        }
        if (doc.HasMember("Y2")) {
            mWDJData.range_Y2 = doc["Y2"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get Y2 %f", mWDJData.range_Y2);
        }
        if (doc.HasMember("X")) {
            mWDJData.range_Xr = doc["Xr"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get Xr %f", mWDJData.range_Xr);
        }
        if (doc.HasMember("X_diff")) {
            mWDJData.range_Xt = doc["Xt"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get Xt %f", mWDJData.range_Xt);
        }
        if (doc.HasMember("IPD")) {
            mWDJData.range_IPD = doc["IPD"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get IPD %f", mWDJData.range_IPD);
        }
    }

    // set default
    if ((mWDJData.range_Y1 <= 0.001)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Y1 use default value");

        mWDJData.range_Y1 = 0.15;
    }
    if ((mWDJData.range_Y2 <= 0.001)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Y2 use default value");

        mWDJData.range_Y2 = 0.1;
    }
    if ((mWDJData.range_Xr <= 0.001)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "X use default value");

        mWDJData.range_Xr = 0.05;
    }
    if ((mWDJData.range_Xt <= 0.001)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Xt use default value");

        mWDJData.range_Xt = 0.03;
    }
    if ((mWDJData.range_IPD <= 0.001)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "IPD use default value");

        mWDJData.range_IPD = 2.0;
    }

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Judgement Data: range_Y1 %f range_Y2 %f range_Xr %f range_Xt %f range_IPD %f", mWDJData.range_Y1, mWDJData.range_Y2,
             mWDJData.range_Xr, mWDJData.range_Xt, mWDJData.range_IPD);
}

void WearingDetection::getCalibrationData() {
    // init
    mWDCData.left_X1 = IDEAL_EYE_POSITION_X;
    mWDCData.left_Ax1 = 0.0;
    mWDCData.left_X2 = IDEAL_EYE_POSITION_X;
    mWDCData.left_Ax2 = 0.0;
    mWDCData.right_X1 = IDEAL_EYE_POSITION_X;
    mWDCData.right_Ax1 = 0.0;
    mWDCData.right_X2 = IDEAL_EYE_POSITION_X;
    mWDCData.right_Ax2 = 0.0;

    // read judgement data
    ifstream infile(WEARING_DETECTION_DATA_FILE_PATH);
    if (infile.is_open()) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "load data from %s", WEARING_DETECTION_DATA_FILE_PATH);

        stringstream buffer;
        buffer << infile.rdbuf();
        infile.close();

        string strJData = buffer.str();
        Document doc;
        doc.Parse(strJData.c_str());
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "strJData %s", strJData.c_str());

        if (doc.HasMember("left_X1")) {
            mWDCData.left_X1 = doc["left_X1"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get left_X1 %f", mWDCData.left_X1);
        }
        if (doc.HasMember("left_Ax1")) {
            mWDCData.left_Ax1 = doc["left_Ax1"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get left_Ax1 %f", mWDCData.left_Ax1);
        }
        if (doc.HasMember("left_X2")) {
            mWDCData.left_X2 = doc["left_X2"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get left_X2 %f", mWDCData.left_X2);
        }
        if (doc.HasMember("left_Ax2")) {
            mWDCData.left_Ax2 = doc["left_Ax2"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get left_Ax2 %f", mWDCData.left_Ax2);
        }
        if (doc.HasMember("right_X1")) {
            /* string temp = doc["right_X1"].GetString();
            mWDCData.right_X1 = strtof(temp.c_str(), nullptr); */
            mWDCData.right_X1 = doc["right_X1"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get right_X1 %f", mWDCData.right_X1);
        }
        if (doc.HasMember("right_Ax1")) {
            mWDCData.right_Ax1 = doc["right_Ax1"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get right_Ax1 %f", mWDCData.right_Ax1);
        }
        if (doc.HasMember("right_X2")) {
            mWDCData.right_X2 = doc["right_X2"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get right_X2 %f", mWDCData.right_X2);
        }
        if (doc.HasMember("right_Ax2")) {
            mWDCData.right_Ax2 = doc["right_Ax2"].GetFloat();
            DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "get right_Ax2 %f", mWDCData.right_Ax2);
        }
    }

    // set default
    if ((mWDCData.left_X1 < 0.2 || mWDCData.left_X1 > 0.8)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "left_X1 use default value");

        mWDCData.left_X1 = IDEAL_EYE_POSITION_X;
    }
    if ((mWDCData.left_Ax1 <= 70.0 || mWDCData.left_Ax1 >= 110.0)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "left_Ax1 use default value");

        mWDCData.left_Ax1 = IDEAL_EYE_POSITION_X_COEF_A;
    }
    if ((mWDCData.left_X2 < 0.2 || mWDCData.left_X2 > 0.8)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "left_X2 use default value");

        mWDCData.left_X2 = IDEAL_EYE_POSITION_X;
    }
    if ((mWDCData.left_Ax2 <= 70.0 || mWDCData.left_Ax2 >= 110.0)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "left_Ax2 use default value");

        mWDCData.left_Ax2 = IDEAL_EYE_POSITION_X_COEF_A;
    }
    if ((mWDCData.right_X1 < 0.2 || mWDCData.right_X1 > 0.8)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "right_X1 use default value");

        mWDCData.right_X1 = IDEAL_EYE_POSITION_X;
    }
    if ((mWDCData.right_Ax1 <= 70.0 || mWDCData.right_Ax1 >= 110.0)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "right_Ax1 use default value");

        mWDCData.right_Ax1 = IDEAL_EYE_POSITION_X_COEF_A;
    }
    if ((mWDCData.right_X2 < 0.2 || mWDCData.right_X2 > 0.8)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "right_X2 use default value");

        mWDCData.right_X2 = IDEAL_EYE_POSITION_X;
    }
    if ((mWDCData.right_Ax2 <= 70.0 || mWDCData.right_Ax2 >= 110.0)) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "right_Ax2 use default value");

        mWDCData.right_Ax2 = IDEAL_EYE_POSITION_X_COEF_A;
    }

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Calibration Data: left_X1 %f left_Ax1 %f left_X2 %f left_Ax2 %f right_X1 %f right_Ax1 %f right_X2 %f right_Ax2 %f",
             mWDCData.left_X1, mWDCData.left_Ax1, mWDCData.left_X2, mWDCData.left_Ax2, mWDCData.right_X1, mWDCData.right_Ax1, mWDCData.right_X2, mWDCData.right_Ax2);
}

void WearingDetection::getParam() {
    mWDParam.collection_frequency = ET_DATA_COLLECTION_FREQUENCY;
    mWDParam.collection_percentage = ET_DATA_COLLECTION_PERCENTAGE;
    mWDParam.collection_duration = ET_DATA_COLLECTION_DURATION;

    // read param data
    ifstream infile(WEARING_DETECTION_DATA_FILE_PATH);
    if (infile.is_open()) {
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "load data from %s", WEARING_DETECTION_DATA_FILE_PATH);

        stringstream buffer;
        buffer << infile.rdbuf();
        infile.close();

        string strJData = buffer.str();
        Document doc;
        doc.Parse(strJData.c_str());
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "strParam %s", strJData.c_str());

        if (doc.HasMember("CoFr")) {
            mWDParam.collection_frequency = doc["CoFr"].GetFloat();
        }
        if (doc.HasMember("CoPe")) {
            mWDParam.collection_percentage = doc["CoPe"].GetFloat();
        }
        if (doc.HasMember("CoDu")) {
            mWDParam.collection_duration = doc["CoDu"].GetFloat();
        }
    }

    // get real frequency
    mWDParam.collection_frequency = ET_DATA_COLLECTION_FREQUENCY;

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "Param: CoFr %f CoPe %f CoDu %f", mWDParam.collection_frequency, mWDParam.collection_percentage,
             mWDParam.collection_duration);
}

void WearingDetection::clearWDData(EyeData& wdData) { return edc.clearEyeData(wdData); }

bool WearingDetection::isWDDataValid(EyeData& wdData) { return edc.isEyeDataValid(wdData); }

int WearingDetection::collectingEyeTrackingData(float frequency, float duration, float percentage, EyeData& wdData) {
    CollectionInfo ci = {frequency, duration, percentage};
    auto result = edc.collectingAsync(ci, wdData);
    int ret = result.get();
    switch (ret) {
        case EYE_DATA_RESULT_FORCE_STOP:
            ret = WEARING_DETECTION_RESULT_FORCE_STOP;
            break;
        case EYE_DATA_RESULT_INTERFACE_FAILURE:
            ret = WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED;
            break;
        case EYE_DATA_RESULT_FAILURE:
            ret = WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED;
            break;
        case EYE_DATA_RESULT_OK:
            ret = WEARING_DETECTION_RESULT_OK;
            break;
        default:
            ret = WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED;
            break;
    }

    return ret;
}

void WearingDetection::stopCollectingEyeTrackingData() { edc.stopCollecting(); }

int WearingDetection::checkPositionY(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, bool isRoughCheck) {
    bool testResult = false;
    float range_Y = 0.0;

    if (isRoughCheck) {
        range_Y = wdJData.range_Y1;
    } else {
        range_Y = wdJData.range_Y2;
    }

    if (wdData.left_pupil_position_Y < INVALID_EYE_POSITION_Y || wdData.left_pupil_position_Y > (1 - INVALID_EYE_POSITION_Y) || wdData.right_pupil_position_Y < INVALID_EYE_POSITION_Y ||
        wdData.right_pupil_position_Y > (1 - INVALID_EYE_POSITION_Y)) {
        // we assume position is poor and need to adjust hmd
        DLOGI(WearingDetection::mModuleName.c_str(), "bad position_Y");

        return WEARING_DETECTION_RESULT_FAILED;
    } else {
        testResult = is_inside(wdData.left_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "testResult left %d, position_Y %f, range_Y %f", testResult, wdData.left_pupil_position_Y, range_Y);
        testResult &= is_inside(wdData.right_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y);
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "testResult left&right %d, position_Y %f, range_Y %f", testResult, wdData.right_pupil_position_Y, range_Y);
    }

    if (testResult) {
        return WEARING_DETECTION_RESULT_Y_GOOD;
    } else {
        if ((is_too_low(wdData.left_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y) && is_too_high(wdData.right_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y)) ||
            (is_too_high(wdData.left_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y) && is_too_low(wdData.right_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y))) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Shouldn't happen.");
        } else if (is_too_low(wdData.left_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y) || is_too_low(wdData.right_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y)) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "case 1");
            return WEARING_DETECTION_RESULT_Y_UPWARDS;
        } else if (is_too_high(wdData.left_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y) || is_too_high(wdData.right_pupil_position_Y, IDEAL_EYE_POSITION_Y, range_Y)) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "case 2");
            return WEARING_DETECTION_RESULT_Y_DOWNWARDS;
        }
    }

    return WEARING_DETECTION_RESULT_FAILED;
}

int WearingDetection::checkPositionX(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, bool isRoughCheck) {
    bool testResultL = false;
    bool testResultR = false;
    float range_X = 0.0;
    if (isRoughCheck) {
        range_X = wdJData.range_Xr;
    } else {
        range_X = wdJData.range_Xt;
    }

    float left_X = (wdCData.left_X1 + wdCData.left_X2) / 2;
    float right_X = (wdCData.right_X1 + wdCData.right_X2) / 2;
    float l_diff = get_diff(wdData.left_pupil_position_X, left_X);
    float r_diff = get_diff(wdData.right_pupil_position_X, right_X);
    float lr_diff = get_diff(l_diff, r_diff);

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "l_x %f, l_ideal_x %f, r_x %f, r_ideal_x %f", wdData.left_pupil_position_X, left_X, wdData.right_pupil_position_X,
             right_X);
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "l_diff %f, r_diff %f, lr_diff %f, range_X %f", l_diff, r_diff, lr_diff, range_X);

    if (abs(lr_diff) > range_X) {
        float ipd = 0.0;
        if (getMotorDistance(ipd)) {
            if (lr_diff < 0) {
                if (abs(ipd - mops.MOTOR_RANGE.max) <= kMOTOR_RANGE_TOLERENCE.max) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Cannot move motor outwards any more.");
                    return WEARING_DETECTION_RESULT_X_GOOD;
                }

                return WEARING_DETECTION_RESULT_X_OUTWARDS;
            } else if (lr_diff > 0) {
                if (abs(ipd - mops.MOTOR_RANGE.min) <= kMOTOR_RANGE_TOLERENCE.min) {
                    DLOGI_IF(mDebugLogFlags.infoEnabled, WearingDetection::mModuleName.c_str(), "Cannot move motor inwards any more.");
                    return WEARING_DETECTION_RESULT_X_GOOD;
                }

                return WEARING_DETECTION_RESULT_X_INWARDS;
            } else {
                return WEARING_DETECTION_RESULT_X_GOOD;
            }
        }
    } else {
        return WEARING_DETECTION_RESULT_X_GOOD;
    }

    return WEARING_DETECTION_RESULT_FAILED;
}

int WearingDetection::reCheckPositionXForBizMode(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData) {
    float ipd;
    if (getMotorDistance(ipd)) {
        float len;
        calculateIPDMoving(wdData, mWDJData, mWDCData, len);
        float targetIpd = ipd + len;
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "current ipd is %f, target ipd is %f", ipd, targetIpd);
        int currLevel = getEyeTrackingPosLevel(ipd);
        int targetLevel = getEyeTrackingPosLevel(targetIpd);
        if (currLevel == targetLevel) {
            return WEARING_DETECTION_RESULT_X_GOOD;
        } else if (currLevel < targetLevel) {
            return WEARING_DETECTION_RESULT_X_OUTWARDS;
        } else if (currLevel > targetLevel) {
            return WEARING_DETECTION_RESULT_X_INWARDS;
        }
    }

    return WEARING_DETECTION_RESULT_FAILED;
}

int WearingDetection::compareIPD(const float ipd, const WearingDetectionJudegment& wdJData, float& diff) {
    float lastIpd = getLastIPD();

    if (is_inside(ipd, lastIpd, wdJData.range_IPD)) {
        diff = 0;
        return WEARING_DETECTION_RESULT_X_GOOD;
    } else if (is_too_low(ipd, lastIpd, wdJData.range_IPD)) {
        diff = get_diff(ipd, lastIpd);
        return WEARING_DETECTION_RESULT_X_OUTWARDS;
    } else if (is_too_high(ipd, lastIpd, wdJData.range_IPD)) {
        diff = get_diff(ipd, lastIpd);
        return WEARING_DETECTION_RESULT_X_INWARDS;
    }

    return WEARING_DETECTION_RESULT_FAILED;
}

int WearingDetection::calculateIPDMoving(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, float& len) {
    int ret = WEARING_DETECTION_RESULT_X_GOOD;

    float l_diff1 = get_diff(wdData.left_pupil_position_X, wdCData.left_X1);
    float r_diff1 = get_diff(wdData.right_pupil_position_X, wdCData.right_X1);
    float l_diff2 = get_diff(wdData.left_pupil_position_X, wdCData.left_X2);
    float r_diff2 = get_diff(wdData.right_pupil_position_X, wdCData.right_X2);
    float lr_diff1 = get_diff(l_diff1, r_diff1) / 2;
    float lr_diff2 = get_diff(l_diff2, r_diff2) / 2;
    int dir = MOTOR_MOVING_DIR_NO_MOVING;

    if (lr_diff1 < 0) {
        dir = MOTOR_MOVING_DIR_OUTWARDS;
        normalizedDiff2MotorStep(lr_diff1, len);

        ret = WEARING_DETECTION_RESULT_X_OUTWARDS;
    } else if (lr_diff2 > 0) {
        dir = MOTOR_MOVING_DIR_INWARDS;
        normalizedDiff2MotorStep(lr_diff2, len);

        ret = WEARING_DETECTION_RESULT_X_INWARDS;
    } else {
        dir = MOTOR_MOVING_DIR_NO_MOVING;

        ret = WEARING_DETECTION_RESULT_X_GOOD;
    }

    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "l_x %f, l_ideal_x1 %f, l_ideal_x2 %f, r_x %f, r_ideal_x1 %f, r_ideal_x2 %f", wdData.left_pupil_position_X,
             wdCData.left_X1, wdCData.left_X2, wdData.right_pupil_position_X, wdCData.right_X1, wdCData.right_X2);
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "l_diff1 %f, l_diff2 %f, r_diff1 %f, r_diff2 %f, lr_diff1 %f, lr_diff2 %f", l_diff1, l_diff2, r_diff1, r_diff2,
             lr_diff1, lr_diff2);
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "dir %d, len %f", dir, len);

    return ret;
}

int WearingDetection::normalizedDiff2MotorStep(const float diff, float& len) {
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "left_Ax1 %f, left_Ax2 %f, right_Ax1 %f, right_Ax2 %f", mWDCData.left_Ax1, mWDCData.left_Ax2, mWDCData.right_Ax1,
             mWDCData.right_Ax2);
    if (diff < 0) {
        len = -diff * (mWDCData.left_Ax1 + mWDCData.right_Ax1) / 2.0;
    } else {
        len = -diff * (mWDCData.left_Ax2 + mWDCData.right_Ax2) / 2.0;
    }

    return WEARING_DETECTION_RESULT_OK;
}

int WearingDetection::checkLensMounting() {
    int iRet = WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED;

    if (mIsTrackingApiReady) {
        int apiRet = TRACKINGAPI_CheckDiopterLensMounting();
        DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "TRACKINGAPI_CheckDiopterLensMounting result: %d", apiRet);
        if (apiRet >= 0) {
            if (apiRet == 0) {
                iRet = WEARING_DETECTION_RESULT_LENS_MOUNTING_GOOD;
            } else if (apiRet & 0x01 && apiRet & 0x02) {
                iRet = WEARING_DETECTION_RESULT_ALL_LENS_MOUNTING_BAD;
            } else if (apiRet & 0x01) {
                iRet = WEARING_DETECTION_RESULT_LEFT_LENS_MOUNTING_BAD;
            } else if (apiRet & 0x02) {
                iRet = WEARING_DETECTION_RESULT_RIGHT_LENS_MOUNTING_BAD;
            }
        } else if (apiRet == -3) {
            DLOGE(WearingDetection::mModuleName.c_str(), "TRACKINGAPI_CheckDiopterLensMounting failed.");
            iRet = WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED;
        } else {
            DLOGE(WearingDetection::mModuleName.c_str(), "TRACKINGAPI_CheckDiopterLensMounting failed.");
            iRet = WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED;
        }
    }

    return iRet;
}

int WearingDetection::calculateLensConfig(float ipd, tobii_lens_configuration_t& lens_config) {
    const float IPD_DEFAULT = 64.0;

    float offset = (ipd - IPD_DEFAULT) / 2;

    lens_config.left_xyz[0] = IPD_DEFAULT / 2 + offset;
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "ipd %f, left_x %f", ipd, lens_config.left_xyz[0]);
    lens_config.right_xyz[0] = -IPD_DEFAULT / 2 - offset;
    DLOGD_IF(mDebugLogFlags.debugEnabled, WearingDetection::mModuleName.c_str(), "ipd %f, right_x %f", ipd, lens_config.right_xyz[0]);

    return WEARING_DETECTION_RESULT_OK;
}

void WearingDetection::loadProjectMode() { mProjectMode = getYVRProjectBuildSpecType(); }

}  // end namespace yvr
}  // end namespace android
