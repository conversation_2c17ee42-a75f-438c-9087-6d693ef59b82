#include "BaseOps.h"
#include "MotorOps.h"

class IPDOps : public BaseOps {
 public:
    IPDOps();
    ~IPDOps();

 public:
    bool getIpdConfig(float& ipd);       // unit: mm
    bool setIpdConfig(const float ipd);  // unit: mm
    bool getIpd(float& ipd);             // unit: mm
    bool setIpd(const float ipd);        // unit: mm
    bool moveIpd(const float offset);    // unit: mm

 public:
    const Range& IPD_RANGE = IPDRange;

 private:
    bool getIpdConfigRange(float& min, float& max);     // unit: mm
    inline float limitIPDConfig(const float distance);  // unit: mm

 private:
    Range IPDRange;
    MotorOps mops;
};