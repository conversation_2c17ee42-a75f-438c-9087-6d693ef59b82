#include <thread>
#include <string>
#include <mutex>
#include <shared_mutex>
#include <cutils/properties.h>

#include "yvrutils/DebugLog.h"
#include "yvrutils/extModule.h"
#include "yvrutils/yvrProperties.h"

#include "extModuleWearingDetection.h"
#include "WearingDetection.h"

using namespace android;
using namespace yvr;

#define MODULE_NAME "WearingDetection"

#define EXT_MODULE_PUBLIC __attribute__((visibility("default")))
#define EXT_MODULE_LOCAL __attribute__((visibility("hidden")))

EXT_MODULE_ERROR getModuleName(std::string& moduleName);
EXT_MODULE_ERROR initModule(const void* input, void* output);
EXT_MODULE_ERROR deinitModule();
EXT_MODULE_ERROR startModule(const void* input, void* output);
EXT_MODULE_ERROR stopModule();
EXT_MODULE_ERROR pauseModule();
EXT_MODULE_ERROR resumeModule();
EXT_MODULE_ERROR getModuleStatus(void* status);
EXT_MODULE_ERROR moduleControl(const int cmd, const Cmd_Param param, Cmd_Param in_out);
extern "C" EXT_MODULE_ERROR extModuleEntry(extModuleOps& ops);

EXT_MODULE_ERROR getModuleStatus(WEARING_DETECTION_STATUS& status);
EXT_MODULE_ERROR setModuleStatus(const WEARING_DETECTION_STATUS status);

const char* extModuleAPIName[] = {
    "getModuleName", "initModule", "deinitModule", "startModule", "stopModule", "pauseModule", "resumeModule", "getModuleStatus", "moduleControl", "extModuleEntry",
};

static bool gIsNewThread = false;
static std::thread* gWorkThread = NULL;
static std::shared_mutex mutex_;

static Debug_Log_Flags g_debugLogFlags;

static WEARING_DETECTION_STATUS gStatus = WEARING_DETECTION_STATUS_UNINITIALIZED;
// static WearingDetection* gWDModuleSinglton = NULL;
static WEARING_DETECTION_MULTI_STAGES_MODE gMultiStagesMode = WEARING_DETECTION_MULTI_STAGES_MODE_DEFAULT;
static uint32_t gLastWorkingStageIndex = 0;
static interAction_callback gIACB = NULL;

void waitForJobDone() {
    WEARING_DETECTION_STATUS currStatus;
    while (1) {
        getModuleStatus(currStatus);
        if (WEARING_DETECTION_STATUS_INITIALIZED != currStatus) {
            usleep(100);
        } else {
            break;
        }
    }
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR getModuleName(std::string& moduleName) {
    moduleName = MODULE_NAME;

    return EXT_MODULE_ERROR_NONE;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR initModule(const void* input, void* output) {
    if (NULL == output) return EXT_MODULE_ERROR_INVALID_PARAM;
    (void)input;

    WD_Module_Init_Output_Param* wd_output = (WD_Module_Init_Output_Param*)output;

    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    if (WEARING_DETECTION_STATUS_UNINITIALIZED == currStatus) {
        setModuleStatus(WEARING_DETECTION_STATUS_INITIALIZED);

        ret = EXT_MODULE_ERROR_NONE;
    } else {
        ret = EXT_MODULE_ERROR_ALREADY_DONE;
    }

    getModuleStatus((void*)&wd_output->output_param.module_status);

    // init log flags
    int32_t logMask = property_get_int32(PROPERTY_WEARINGDETECTION_LOG_MASK, 0);
    parseLogMask(logMask, &g_debugLogFlags);

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR deinitModule() {
    stopModule();
    setModuleStatus(WEARING_DETECTION_STATUS_UNINITIALIZED);
    return EXT_MODULE_ERROR_NONE;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR startModule(const void* input, void* output) {
    if (NULL == input || NULL == output) return EXT_MODULE_ERROR_INVALID_PARAM;

    WD_Module_Start_Input_Param* wd_input = (WD_Module_Start_Input_Param*)input;
    WD_Module_Start_Output_Param* wd_output = (WD_Module_Start_Output_Param*)output;

    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    if (WEARING_DETECTION_STATUS_INITIALIZED == currStatus) {
        setModuleStatus(WEARING_DETECTION_STATUS_STARTING);

        gIsNewThread = wd_input->new_thread;
        gIACB = wd_input->ia_cb;
        gMultiStagesMode = wd_input->multi_stages_mode;

        if (gIsNewThread) {
            gWorkThread = new std::thread(WearingDetection::job, gMultiStagesMode, gIACB, gLastWorkingStageIndex);
        } else {
            WearingDetection::job(gMultiStagesMode, gIACB);
        }

        ret = EXT_MODULE_ERROR_NONE;
    } else if (WEARING_DETECTION_STATUS_STARTING == currStatus || WEARING_DETECTION_STATUS_WORKING_PAUSE || WEARING_DETECTION_STATUS_WORKING) {
        ret = EXT_MODULE_ERROR_BUSY;
    } else {
        ret = EXT_MODULE_ERROR_NO_SUPPORT;
    }

    getModuleStatus((void*)&wd_output->output_param.module_status);

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR stopModule() {
    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    if (WEARING_DETECTION_STATUS_STARTING == currStatus || WEARING_DETECTION_STATUS_WORKING_PAUSE || WEARING_DETECTION_STATUS_WORKING) {
        WearingDetection::stopJob();
        waitForJobDone();

        setModuleStatus(WEARING_DETECTION_STATUS_INITIALIZED);

        ret = EXT_MODULE_ERROR_NONE;
    } else {
        ret = EXT_MODULE_ERROR_NO_SUPPORT;
    }

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR pauseModule() {
    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    if (WEARING_DETECTION_STATUS_WORKING == currStatus) {
        gLastWorkingStageIndex = WearingDetection::getCurrentStageIndex();
        stopModule();

        setModuleStatus(WEARING_DETECTION_STATUS_WORKING_PAUSE);
        ret = EXT_MODULE_ERROR_NONE;
    } else {
        // setModuleStatus(WEARING_DETECTION_STATUS_PAUSE);

        ret = EXT_MODULE_ERROR_NO_SUPPORT;
    }

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR resumeModule() {
    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    if (WEARING_DETECTION_STATUS_WORKING_PAUSE == currStatus) {
        if (gIsNewThread) {
            gWorkThread = new std::thread(WearingDetection::job, gMultiStagesMode, gIACB, gLastWorkingStageIndex);
        } else {
            WearingDetection::job(gMultiStagesMode, gIACB);
        }
        setModuleStatus(WEARING_DETECTION_STATUS_WORKING);
        ret = EXT_MODULE_ERROR_NONE;
    } else {
        // setModuleStatus(WEARING_DETECTION_STATUS_INITIALIZED);
        ret = EXT_MODULE_ERROR_NO_SUPPORT;
    }

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR getModuleStatus(WEARING_DETECTION_STATUS& status) {
    std::shared_lock<std::shared_mutex> lock(mutex_);

    status = gStatus;

    return EXT_MODULE_ERROR_NONE;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR setModuleStatus(const WEARING_DETECTION_STATUS status) {
    std::unique_lock<std::shared_mutex> lock(mutex_);

    gStatus = status;

    return EXT_MODULE_ERROR_NONE;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR getModuleStatus(void* status) {
    std::shared_lock<std::shared_mutex> lock(mutex_);

    WEARING_DETECTION_STATUS s;
    EXT_MODULE_ERROR ret = getModuleStatus(s);
    ((WD_Module_Status*)status)->status = s;

    return ret;
}

EXT_MODULE_LOCAL EXT_MODULE_ERROR moduleControl(const int cmd, const Cmd_Param param, Cmd_Param in_out) {
    EXT_MODULE_ERROR ret = EXT_MODULE_ERROR_FAILED;
    WEARING_DETECTION_STATUS currStatus;
    getModuleStatus(currStatus);

    WEARING_DETECTION_CMD wdCmd = (WEARING_DETECTION_CMD)cmd;

    if (WEARING_DETECTION_STATUS_INITIALIZED == currStatus) {
        switch (cmd) {
            case WEARING_DETECTION_CMD_MOVE_IPD: {
                int steps = static_cast<int>(param);
                double offset = steps / 25.0 * 10.0;  // 25 steps -> 10.0 mm
                DLOGD_IF(g_debugLogFlags.debugEnabled, MODULE_NAME, "offset %f", offset);

                WearingDetection::moveIPD(offset);

                ret = EXT_MODULE_ERROR_NONE;

                break;
            }
            case WEARING_DETECTION_CMD_MOVE_IPD_TO_POSITION: {
                float distance = static_cast<float>(param);
                DLOGD_IF(g_debugLogFlags.debugEnabled, MODULE_NAME, "distance %f", distance);

                WearingDetection::setIPD(distance);

                ret = EXT_MODULE_ERROR_NONE;

                break;
            }
            case WEARING_DETECTION_CMD_LOAD_ET_CALIBRATION_DATA: {
                WearingDetection::applyEyeTrackingCalData();

                ret = EXT_MODULE_ERROR_NONE;

                break;
            }
            case WEARING_DETECTION_CMD_STAGE_ACTION: {
                setModuleStatus(WEARING_DETECTION_STATUS_STARTING);

                WEARING_DETECTION_STAGE stage = static_cast<WEARING_DETECTION_STAGE>(param);
                interAction_callback ia_cb = reinterpret_cast<interAction_callback>(in_out);
                DLOGD_IF(g_debugLogFlags.debugEnabled, MODULE_NAME, "stage %d, ia_cb %p", stage, ia_cb);

                gWorkThread = new std::thread(WearingDetection::job_s, stage, ia_cb);
                gWorkThread->detach();

                ret = EXT_MODULE_ERROR_NONE;

                break;
            }
            case WEARING_DETECTION_CMD_SWEEP_FOR_POSITION_GUIDE: {
                ret = EXT_MODULE_ERROR_NONE;
                WearingDetection::sweepMotorAndSaveETData();

                break;
            }
            case WEARING_DETECTION_CMD_GET_DATA: {
                WEARING_DETECTION_DATA_TYPE type = static_cast<WEARING_DETECTION_DATA_TYPE>(param);
                if (type == WEARING_DETECTION_DATA_TYPE_PG) {
                    WD_Module_CMD_GET_DATA_Output_Param* data = reinterpret_cast<WD_Module_CMD_GET_DATA_Output_Param*>(in_out);
                    data->type = WEARING_DETECTION_DATA_TYPE_PG;

                    bool bRet = WearingDetection::getCurrentPGData(data->pg_data.left_pg_X, data->pg_data.left_pg_Y, data->pg_data.right_pg_X, data->pg_data.right_pg_Y);
                    if (bRet) {
                        ret = EXT_MODULE_ERROR_NONE;
                    } else {
                        ret = EXT_MODULE_ERROR_FAILED;
                    }
                }
                break;
            }
            case WEARING_DETECTION_CMD_FORCE_STOP: {
                WearingDetection::stopJob();

                waitForJobDone();

                break;
            }
            default:
                break;
        }

        ret = EXT_MODULE_ERROR_NONE;
    } else if (WEARING_DETECTION_STATUS_STARTING == currStatus || WEARING_DETECTION_STATUS_WORKING_PAUSE || WEARING_DETECTION_STATUS_WORKING) {
        ret = EXT_MODULE_ERROR_BUSY;
    } else {
        ret = EXT_MODULE_ERROR_NO_SUPPORT;
    }

    return ret;
}

extern "C" EXT_MODULE_PUBLIC EXT_MODULE_ERROR extModuleEntry(extModuleOps& ops) {
    ops.getModuleName = getModuleName;
    ops.initModule = initModule;
    ops.deinitModule = deinitModule;
    ops.startModule = startModule;
    ops.stopModule = stopModule;
    ops.pauseModule = pauseModule;
    ops.resumeModule = resumeModule;
    ops.getModuleStatus = getModuleStatus;
    ops.moduleControl = moduleControl;

    return EXT_MODULE_ERROR_NONE;
}
