#include <stdlib.h>
#include <cutils/properties.h>

#include "MotorOps.h"

#define MODULE_NAME "MOTOR_OPS"

#define KEY_IPD_MAX "persist.yvr.ipd.max"
#define KEY_IPD_MIN "persist.yvr.ipd.min"

MotorOps::MotorOps() { getMotorDistanceRange(MotorRange.min, MotorRange.max); }

MotorOps::~MotorOps() {}

bool MotorOps::getMotorDistance(float& distance) {
    bool bRet = false;
    if (mIsTrackingApiReady) {
        float ipd_meters[3];
        bRet = (0 == TRACKINGAPI_GetSystemPropertyFloatArray(kIpdFrustumProp, ipd_meters, 3));
        if (bRet) {
            distance = ipd_meters[0] * 1000.0f;
            distance = motorDistanceCorrection(distance);
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "distance %f.", distance);
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_GetSystemPropertyFloatArray failed.");
        }
    }

    return bRet;
}

bool MotorOps::setMotorDistance(const float&& distance) {
    bool bRet = false;

    if (mIsTrackingApiReady) {
        float corrD = motorDistanceCorrection(distance);
        int ret = TRACKINGAPI_SetSystemPropertyFloat(kIpdValuesProp, corrD / 1000.0);  // mm -> m
        if (0 == ret) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Set motor distance successfully");
            bRet = true;
        } else {
            DLOGE(MODULE_NAME, "Call TRACKINGAPI_SetSystemPropertyFloat return: %d", ret);
        }
    }

    return bRet;
}

bool MotorOps::setMotorDistance(float& distance) {
    bool bRet = false;

    if (mIsTrackingApiReady && setMotorDistance(std::move(distance))) {
        bRet = getMotorDistance(distance);
    }

    return bRet;
}

bool MotorOps::setMotorDistanceForET(float& distance) {
    bool bRet = false;

    if (mIsTrackingApiReady && setMotorDistance(distance)) {
        // update lens configuration
        int ret = TRACKINGAPI_SetLensConfig(distance);
        if (ret == 0) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Set lens config successfully");
            bRet = true;
        } else if (ret == -3) {
            DLOGE(MODULE_NAME, "Failed to set lens config");
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetLensConfig failed.");
        }
    }

    return bRet;
}

bool MotorOps::setMotorDistanceForIPD(float& distance) {
    bool bRet = false;

    if (mIsTrackingApiReady && setMotorDistanceForET(distance)) {
        // update ipd configuration
        IPDConfiguration config = {.currIPD = distance};
        bRet = (0 == TRACKINGAPI_SetIPDConfiguration(&config));
        if (bRet) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "config.currIPD %f.", distance);
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetIPDConfiguration failed.");
        }
    }

    return bRet;
}

bool MotorOps::moveMotor(const float offset) {
    bool bRet = false;

    if (mIsTrackingApiReady) {
        DirectionTA direction;
        const float abs_offset = abs(offset);
        if (abs_offset < 1e-1) {  // == 0
            return true;
        } else if (offset > 0) {
            direction = DIRECTION_OUTER;
        } else if (offset < 0) {
            direction = DIRECTION_INNER;
        }

        DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "offset %f, direction %d", abs_offset, direction);
        int ret = TRACKINGAPI_MoveMotor(abs_offset, direction);
        if (0 == ret) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Move motor successfully");
            bRet = true;
        } else {
            DLOGE(MODULE_NAME, "Call TRACKINGAPI_MoveMotor return: %d", ret);
        }
    }

    return bRet;
}

bool MotorOps::moveMotorForET(const float offset) {
    bool bRet = false;

    float distance = 0.0;
    if (mIsTrackingApiReady && moveMotor(offset) && getMotorDistance(distance)) {
        // update lens configuration
        int ret = TRACKINGAPI_SetLensConfig(distance);
        if (ret == 0) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Set lens config successfully");
            bRet = true;
        } else if (ret == -3) {
            DLOGE(MODULE_NAME, "Failed to set lens config");
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetLensConfig failed.");
        }
    }

    return bRet;
}

bool MotorOps::moveMotorForIPD(const float offset) {
    bool bRet = false;

    float distance = 0.0;
    if (mIsTrackingApiReady && moveMotor(offset) && getMotorDistance(distance)) {
        // update lens configuration
        int ret = TRACKINGAPI_SetLensConfig(distance);
        if (ret == 0) {
            DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Set lens config successfully");
            bRet = true;
        } else if (ret == -3) {
            DLOGE(MODULE_NAME, "Failed to set lens config");
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetLensConfig failed.");
        }

        if (bRet) {
            bRet = false;
            // update ipd configuration
            IPDConfiguration config = {.currIPD = distance};
            bRet = (0 == TRACKINGAPI_SetIPDConfiguration(&config));
            if (bRet) {
                DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "config.currIPD %f.", distance);
            } else {
                DLOGE(MODULE_NAME, "TRACKINGAPI_SetIPDConfiguration failed.");
            }
        }
    }

    return bRet;
}

bool MotorOps::getMotorDistanceRange(float& min, float& max) {
    bool bRet = true;

    char buf[PROPERTY_VALUE_MAX + 10] = {'\0'};
    property_get(KEY_IPD_MIN, buf, "0.0");
    min = strtof(buf, nullptr);
    property_get(KEY_IPD_MAX, buf, "0.0");
    max = strtof(buf, nullptr);

    DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "min %f, max %f", min, max);

    return bRet;
}

float MotorOps::motorDistanceCorrection(const float distance) {
    float corr = distance;
    if (corr <= 59.4) {
        corr = 59.0;
    }
    if (corr >= 69.1) {
        corr = 69.5;
    }
    return corr;
}

float MotorOps::limitMotorDistance(const float distance) {
    float ret = distance;
    if (ret < MotorRange.min) ret = MotorRange.min;
    if (ret > MotorRange.max) ret = MotorRange.max;

    return ret;
}
