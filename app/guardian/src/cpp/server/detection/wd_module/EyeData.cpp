#include <inttypes.h>
#include <stdio.h>
#include <utils/Log.h>
#include <cutils/properties.h>

#include "screenlog/YvrSLog.h"

#include "EyeData.h"

#define MODULE_NAME "EYEDATA"
// #define SHOW_OVERLAY_INFO

EyeDataCollector::EyeDataCollector() : mForceStop(0) {}

EyeDataCollector::~EyeDataCollector() {}

std::future<int> EyeDataCollector::collectingAsync(const CollectionInfo& info, EyeData& eyeData) noexcept {
    mForceStop = false;
    return std::async(std::launch::async, [this, &info, &eyeData] { return collectingEyeTrackingData(info.frequency, info.duration, info.percentage, eyeData); });
}

void EyeDataCollector::stopCollecting() noexcept { mForceStop = true; }

bool EyeDataCollector::getEyeTrackingData(EyePoseStateTA& epState) {
    bool bRet = false;
    int ret;

    if (mIsTrackingApiReady) {
        int64_t ts;
        struct timeval tv;
        gettimeofday(&tv, NULL);
        int64_t currentTime = tv.tv_sec * 1000000 + tv.tv_usec;
        ret = TRACKINGAPI_GetEyeDataET(currentTime, &epState);
        DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "Call TRACKINGAPI_GetEyeDataET return: %d", ret);
        if (0 == ret) {
            bRet = true;
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "Left: flags %" PRIu64 ", PG_X %f, PG_Y %f, Right: flags %" PRIu64 ", PG_X %f, PG_Y %f, Z %f", epState.eye[0].flags,
                     epState.eye[0].positionGuide[0], epState.eye[0].positionGuide[1], epState.eye[1].flags, epState.eye[1].positionGuide[0], epState.eye[1].positionGuide[1],
                     epState.gazeOriginCombined[2]);
        }
    }

    return bRet;
}

bool EyeDataCollector::calulateEyeData(const EyePoseStateTA& epState, EyeData& eyeData) {
    bool bRet = false;
    bool bRetPGL = false;
    bool bRetPGR = false;
    bool bRetCmbOrigin = false;

    if ((epState.eye[ET_EYE_LEFT].flags & ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) == ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) {
        eyeData.left_pupil_position_X = epState.eye[ET_EYE_LEFT].positionGuide[0];
        eyeData.left_pupil_position_Y = epState.eye[ET_EYE_LEFT].positionGuide[1];
        bRetPGL = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid left eye Position Guide.");
#ifdef SHOW_OVERLAY_INFO
        YSLOGD_K(yvrutils::KEY_LINE_1, "left position guide: Invalid");
#endif
    }

    if ((epState.eye[ET_EYE_RIGHT].flags & ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) == ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) {
        eyeData.right_pupil_position_X = epState.eye[ET_EYE_RIGHT].positionGuide[0];
        eyeData.right_pupil_position_Y = epState.eye[ET_EYE_RIGHT].positionGuide[1];
        bRetPGR = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid right eye Position Guide.");
#ifdef SHOW_OVERLAY_INFO
        YSLOGD_K(yvrutils::KEY_LINE_2, "right position guide: Invalid");
#endif
    }

    if ((epState.flags & ET_GAZE_ORIGIN_COMBINED_VALID) == ET_GAZE_ORIGIN_COMBINED_VALID) {
        eyeData.combined_origin_Z = epState.gazeOriginCombined[2];
        bRetCmbOrigin = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid eye Position.");
#ifdef SHOW_OVERLAY_INFO
        YSLOGD_K(yvrutils::KEY_LINE_2, "combined eye position: Invalid");
#endif
    }

    if (bRetPGL && bRetPGR && bRetCmbOrigin) bRet = true;

    return bRet;
}

void EyeDataCollector::clearEyeData(EyeData& eyeData) {
    DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Clear WD Data");

    eyeData.left_pupil_position_X = 0.0;
    eyeData.left_pupil_position_Y = 0.0;
    eyeData.right_pupil_position_X = 0.0;
    eyeData.right_pupil_position_Y = 0.0;
    eyeData.combined_origin_Z = 0.0;
}

bool EyeDataCollector::isEyeDataValid(EyeData& eyeData) {
    bool bRet = false;

    if ((eyeData.left_pupil_position_X > 0.01 && eyeData.left_pupil_position_X < 0.99) && (eyeData.left_pupil_position_Y > 0.01 && eyeData.left_pupil_position_Y < 0.99) &&
        (eyeData.right_pupil_position_X > 0.01 && eyeData.right_pupil_position_X < 0.99) && (eyeData.right_pupil_position_Y > 0.01 && eyeData.right_pupil_position_Y < 0.99)) {
        bRet = true;
    }

    return bRet;
}

int EyeDataCollector::collectingEyeTrackingData(float frequency, float duration, float percentage, EyeData& eyeData) {
    int ret = EYE_DATA_RESULT_FAILURE;

    EyePoseStateTA epState;
    // pre-check eyetracking data
    if (!getEyeTrackingData(epState)) {
        DLOGE(MODULE_NAME, "Cannot get eyetracking data. Need to check eyetracking engine.\n");
        return EYE_DATA_RESULT_INTERFACE_FAILURE;
    }

    const uint32_t MAX_EYE_POSE_STATE_COUNT = frequency * duration * percentage;  // 90 tps * duration * 50%
    DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "MAX_EYE_POSE_STATE_COUNT %d.\n", MAX_EYE_POSE_STATE_COUNT);
    const uint32_t MAX_EYE_POSE_STATE_FAILURE_COUNT = MAX_EYE_POSE_STATE_COUNT;
    uint32_t eye_pose_state_count = 0;
    uint32_t eye_pose_state_failure_count = 0;
    EyeData eyeDataArr[MAX_EYE_POSE_STATE_COUNT];

    do {
        if (getEyeTrackingData(epState) && calulateEyeData(epState, eyeDataArr[eye_pose_state_count])) {
            eye_pose_state_count++;
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "eye_pose_state_count %d.\n", eye_pose_state_count);
            if (eye_pose_state_count >= MAX_EYE_POSE_STATE_COUNT) {
                ret = EYE_DATA_RESULT_OK;
                break;
            } else {
                usleep(11111);
            }
        } else {
            DLOGW(MODULE_NAME, "getEyeTrackingData failed.\n");
            eye_pose_state_failure_count++;
            if (eye_pose_state_failure_count >= MAX_EYE_POSE_STATE_FAILURE_COUNT) {
                break;
            }
            usleep(11111);
        }
    } while (eye_pose_state_count < MAX_EYE_POSE_STATE_COUNT && !mForceStop.load());

    if (mForceStop.load()) {
        ret = EYE_DATA_RESULT_FORCE_STOP;
        // throw OperationInterrupted();
    }

    if (EYE_DATA_RESULT_OK == ret) {
        DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Calculating  eyetracking data.\n");
        // average filter
        eyeData.left_pupil_position_X = 0.0;
        eyeData.right_pupil_position_X = 0.0;
        eyeData.left_pupil_position_Y = 0.0;
        eyeData.right_pupil_position_Y = 0.0;
        eyeData.combined_origin_Z = 0.0;
        for (uint32_t i = 0; i < MAX_EYE_POSE_STATE_COUNT; i++) {
            eyeData.left_pupil_position_X += eyeDataArr[i].left_pupil_position_X;
            eyeData.right_pupil_position_X += eyeDataArr[i].right_pupil_position_X;
            eyeData.left_pupil_position_Y += eyeDataArr[i].left_pupil_position_Y;
            eyeData.right_pupil_position_Y += eyeDataArr[i].right_pupil_position_Y;
            eyeData.combined_origin_Z += eyeDataArr[i].combined_origin_Z;
        }
        eyeData.left_pupil_position_X = eyeData.left_pupil_position_X / MAX_EYE_POSE_STATE_COUNT;
        eyeData.right_pupil_position_X = eyeData.right_pupil_position_X / MAX_EYE_POSE_STATE_COUNT;
        eyeData.left_pupil_position_Y = eyeData.left_pupil_position_Y / MAX_EYE_POSE_STATE_COUNT;
        eyeData.right_pupil_position_Y = eyeData.right_pupil_position_Y / MAX_EYE_POSE_STATE_COUNT;
        eyeData.combined_origin_Z = eyeData.combined_origin_Z / MAX_EYE_POSE_STATE_COUNT;
#ifdef SHOW_OVERLAY_INFO
        YSLOGD_K(yvrutils::KEY_LINE_1, "left position guide: x %f, y %f", eyeData.left_pupil_position_X, eyeData.left_pupil_position_Y);
        YSLOGD_K(yvrutils::KEY_LINE_2, "right position guide: x %f, y %f", eyeData.right_pupil_position_X, eyeData.right_pupil_position_Y);
        YSLOGD_K(yvrutils::KEY_LINE_3, "combined origin: z %f", eyeData.combined_origin_Z);
#endif
    }

    return ret;
}

// EyeDataCollector::OperationInterrupted::OperationInterrupted(): std::runtime_error("Operation interrupted by user request") {}
