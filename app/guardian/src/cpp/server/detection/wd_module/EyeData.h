#include <atomic>
#include <future>
#include <stdexcept>

#include "BaseOps.h"

typedef struct _EyeData {
    float left_pupil_position_X = 0.0f;   // normalized
    float right_pupil_position_X = 0.0f;  // normalized
    float left_pupil_position_Y = 0.0f;   // normalized
    float right_pupil_position_Y = 0.0f;  // normalized
    float combined_origin_Z = 0.0f;       // mm
} EyeData;

typedef struct _CollectionInfo {
    float frequency = 90.0f;
    float duration = 1.0f;
    float percentage = 0.5f;
} CollectionInfo;

enum EYE_DATA_RESULT {
    EYE_DATA_RESULT_FORCE_STOP = -3,
    EYE_DATA_RESULT_INTERFACE_FAILURE = -2,
    EYE_DATA_RESULT_FAILURE = -1,
    EYE_DATA_RESULT_OK = 0,
};

class EyeDataCollector : public BaseOps {
 public:
    EyeDataCollector();
    ~EyeDataCollector();

 public:
    std::future<int> collectingAsync(const CollectionInfo& info, EyeData& eyeData) noexcept;
    void stopCollecting() noexcept;
    void clearEyeData(EyeData& eyeData);
    bool isEyeDataValid(EyeData& eyeData);

 private:
    bool getEyeTrackingData(EyePoseStateTA& epState);
    bool calulateEyeData(const EyePoseStateTA& epState, EyeData& eyeData);
    int collectingEyeTrackingData(float frequency, float duration, float percentage, EyeData& eyeData);  // duration unit: second

    /* class OperationInterrupted : public std::runtime_error {
      public:
          OperationInterrupted();
    }; */

 private:
    std::atomic<bool> mForceStop{false};
};