#ifndef _WEARING_DETECTION_H
#define _WEARING_DETECTION_H

#include <atomic>
#include <string>
#include <vector>

#include "trackingapi/eyetracking_types.h"
#include "yvrutils/ProductUtils.h"
#include "yvrutils/extModule.h"
#include "yvrutils/DebugLog.h"
#include "TobiiSECImpl.h"
#include "extModuleWearingDetection.h"
#include "EyeData.h"
#include "MotorOps.h"
#include "IPDOps.h"

namespace android {
namespace yvr {

class WearingDetection {
 public:
    WearingDetection();
    ~WearingDetection();

    static WearingDetection* getInstance();
    static void job(const WEARING_DETECTION_MULTI_STAGES_MODE multi_stage_mode, const interAction_callback ia_cb, uint32_t startIndex = 0);
    static void job_s(const WEARING_DETECTION_STAGE job_stage, const interAction_callback ia_cb);
    static void stopJob();

    static WEARING_DETECTION_STAGE getCurrentStage();
    static uint32_t getCurrentStageIndex();
    static bool moveIPD(float offset);    // mm
    static bool setIPD(float targetPos);  // mm
    static bool applyEyeTrackingCalData();
    static void sweepMotorAndSaveETData();
    static bool getCurrentPGData(float& leftX, float& leftY, float& rightX, float& rightY);

 public:
    bool isFirstWearing();

 private:
    typedef struct WearingDetectionParams {
        float collection_frequency;   // same as eye camera fps
        float collection_percentage;  // 0.0 ~ 1.0
        float collection_duration;    // unit: second
    } WearingDetectionParams;

    typedef struct WearingDetectionJudegment {
        float range_Y1;   // normalized
        float range_Y2;   // normalized
        float range_Xr;   // normalized
        float range_Xt;   // normalized
        float range_IPD;  // mm
    } WearingDetectionJudegment;

    typedef struct WearingDetectionCalibration {
        float left_X1;
        float left_Ax1;
        float left_X2;
        float left_Ax2;
        float right_X1;
        float right_Ax1;
        float right_X2;
        float right_Ax2;
    } WearingDetectionCalibration;

 private:
    static std::string mModuleName;
    static WearingDetectionParams mWDParam;
    static WearingDetectionJudegment mWDJData;
    static WearingDetectionCalibration mWDCData;
    static std::vector<WEARING_DETECTION_STAGE> mTestStages;
    static uint8_t mCurrStageIndex;
    static uint8_t mLastStageIndex;
    // static WEARING_DETECTION_STAGE mStartStage;
    // static WEARING_DETECTION_STAGE mNextStage;
    static std::atomic<WEARING_DETECTION_STAGE> mCurrStage;

    static void prepareJob();
    static void jobEnds();
    static bool canJobRun();

 private:
    bool mIsTrackingApiReady;
    // bool mIsFirstWearing;
    std::atomic<bool> mIsRunning;
    bool mIsETDataCollecting;
    float mMotorRangMin;
    float mMotorRangMax;
    EyeData mWdDataOfLastXCheck;
    EyeDataCollector edc;
    MotorOps mops;
    IPDOps iops;

    int stageInit(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb);
    int stageJudgement(const WEARING_DETECTION_STAGE stage, const EyeData wdData);
    int stageProcess(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb, const int result, const EyeData wdData);
    int stageEnd(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb, const int result);
    int stageAction(const WEARING_DETECTION_STAGE stage, const interAction_callback ia_cb);
    void stageActions(const std::vector<WEARING_DETECTION_STAGE> stages, const interAction_callback ia_cb, uint32_t startIndex = 0);

    bool getEyeTrackingCalData();
    int getEyeTrackingPosLevel(float ipd);
    bool applyEyeTrackingCalData(std::string path);
    bool getMotorDistance(float& distance);
    bool setMotorDistance(const float distance);
    bool getIpdConfig(float& ipd);
    bool setIpdConfig(const float ipd);
    bool moveIpd(const float offset);
    bool setIpd(const float distance);
    float getLastIPD();

    void getJudgementData();
    void getCalibrationData();
    void getParam();

    void clearWDData(EyeData& wdData);
    bool isWDDataValid(EyeData& wdData);
    int collectingEyeTrackingData(float frequency, float duration, float percentage, EyeData& wdData);  // duration unit: second
    void stopCollectingEyeTrackingData();

    int checkPositionY(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, bool isRoughCheck);
    int checkPositionX(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, bool isRoughCheck);
    int reCheckPositionXForBizMode(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData);
    int compareIPD(const float ipd, const WearingDetectionJudegment& wdJData, float& diff);
    int calculateIPDMoving(const EyeData& wdData, const WearingDetectionJudegment& wdJData, const WearingDetectionCalibration& wdCData, float& len);
    int normalizedDiff2MotorStep(const float diff, float& len);

    int checkLensMounting();

    int calculateLensConfig(float ipd, tobii_lens_configuration_t& lens_config);

 private:
    yvrProjectBuildSpecType mProjectMode;

    void loadProjectMode();

 private:
    static Debug_Log_Flags mDebugLogFlags;
};

}  // end namespace yvr
}  // end namespace android

#endif  // _WEARING_DETECTION_H