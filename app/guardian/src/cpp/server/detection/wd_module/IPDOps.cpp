#include <stdlib.h>
#include <cutils/properties.h>

#include "IPDOps.h"

#define MODULE_NAME "IPD_OPS"

IPDOps::IPDOps() { getIpdConfigRange(IPDRange.min, IPDRange.max); }

IPDOps::~IPDOps() {}

bool IPDOps::getIpdConfig(float& ipd) {  // unit: mm
    bool bRet = false;
    if (mIsTrackingApiReady) {
        IPDConfiguration config = {.currIPD = 0.0f};
        bRet = (0 == TRACKINGAPI_GetIPDConfiguration(&config));
        if (bRet) {
            ipd = config.currIPD;
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "ipdConfig %f.", ipd);
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_GetIPDConfiguration failed.");
        }
    }
    return bRet;
}

bool IPDOps::setIpdConfig(const float ipd) {  // unit: mm
    bool bRet = false;
    if (mIsTrackingApiReady) {
        float corrIpd = limitIPDConfig(ipd);
        IPDConfiguration config = {.currIPD = corrIpd};
        bRet = (0 == TRACKINGAPI_SetIPDConfiguration(&config));
        if (bRet) {
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "ipdConfig %f.", corrIpd);
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetIPDConfiguration failed.");
        }
    }
    return bRet;
}

bool IPDOps::getIpd(float& ipd) { return mops.getMotorDistance(ipd); }

bool IPDOps::setIpd(const float ipd) {
    bool bRet = false;

    if (mIsTrackingApiReady) {
        float corrIpd = limitIPDConfig(ipd);
        float distance = ipd;
        if (mops.setMotorDistanceForET(distance)) {
            // need to update ipd configuration here
            bRet = setIpdConfig(corrIpd);
        }
    }

    return bRet;
}

bool IPDOps::moveIpd(const float offset) {
    bool bRet = false;

    DirectionTA direction;
    const float abs_offset = abs(offset);
    if (abs_offset < 1e-1) {  // == 0
        return true;
    } else if (offset > 0) {
        direction = DIRECTION_OUTER;
    } else if (offset < 0) {
        direction = DIRECTION_INNER;
    }
    DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "offset %f, direction %d", abs_offset, direction);

    if (mIsTrackingApiReady) {
        float currIpd, currIpdConfig;
        float nextIPD, nextIpdConfig;
        if (mops.getMotorDistance(currIpd) && getIpdConfig(currIpdConfig)) {
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "currIpd %f, currIpdConfig %f", currIpd, currIpdConfig);
            DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "motor range: min %f, max %f", mops.MOTOR_RANGE.min, mops.MOTOR_RANGE.max);

            nextIPD = currIpd + offset;
            nextIPD = limitIPDConfig(nextIPD);
            nextIpdConfig = nextIPD;

            if (abs(currIpdConfig - currIpd) < 0.2) {
                nextIpdConfig = nextIPD;
            } else {
                if ((currIpd >= mops.MOTOR_RANGE.max) && (DIRECTION_OUTER == direction)) {
                    nextIpdConfig = currIpdConfig + offset;
                    nextIpdConfig = limitIPDConfig(nextIpdConfig);
                } else if ((currIpd <= mops.MOTOR_RANGE.min) && (DIRECTION_INNER == direction)) {
                    nextIpdConfig = currIpdConfig + offset;
                    nextIpdConfig = limitIPDConfig(nextIpdConfig);
                }
            }

            bRet = (0 == TRACKINGAPI_MoveMotor(abs_offset, direction));
            if (bRet) {
                // need to update ipd configuration here, cuz this is the only interface called from guardian service to change ipd
                setIpdConfig(nextIpdConfig);
                if (mops.getMotorDistance(currIpd)) {
                    int retLens = TRACKINGAPI_SetLensConfig(currIpd);
                    DLOGD_IF(mDebugLogFlags.debugEnabled, MODULE_NAME, "Try to set lens config: %f", currIpd);
                    if (retLens == 0) {
                        DLOGI_IF(mDebugLogFlags.infoEnabled, MODULE_NAME, "Set lens config successfully");
                    } else if (retLens == -3) {
                        DLOGE(MODULE_NAME, "Failed to set lens config");
                    } else {
                        DLOGE(MODULE_NAME, "Failed to call TRACKINGAPI_SetLensConfig");
                    }
                }
            } else {
                DLOGE(MODULE_NAME, "TRACKINGAPI_MoveMotor failed.");
            }
        }
    }

    return bRet;
}

bool IPDOps::getIpdConfigRange(float& min, float& max) {
    bool bRet = true;

    min = IPD_FULL_RANGE_MIN;
    max = IPD_FULL_RANGE_MAX;

    return bRet;
}

float IPDOps::limitIPDConfig(const float ipd) {
    float ret = ipd;
    if (ret < IPDRange.min) ret = IPDRange.min;
    if (ret > IPDRange.max) ret = IPDRange.max;

    return ret;
}