#ifndef _EXT_MODULE_WEARING_DETECTION_H
#define _EXT_MODULE_WEARING_DETECTION_H

enum WEARING_DETECTION_RESULT {
    // bit0 ~ bit3
    WEARING_DETECTION_RESULT_OK = 0x0,
    WEARING_DETECTION_RESULT_FAILED = 0x1,
    WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED = 0x2,
    WEARING_DETECTION_RESULT_COLLECTING_ET_DATA_FAILED = 0x3,
    WEARING_DETECTION_RESULT_FORCE_STOP = 0x4,
    WEARING_DETECTION_RESULT_RETEST = 0x5,
    // bit4 ~ bit7
    WEARING_DETECTION_RESULT_Y_GOOD = 0x10,
    WEARING_DETECTION_RESULT_Y_UPWARDS = 0x20,
    WEARING_DETECTION_RESULT_Y_DOWNWARDS = 0x30,
    // bit8 ~ bit11
    WEARING_DETECTION_RESULT_X_GOOD = 0x100,
    WEARING_DETECTION_RESULT_X_OUTWARDS = 0x200,
    WEARING_DETECTION_RESULT_X_INWARDS = 0x300,
    WEARING_DETECTION_RESULT_X_LEFTWARDS = 0x400,
    WEARING_DETECTION_RESULT_X_RIGHTWARDS = 0x500,
    // bit12 ~ bit15
    WEARING_DETECTION_RESULT_IPD_GOOD = 0x1000,
    // bit16 ~ bit19
    WEARING_DETECTION_RESULT_LENS_MOUNTING_GOOD = 0x10000,
    WEARING_DETECTION_RESULT_LEFT_LENS_MOUNTING_BAD = 0x20000,
    WEARING_DETECTION_RESULT_RIGHT_LENS_MOUNTING_BAD = 0x30000,
    WEARING_DETECTION_RESULT_ALL_LENS_MOUNTING_BAD = 0x40000,
};

#define WEARING_DETECTION_RESULT_MASK 0x000f
#define WEARING_DETECTION_RESULT_Y_MASK 0x00f0
#define WEARING_DETECTION_RESULT_X_MASK 0x0f00
#define WEARING_DETECTION_RESULT_IPD_MASK 0xf000
#define WEARING_DETECTION_RESULT_LENS_MOUNTING_MASK 0xf0000

enum WEARING_DETECTION_STATUS {
    WEARING_DETECTION_STATUS_ERROR = -1,
    WEARING_DETECTION_STATUS_OK = 0,
    WEARING_DETECTION_STATUS_UNINITIALIZED,
    WEARING_DETECTION_STATUS_INITIALIZING,
    WEARING_DETECTION_STATUS_INITIALIZED,
    WEARING_DETECTION_STATUS_STARTING,
    // WEARING_DETECTION_STATUS_STARTED,
    WEARING_DETECTION_STATUS_PAUSE,
    WEARING_DETECTION_STATUS_WORKING_PAUSE,
    WEARING_DETECTION_STATUS_WORKING,
    /* WEARING_DETECTION_STATUS_POSITION_CHECKING,
    WEARING_DETECTION_STATUS_POSITION_CHECKED_DOWNWARDS,
    WEARING_DETECTION_STATUS_POSITION_CHECKED_UPWARDS,
    WEARING_DETECTION_STATUS_POSITION_CHECKED_GOOD,
    WEARING_DETECTION_STATUS_POSITION_CHECKED_FAILED,
    WEARING_DETECTION_STATUS_IPD_CHECKING,
    WEARING_DETECTION_STATUS_IPD_CHECKED_MOVING_OUTWARDS,
    WEARING_DETECTION_STATUS_IPD_CHECKED_MOVING_INWARDS,
    WEARING_DETECTION_STATUS_IPD_CHECKED_GOOD,
    WEARING_DETECTION_STATUS_IPD_CHECKED_FAILED,
    WEARING_DETECTION_STATUS_EYETRACKIHNG_CALIBRATION_CHECKING,
    WEARING_DETECTION_STATUS_FINISHED, */
};

enum WEARING_DETECTION_INTER_ACTION {
    // common
    WEARING_DETECTION_IA_START = 0,
    WEARING_DETECTION_IA_FINISHED = 1,
    WEARING_DETECTION_IA_FORCE_STOP = 2,
    // error return:
    WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID = 3,
    WEARING_DETECTION_IA_ERROR_FAILED = 4,
    // Y
    WEARING_DETECTION_IA_POSITION_Y_CHECKING = 10,
    WEARING_DETECTION_IA_POSITION_Y_CHECKED_GOOD = 11,
    WEARING_DETECTION_IA_POSITION_Y_CHECKED_FAILED = 12,
    WEARING_DETECTION_IA_POSITION_Y_CHECKED_UPWARDS = 13,
    WEARING_DETECTION_IA_POSITION_Y_CHECKED_DOWNWARDS = 14,
    // X
    WEARING_DETECTION_IA_POSITION_X_CHECKING = 20,
    WEARING_DETECTION_IA_POSITION_X_CHECKED_GOOD = 21,
    WEARING_DETECTION_IA_POSITION_X_CHECKED_FAILED = 22,
    WEARING_DETECTION_IA_POSITION_X_CHECKING_OUTWARDS = 23,
    WEARING_DETECTION_IA_POSITION_X_CHECKING_INWARDS = 24,
    WEARING_DETECTION_IA_POSITION_X_CHECKING_LEFTWARDS = 25,
    WEARING_DETECTION_IA_POSITION_X_CHECKING_RIGHTWARDS = 26,
    // Y and X
    WEARING_DETECTION_IA_POSITION_Y_AND_X_CHECKING = 30,
    WEARING_DETECTION_IA_POSITION_Y_AND_X_CHECKED_GOOD = 31,
    WEARING_DETECTION_IA_POSITION_Y_AND_X_CHECKED_FAILED = 32,
    // IPD
    WEARING_DETECTION_IA_IPD_CHECKING = 40,
    WEARING_DETECTION_IA_IPD_CHECKED_GOOD = 41,
    WEARING_DETECTION_IA_IPD_CHECKED_FAILED = 42,
    WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_OUTWARDS = 43,  // for last wd data mode
    WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_INWARDS = 44,
    // WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_OUTWARDS = 45,  // for none last wd data mode
    // WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_INWARDS = 46,
    // lens mounting check
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKING = 50,
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_GOOD = 51,
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_FAILED = 52,
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_LEFT_BAD = 53,
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_RIGHT_BAD = 54,
    WEARING_DETECTION_IA_LENS_MOUNTING_CHECKED_ALL_BAD = 55,
    // Others
    WEARING_DETECTION_IA_EYETRACKIHNG_CALIBRATION_CHECKING = 60,
};

enum WEARING_DETECTION_CMD {
    WEARING_DETECTION_CMD_SET_START_STAGE,           // obsoleted, no longer support, param: WEARING_DETECTION_STAGE startStage;
    WEARING_DETECTION_CMD_MOVE_IPD,                  // param: int steps;
    WEARING_DETECTION_CMD_MOVE_IPD_TO_POSITION,      // param: float targetPos;
    WEARING_DETECTION_CMD_LOAD_ET_CALIBRATION_DATA,  // param: NA;
    WEARING_DETECTION_CMD_STAGE_ACTION,              // param: WEARING_DETECTION_STAGE stage, in_out: interAction_callback ia_cb;
    WEARING_DETECTION_CMD_SWEEP_FOR_POSITION_GUIDE,  // no input needed
    WEARING_DETECTION_CMD_GET_DATA,                  // param: WEARING_DETECTION_DATA_TYPE type, in_out: WD_Module_CMD_GET_DATA_Output_Param param;
    WEARING_DETECTION_CMD_FORCE_STOP,                // stop any processing
};

enum WEARING_DETECTION_STAGE {
    WEARING_DETECTION_STAGE_START = 0,
    WEARING_DETECTION_STAGE_POSITION_Y_JUDGEMENT,
    WEARING_DETECTION_STAGE_POSITION_X_JUDGEMENT,
    WEARING_DETECTION_STAGE_POSITION_Y_AND_X_JUDGEMENT,
    WEARING_DETECTION_STAGE_POSITION_Z_JUDGEMENT,
    WEARING_DETECTION_STAGE_IPD_ADJUSTMENT,
    WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK,
    WEARING_DETECTION_STAGE_NUM = WEARING_DETECTION_STAGE_LENS_MOUNTING_CHECK,

    WEARING_DETECTION_STAGE_SET_ET_CALIBRATION_DATA,
    WEARING_DETECTION_STAGE_EYETRACKIHNG_CALIBRATION,
    WEARING_DETECTION_STAGE_GESTURE_CALIBRATION,
    WEARING_DETECTION_STAGE_FINISHED,
    WEARING_DETECTION_STAGE_NONE,
};

enum WEARING_DETECTION_MULTI_STAGES_MODE {
    WEARING_DETECTION_MULTI_STAGES_MODE_DEFAULT,
    WEARING_DETECTION_MULTI_STAGES_MODE_TEST,
};

enum WEARING_DETECTION_DATA_TYPE {
    WEARING_DETECTION_DATA_TYPE_PG,
};

typedef void (*interAction_callback)(int inter_action);

typedef struct WD_Module_Status {
    int status;
} WD_Module_Status;

typedef struct WD_Module_Output_Param {
    WD_Module_Status module_status;
} WD_Module_Output_Param;

typedef struct WD_Module_Init_Input_Param {
    ;
} WD_Module_Init_Input_Param;

typedef struct WD_Module_Init_Output_Param {
    WD_Module_Output_Param output_param;
} WD_Module_Init_Output_Param;

typedef struct WD_Module_Start_Input_Param {
    bool new_thread;
    interAction_callback ia_cb;
    WEARING_DETECTION_MULTI_STAGES_MODE multi_stages_mode;
} WD_Module_Start_Input_Param;

typedef struct WD_Module_Start_Output_Param {
    WD_Module_Output_Param output_param;
} WD_Module_Start_Output_Param;

typedef struct WD_Module_PG_Data_Param {
    float left_pg_X;
    float left_pg_Y;
    float right_pg_X;
    float right_pg_Y;
} WD_Module_PG_Data_Param;

typedef struct WD_Module_CMD_GET_DATA_Output_Param {
    // WD_Module_Output_Param output_param;

    WEARING_DETECTION_DATA_TYPE type;
    union {
        WD_Module_PG_Data_Param pg_data;
    };
} WD_Module_CMD_GET_DATA_Output_Param;

#endif  // _EXT_MODULE_WEARING_DETECTION_H