#ifndef _MOTOR_OPS
#define _MOTOR_OPS

#include "BaseOps.h"

typedef struct _Range {
    float min = 0.0f;
    float max = 0.0f;
} Range;

class MotorOps : public BaseOps {
 public:
    MotorOps();
    ~MotorOps();

 public:
    bool getMotorDistance(float& distance);         // unit: mm
    bool setMotorDistance(const float&& distance);  // unit: mm
    bool setMotorDistance(float& distance);         // unit: mm
    bool setMotorDistanceForET(float& distance);    // unit: mm
    bool setMotorDistanceForIPD(float& distance);   // unit: mm
    bool moveMotor(const float offset);             // unit: mm, >0: outwards, <0: inwards
    bool moveMotorForET(const float offset);        // unit: mm
    bool moveMotorForIPD(const float offset);       // unit: mm

 public:
    const Range& MOTOR_RANGE = MotorRange;

 private:
    bool getMotorDistanceRange(float& min, float& max);          // unit: mm
    inline float motorDistanceCorrection(const float distance);  // unit: mm
    inline float limitMotorDistance(const float distance);       // unit: mm

 private:
    Range MotorRange;
};

#endif  // _MOTOR_OPS