#include <csignal>
#include <unistd.h>
#include <iostream>

#include "yvrutils/extModuleLoader.h"
#include "extModuleWearingDetection.h"

using namespace std;
using namespace android;
using namespace yvr;

#define EXT_MODULE_WEARING_DETECTION_LIB_PATH "libwearingdetection.so"

static bool gRunning = false;
static bool gDump = false;
static int gMode = 0;  // 0: stages action; 1: ipd move; 2: stage action; 3: sweep ipd
static int gParamInt = 0;
static double gParamDb = 59.0;
static bool gNewThread = false;

// using namespace android;
// using namespace yvr;

void parseArg(int argc, char **argv) {
    uint8_t argIdx = 1;
    while (argIdx < argc) {
        if (!strcmp(argv[argIdx], "-c")) {
            gRunning = true;
        } else if (!strcmp(argv[argIdx], "-d")) {
            gDump = true;
        } else if (!strcmp(argv[argIdx], "-m")) {  // mode
            argIdx++;
            if (!strcmp(argv[argIdx], "stages")) {
                gMode = 0;
                argIdx++;
                if (!strcmp(argv[argIdx], "default")) {
                    gParamInt = 0;
                } else if (!strcmp(argv[argIdx], "test")) {
                    gParamInt = 1;
                }
            } else if (!strcmp(argv[argIdx], "ipd")) {
                gMode = 1;
                argIdx++;
                gParamInt = atoi(argv[argIdx]);  // input rang is -50 ~ +50, not the ipd value
            } else if (!strcmp(argv[argIdx], "stage")) {
                gMode = 2;
                argIdx++;
                gParamInt = atoi(argv[argIdx]);
            } else if (!strcmp(argv[argIdx], "sweep")) {
                gMode = 3;
            }
        } else if (!strcmp(argv[argIdx], "-t")) {
            gNewThread = true;
        } else {
        }
        argIdx++;
    }
}

void signal_handler(int signal) {
    if (signal == SIGINT) {
        gRunning = false;
    } else if (signal == SIGTSTP) {
        gRunning = false;
    }
}

void ia_callback(int inter_action) {
    switch (inter_action) {
        case WEARING_DETECTION_IA_POSITION_Y_CHECKING:
            printf("In stage positionY checking.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_Y_CHECKED_DOWNWARDS:
            printf("Need to move HMD downwards.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_Y_CHECKED_UPWARDS:
            printf("Need to move HMD upwards.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_Y_CHECKED_GOOD:
            printf("HMD positionY is good.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_Y_CHECKED_FAILED:
            printf("HMD positionY is bad.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_X_CHECKING:
            printf("In stage positionX checking.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_X_CHECKED_GOOD:
            printf("HMD positionX is good.\n");
            break;
        case WEARING_DETECTION_IA_POSITION_X_CHECKED_FAILED:
            printf("HMD positionX is bad.\n");
            break;
        case WEARING_DETECTION_IA_IPD_CHECKING:
            printf("In stage IPD checking.\n");
            break;
        /* case WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_OUTWARDS:
            printf("Need to move IPD outwards.\n");
            break;
        case WEARING_DETECTION_IA_IPD_CHECKED_NEED_MOVING_INWARDS:
            printf("Need to move IPD inwards.\n");
            break; */
        case WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_OUTWARDS:
            printf("Process to move IPD outwards.\n");
            break;
        case WEARING_DETECTION_IA_IPD_CHECKED_PROCESS_MOVING_INWARDS:
            printf("Process to move IPD inwards.\n");
            break;
        case WEARING_DETECTION_IA_IPD_CHECKED_GOOD:
            printf("IPD is good.\n");
            break;
        case WEARING_DETECTION_IA_IPD_CHECKED_FAILED:
            printf("IPD is bad.\n");
            break;
        case WEARING_DETECTION_IA_EYETRACKIHNG_CALIBRATION_CHECKING:
            printf("Try to call eyatracking calibration procedure.\n");
            break;
        case WEARING_DETECTION_IA_FINISHED:
            printf("Working done.\n");
            break;
        case WEARING_DETECTION_IA_FORCE_STOP:
            printf("Force stop.\n");
            break;
        default:
            break;
        case WEARING_DETECTION_IA_ERROR_EYETRACKING_INVALID:
            printf("EyeTracking issue.\n");
            break;
    }
}

int main(int argc, char **argv) {
    parseArg(argc, argv);

    struct sigaction sa;
    sa.sa_handler = &signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTSTP, &sa, NULL);

    bool bTest = true;
    int ret = EXT_MODULE_ERROR_FAILED;

    extModuleLoader emLoader(EXT_MODULE_WEARING_DETECTION_LIB_PATH);

    printf("Try to load extModule in %s.\n", EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    if (emLoader.isExtModuleReady()) {
        printf("%s is loaded.\n", EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    }

    string moduleName;
    emLoader.getModuleName(moduleName);
    printf("Test extModule Name: %s\n", moduleName.c_str());

    WD_Module_Init_Input_Param init_input_param;
    WD_Module_Init_Output_Param init_output_param;
    WD_Module_Start_Input_Param start_input_param;
    WD_Module_Start_Output_Param start_output_param;
    WD_Module_Status module_status;

    if (gNewThread) {
        printf("[%s] Launch new thread.\n", moduleName.c_str());
    }
    start_input_param.new_thread = gNewThread;
    start_input_param.ia_cb = ia_callback;
    start_input_param.multi_stages_mode = (WEARING_DETECTION_MULTI_STAGES_MODE)gParamInt;  // WEARING_DETECTION_MULTI_STAGES_MODE_DEFAULT;

    do {
        printf("\n\n");

        emLoader.getModuleStatus((void *)&module_status);
        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);

        printf("[%s] Try to init module.\n", moduleName.c_str());
        ret = emLoader.initModule((void *)&init_input_param, (void *)&init_output_param);
        if (EXT_MODULE_ERROR_NONE == ret || EXT_MODULE_ERROR_ALREADY_DONE == ret) {
            printf("[%s] Init module successfully.\n", moduleName.c_str());
            emLoader.getModuleStatus((void *)&module_status);
            printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
            printf("\n");

            switch (gMode) {
                case 0:  // stages action
                {
                    printf("[%s] Start working.\n", moduleName.c_str());
                    if (EXT_MODULE_ERROR_NONE == emLoader.startModule((void *)&start_input_param, (void *)&start_output_param)) {
                        emLoader.getModuleStatus((void *)&module_status);
                        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);

                        sleep(7);
                        printf("[%s] Pause module.\n", moduleName.c_str());
                        emLoader.pauseModule();
                        emLoader.getModuleStatus((void *)&module_status);
                        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
                        printf("\n");

                        sleep(1);
                        printf("[%s] Resume module.\n", moduleName.c_str());
                        emLoader.resumeModule();
                        emLoader.getModuleStatus((void *)&module_status);
                        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
                        printf("\n");

                        sleep(1);
                        printf("[%s] Stop module.\n", moduleName.c_str());
                        emLoader.stopModule();
                        emLoader.getModuleStatus((void *)&module_status);
                        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
                    }
                    break;
                }
                case 1:  // ipd move
                {
                    emLoader.moduleControl(WEARING_DETECTION_CMD_MOVE_IPD, gParamInt, NULL);
                    break;
                }
                case 2:  // stage action
                {
                    emLoader.moduleControl(WEARING_DETECTION_CMD_STAGE_ACTION, gParamInt, (Cmd_Param)ia_callback);
                    break;
                }
                case 3:  // sweep ipd
                {
                    emLoader.moduleControl(WEARING_DETECTION_CMD_SWEEP_FOR_POSITION_GUIDE, gParamDb, NULL);
                    break;
                }
                default: {
                    break;
                }
            }
        }

        sleep(3);
        //} while(gRunning);
    } while (0);

    emLoader.getModuleStatus((void *)&module_status);
    while (WEARING_DETECTION_STATUS_INITIALIZED != module_status.status) {
        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
        sleep(1);
    }

    printf("\n");
    printf("[%s] Deinit module.\n", moduleName.c_str());
    emLoader.deinitModule();
    emLoader.getModuleStatus((void *)&module_status);
    printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
}
