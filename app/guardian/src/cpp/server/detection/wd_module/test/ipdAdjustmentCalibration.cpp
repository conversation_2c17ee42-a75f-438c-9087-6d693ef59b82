#include <csignal>
#include <unistd.h>
#include <iostream>
#include <fstream>
#include <sstream>
#include <unistd.h>
#include <vector>
#include <cmath>
#include <stdexcept>
#include <iomanip>
#include <utility>
#include <algorithm>

#include "yvrutils/DebugLog.h"
#include "yvrutils/extModuleLoader.h"
#include "extModuleWearingDetection.h"
#include "trackingapi/TrackingAPI_Helper.h"
#include "trackingapi/ipd_configuration.h"
#include "guardianapi/GuardianAPI_Helper.h"
#include "EyeData.h"
#include "MotorOps.h"

using namespace std;
using namespace android;
using namespace yvr;

#define MODULE_NAME "ipdAdjustmentCalibration"

#define EXT_MODULE_WEARING_DETECTION_LIB_PATH "libwearingdetection.so"
// #define EXT_MODULE_WEARING_DETECTION_LIB_PATH "libextModuleSample.so"

#define DUMP_CSV_FILE_PATH "/data/misc/trackingservice/iac.csv"
#define DUMP_IPD_CALIBRATION_FILE_PATH "/backup/wddata.json"

#define IPD_VALUE_MIN (59.0f)
#define IPD_VALUE_MAX (69.5f)

static bool gContinuous = false;
static bool gDump = false;
static bool gNewThread = false;
static float gIdealIpd = 0.0f;
static bool gOutputCalibrationDataFile = false;
static bool gGazeImage = false;

static bool gIsTrackingApiReady = false;
static bool gIsGuardianApiReady = false;

static EyeDataCollector gEdc;
static MotorOps gMOps;

typedef struct PGData {
    EyeData eyeData;
    float ipd;
} PGData;

typedef struct PGCalibrationData {
    float data;
    float coef;
} PGCalibrationData;

typedef struct CalibrationData {
    PGCalibrationData leftX_outwards;
    PGCalibrationData leftX_inwards;
    PGCalibrationData rightX_outwards;
    PGCalibrationData rightX_inwards;
    PGCalibrationData leftY_outwards;
    PGCalibrationData leftY_inwards;
    PGCalibrationData rightY_outwards;
    PGCalibrationData rightY_inwards;
};

void parseArg(int argc, char** argv) {
    uint8_t argIdx = 1;
    while (argIdx < argc) {
        if (!strcmp(argv[argIdx], "-c")) {
            gContinuous = true;
        } else if (!strcmp(argv[argIdx], "-d")) {
            gDump = true;
        } else if (!strcmp(argv[argIdx], "-t")) {
            gNewThread = true;
        } else if (!strcmp(argv[argIdx], "-i")) {
            if ((argIdx + 1) < argc) {
                argIdx++;
                gIdealIpd = atof(argv[argIdx]);
                cout << "Ideal ipd is " << gIdealIpd << ".\n";
            }
        } else if (!strcmp(argv[argIdx], "-o")) {
            gOutputCalibrationDataFile = true;
        } else if (!strcmp(argv[argIdx], "-g")) {
            gGazeImage = true;
        } else {
        }
        argIdx++;
    }
}

bool validateParam() {
    bool ret = false;

    if (gIdealIpd >= IPD_VALUE_MIN && gIdealIpd <= IPD_VALUE_MAX) {
        ret = true;
    } else {
        cout << "Ideal ipd param error. Must be between " << IPD_VALUE_MIN << " and " << IPD_VALUE_MAX << ".\n";
    }

    return ret;
}

void signal_handler(int signal) {
    if (signal == SIGINT) {
        gContinuous = false;
    } else if (signal == SIGTSTP) {
        gContinuous = false;
    }
}
/*
bool moveMotor(float distance) {
    bool bRet = false;

    if (gIsTrackingApiReady) {
        bRet = (0 == TRACKINGAPI_SetSystemPropertyFloat(kIpdValuesProp, distance / 1000.0f));
        if (bRet) {
            // update ipd configuration
            IPDConfiguration config = {.currIPD = distance};
            bRet = (0 == TRACKINGAPI_SetIPDConfiguration(&config));
            if (bRet) {
                DLOGI(MODULE_NAME, "ipdConfig %f.", distance);
            } else {
                DLOGE(MODULE_NAME, "TRACKINGAPI_SetIPDConfiguration failed.");
            }

            // set lens config
            int retLens = TRACKINGAPI_SetLensConfig(distance);
            if (retLens == 0) {
                DLOGI(MODULE_NAME, "Set lens config successfully");
            } else if (retLens == -3) {
                DLOGW(MODULE_NAME, "Failed to set lens config");
            } else {
                DLOGW(MODULE_NAME, "Failed to call TRACKINGAPI_SetLensConfig");
            }
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_SetSystemPropertyFloat failed.");
        }
    }

    return bRet;
}

float motorDistanceCorrection(const float distance) {
    float corr = distance;
    if (corr <= 59.4) {
        corr = 59.0;
    }
    if (corr >= 69.1) {
        corr = 69.5;
    }
    return corr;
}

bool getMotorDistance(float& distance) {  // unit: mm
    bool bRet = false;
    if (gIsTrackingApiReady) {
        float ipd_meters[3];
        bRet = (0 == TRACKINGAPI_GetSystemPropertyFloatArray(kIpdFrustumProp, ipd_meters, 3));
        if (bRet) {
            distance = ipd_meters[0] * 1000.0f;
            distance = motorDistanceCorrection(distance);
            DLOGI(MODULE_NAME, "distance %f.", distance);
        } else {
            DLOGE(MODULE_NAME, "TRACKINGAPI_GetSystemPropertyFloatArray failed.");
        }
    }

    return bRet;
}

bool getEyeTrackingData(EyePoseStateTA& epState) {
    bool bRet = false;
    int ret;

    if (gIsTrackingApiReady) {
        int64_t currentTime = 0;
        ret = TRACKINGAPI_GetEyeDataET(currentTime, &epState);
        DLOGD(MODULE_NAME, "Call TRACKINGAPI_GetEyeDataET return: %d", ret);
        if (0 == ret) {
            bRet = true;
            // DLOGD(MODULE_NAME, "Left: flags %" PRIu64 ", PG_X %f, PG_Y %f, Right: flags %" PRIu64 ", PG_X %f, PG_Y %f", epState.eye[0].flags,
            //          epState.eye[0].positionGuide[0], epState.eye[0].positionGuide[1], epState.eye[1].flags, epState.eye[1].positionGuide[0], epState.eye[1].positionGuide[1]);
        }
    }

    return bRet;
}

bool calulateEyeData(const EyePoseStateTA& epState, EyeData& eyeData) {
    bool bRet = false;
    bool bRetPGL = false;
    bool bRetPGR = false;
    bool bRetCmbOrigin = false;

    if ((epState.eye[ET_EYE_LEFT].flags & ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) == ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) {
        eyeData.left_pupil_position_X = epState.eye[ET_EYE_LEFT].positionGuide[0];
        eyeData.left_pupil_position_Y = epState.eye[ET_EYE_LEFT].positionGuide[1];
        bRetPGL = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid left eye Position Guide.");
    }

    if ((epState.eye[ET_EYE_RIGHT].flags & ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) == ET_GAZE_PER_EYE_POSITION_GUIDE_VALID) {
        eyeData.right_pupil_position_X = epState.eye[ET_EYE_RIGHT].positionGuide[0];
        eyeData.right_pupil_position_Y = epState.eye[ET_EYE_RIGHT].positionGuide[1];
        bRetPGR = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid right eye Position Guide.");
    }

    if ((epState.flags & ET_GAZE_ORIGIN_COMBINED_VALID) == ET_GAZE_ORIGIN_COMBINED_VALID) {
        eyeData.combined_origin_Z = epState.gazeOriginCombined[2];
        bRetCmbOrigin = true;
    } else {
        DLOGW(MODULE_NAME, "Invalid eye position.");
    }

    if (bRetPGL && bRetPGR && bRetCmbOrigin) bRet = true;

    return bRet;
}

int collectingEyeTrackingData(float duration, float collection_percentage, EyeData& eyeData) {
    int ret = -1;

    EyePoseStateTA epState;
    // pre check eyetracking data
    if (!getEyeTrackingData(epState)) {
        DLOGE(MODULE_NAME, "Cannot get eyetracking data. Need to check eyetracking engine.\n");
        return WEARING_DETECTION_RESULT_ET_INTERFACE_FAILED;
    }

    const uint32_t MAX_EYE_POSE_STATE_COUNT = 90 * duration * collection_percentage;  // 90 tps * duration * 50%
    DLOGD(MODULE_NAME, "MAX_EYE_POSE_STATE_COUNT %d.\n", MAX_EYE_POSE_STATE_COUNT);
    const uint32_t MAX_EYE_POSE_STATE_FAILURE_COUNT = MAX_EYE_POSE_STATE_COUNT;
    uint32_t eye_pose_state_count = 0;
    uint32_t eye_pose_state_failure_count = 0;
    EyeData eyeDataArr[MAX_EYE_POSE_STATE_COUNT];

    do {
        if (getEyeTrackingData(epState) && calulateEyeData(epState, eyeDataArr[eye_pose_state_count])) {
            eye_pose_state_count++;
            DLOGD(MODULE_NAME, "eye_pose_state_count %d.\n", eye_pose_state_count);
            if (eye_pose_state_count >= MAX_EYE_POSE_STATE_COUNT) {
                ret = 0;
                break;
            } else {
                usleep(11111);
            }
        } else {
            DLOGW(MODULE_NAME, "getEyeTrackingData() failed.\n");
            eye_pose_state_failure_count++;
            if (eye_pose_state_failure_count >= MAX_EYE_POSE_STATE_FAILURE_COUNT) {
                break;
            }
            usleep(11111);
        }
    } while (eye_pose_state_count < MAX_EYE_POSE_STATE_COUNT);

    if (0 == ret) {
        DLOGI(MODULE_NAME, "Calculating  eyetracking data.\n");
        // average filter
        eyeData.left_pupil_position_X = 0.0;
        eyeData.right_pupil_position_X = 0.0;
        eyeData.left_pupil_position_Y = 0.0;
        eyeData.right_pupil_position_Y = 0.0;
        eyeData.combined_origin_Z = 0.0;
        for (uint32_t i = 0; i < MAX_EYE_POSE_STATE_COUNT; i++) {
            eyeData.left_pupil_position_X += eyeDataArr[i].left_pupil_position_X;
            eyeData.right_pupil_position_X += eyeDataArr[i].right_pupil_position_X;
            eyeData.left_pupil_position_Y += eyeDataArr[i].left_pupil_position_Y;
            eyeData.right_pupil_position_Y += eyeDataArr[i].right_pupil_position_Y;
            eyeData.combined_origin_Z += eyeDataArr[i].combined_origin_Z;
        }
        eyeData.left_pupil_position_X = eyeData.left_pupil_position_X / MAX_EYE_POSE_STATE_COUNT;
        eyeData.right_pupil_position_X = eyeData.right_pupil_position_X / MAX_EYE_POSE_STATE_COUNT;
        eyeData.left_pupil_position_Y = eyeData.left_pupil_position_Y / MAX_EYE_POSE_STATE_COUNT;
        eyeData.right_pupil_position_Y = eyeData.right_pupil_position_Y / MAX_EYE_POSE_STATE_COUNT;
        eyeData.combined_origin_Z = eyeData.combined_origin_Z / MAX_EYE_POSE_STATE_COUNT;
    }

    return ret;
}
*/

/* template<typename T>
void sort_any_object(std::vector<T>& arr, bool (*comp)(const T&, const T&) = nullptr) {
    if(comp) {
        std::sort(arr.begin(), arr.end(), comp);
    } else {
        std::sort(arr.begin(), arr.end());
    }
} */

template <typename T, typename Compare>
void sort_any_object(std::vector<T>& arr, Compare comp) {
    std::sort(arr.begin(), arr.end(), comp);
}

template <typename T>
void sort_any_object(std::vector<T>& arr) {
    std::sort(arr.begin(), arr.end());
}

void dumpCsv(vector<PGData>& vecPGData) {
    ofstream csvFile(DUMP_CSV_FILE_PATH);
    if (csvFile.is_open()) {
        ostringstream ss;

        for (PGData& pgData : vecPGData) {
            ss << pgData.ipd << ",";
        }
        ss << "\n";

        for (PGData& pgData : vecPGData) {
            ss << pgData.eyeData.left_pupil_position_X << ",";
        }
        ss << "\n";

        for (PGData& pgData : vecPGData) {
            ss << pgData.eyeData.left_pupil_position_Y << ",";
        }
        ss << "\n";

        for (PGData& pgData : vecPGData) {
            ss << pgData.eyeData.right_pupil_position_X << ",";
        }
        ss << "\n";

        for (PGData& pgData : vecPGData) {
            ss << pgData.eyeData.right_pupil_position_Y << ",";
        }
        ss << "\n";

        for (PGData& pgData : vecPGData) {
            ss << pgData.eyeData.combined_origin_Z << ",";
        }
        ss << "\n";

        // std::cout << "dumpCsv: " << ss.str() << "\n";
        csvFile << ss.str();

        csvFile.close();
    }
}

// 存储线性函数参数的结构体
struct LinearFunction {
    double slope;      // 斜率
    double intercept;  // 截距
    std::string equation() const { return "y = " + std::to_string(slope) + "x + " + std::to_string(intercept); }
};

// 核心计算函数
LinearFunction calculate_linear(const std::vector<std::pair<float, float>>& points) {
    if (points.size() < 2) {
        throw std::invalid_argument("至少需要2个坐标点");
    }

    double sum_x = 0, sum_y = 0;
    for (const auto& p : points) {
        sum_x += p.first;
        sum_y += p.second;
    }

    const double mean_x = sum_x / points.size();
    const double mean_y = sum_y / points.size();

    double numerator = 0;    // 协方差分子
    double denominator = 0;  // 方差分母
    for (const auto& p : points) {
        const double x_diff = p.first - mean_x;
        numerator += x_diff * (p.second - mean_y);
        denominator += x_diff * x_diff;
    }

    if (std::fabs(denominator) < 1e-10) {
        throw std::runtime_error("无法计算垂直直线的斜率");
    }

    const double slope = numerator / denominator;
    const double intercept = mean_y - slope * mean_x;

    return {slope, intercept};
}

bool calculate(std::vector<std::pair<float, float>>& points, PGCalibrationData& pgCalData) {
    bool bRet = false;

    try {
        // 计算并输出结果
        const auto func = calculate_linear(points);
        /* std::cout << std::fixed << std::setprecision(4);
        std::cout << "线性函数参数：\n"
                  << "斜率 (a) = " << func.slope << "\n"
                  << "截距 (b) = " << func.intercept << "\n"
                  << "方程：" << func.equation() << std::endl; */

        pgCalData.data = func.slope * gIdealIpd + func.intercept;
        pgCalData.coef = abs(1.0f / func.slope);

        bRet = true;
    } catch (const std::exception& e) {
        std::cerr << "错误: " << e.what() << std::endl;
    }

    return bRet;
}

bool getCalibrationResult(vector<PGData>& vecPGDataOutwards, vector<PGData>& vecPGDataInwards, CalibrationData& cData) {
    bool bRet = false;

    std::vector<std::pair<float, float>> points;

    auto fillVec = [](float x, float y, std::vector<std::pair<float, float>>& vec) -> bool {
        if (y > 0.1) {
            vec.emplace_back(x, y);
            return true;
        }
        return false;
    };

    // X
    // outwards left
    points.clear();
    for (PGData& pgData : vecPGDataOutwards) {
        fillVec(pgData.ipd, pgData.eyeData.left_pupil_position_X, points);
    }
    bRet = calculate(points, cData.leftX_outwards);
    // outwards right
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataOutwards) {
            fillVec(pgData.ipd, pgData.eyeData.right_pupil_position_X, points);
        }
        calculate(points, cData.rightX_outwards);
    }
    // inwards left
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataInwards) {
            fillVec(pgData.ipd, pgData.eyeData.left_pupil_position_X, points);
        }
        calculate(points, cData.leftX_inwards);
    }
    // inwards right
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataInwards) {
            fillVec(pgData.ipd, pgData.eyeData.right_pupil_position_X, points);
        }
        calculate(points, cData.rightX_inwards);
    }

    // Y
    // outwards left
    points.clear();
    for (PGData& pgData : vecPGDataOutwards) {
        fillVec(pgData.ipd, pgData.eyeData.left_pupil_position_Y, points);
    }
    bRet = calculate(points, cData.leftY_outwards);
    // outwards right
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataOutwards) {
            fillVec(pgData.ipd, pgData.eyeData.right_pupil_position_Y, points);
        }
        calculate(points, cData.rightY_outwards);
    }
    // inwards left
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataInwards) {
            fillVec(pgData.ipd, pgData.eyeData.left_pupil_position_Y, points);
        }
        calculate(points, cData.leftY_inwards);
    }
    // inwards right
    if (bRet) {
        points.clear();
        for (PGData& pgData : vecPGDataInwards) {
            fillVec(pgData.ipd, pgData.eyeData.right_pupil_position_Y, points);
        }
        calculate(points, cData.rightY_inwards);
    }

    if (bRet) {
        /* std::cout << "left_X1: " << cData.leftX_outwards.data << ", left_Ax1: " << cData.leftX_outwards.coef << "\n";
        std::cout << "right_X1: " << cData.rightX_outwards.data << ", right_Ax1: " << cData.rightX_outwards.coef << "\n";
        std::cout << "left_X2: " << cData.leftX_inwards.data << ", left_Ax2: " << cData.leftX_inwards.coef << "\n";
        std::cout << "right_X2: " << cData.rightX_inwards.data << ", right_Ax2: " << cData.rightX_inwards.coef << "\n";
        std::cout << "left_Y1: " << cData.leftY_outwards.data << ", left_Ay1: " << cData.leftY_outwards.coef << "\n";
        std::cout << "right_Y1: " << cData.rightY_outwards.data << ", right_Ay1: " << cData.rightY_outwards.coef << "\n";
        std::cout << "left_Y2: " << cData.leftY_inwards.data << ", left_Ay2: " << cData.leftY_inwards.coef << "\n";
        std::cout << "right_Y2: " << cData.rightY_inwards.data << ", right_Ay2: " << cData.rightY_inwards.coef << "\n"; */
    }

    return bRet;
}

void saveIpdAdjustmentCalibrationDataFile(const CalibrationData& cData) {
    ofstream oFile(DUMP_IPD_CALIBRATION_FILE_PATH);
    if (oFile.is_open()) {
        ostringstream ss;

        ss << "{" << "\"left_X1\":" << cData.leftX_outwards.data << ",\"left_Ax1\":" << cData.leftX_outwards.coef << ",\"right_X1\":" << cData.rightX_outwards.data
           << ",\"right_Ax1\":" << cData.rightX_outwards.coef << ",\"left_X2\":" << cData.leftX_inwards.data << ",\"left_Ax2\":" << cData.leftX_inwards.coef
           << ",\"right_X2\":" << cData.rightX_inwards.data << ",\"right_Ax2\":" << cData.rightX_inwards.coef << ",\"left_Y1\":" << cData.leftY_outwards.data
           << ",\"left_Ay1\":" << cData.leftY_outwards.coef << ",\"right_Y1\":" << cData.rightY_outwards.data << ",\"right_Ay1\":" << cData.rightY_outwards.coef
           << ",\"left_Y2\":" << cData.leftY_inwards.data << ",\"left_Ay2\":" << cData.leftY_inwards.coef << ",\"right_Y2\":" << cData.rightY_inwards.data
           << ",\"right_Ay2\":" << cData.rightY_inwards.coef << "}";

        oFile << ss.str();

        oFile.close();
    }
}

void doOnce(const float ipd, PGData& pgData) {
    // move motor
    // moveMotor(ipd);
    pgData.ipd = ipd;
    gMOps.setMotorDistanceForIPD(pgData.ipd);

    // set IPD
    /* float readoutIPD;
    if (gMOps.getMotorDistance(readoutIPD)) {
        pgData.ipd = readoutIPD;
    } else {
        pgData.ipd = ipd;
    } */

    // get eye data
    CollectionInfo ci = {90.0f, 2.0f, 0.5f};
    auto result = gEdc.collectingAsync(ci, pgData.eyeData);
    int ret = result.get();
    // collectingEyeTrackingData(2.0f, 0.5f, pgData.eyeData);
    /* printf("[%s] IPD: %f, left: X %f, Y %f, right: X %f, Y %f\n", MODULE_NAME, ipd,
        pgData.eyeData.left_pupil_position_X, pgData.eyeData.left_pupil_position_Y,
        pgData.eyeData.right_pupil_position_X, pgData.eyeData.right_pupil_position_Y); */
}

int main(int argc, char** argv) {
    parseArg(argc, argv);
    if (!validateParam()) {
        return -1;
    }

    /* struct sigaction sa;
    sa.sa_handler = &signal_handler;
    sigemptyset(&sa.sa_mask);
    sa.sa_flags = 0;
    sigaction(SIGINT, &sa, NULL);
    sigaction(SIGTSTP, &sa, NULL); */

#if 1
    // init
    gIsTrackingApiReady = (0 == TRACKINGAPI_Init());
    if (!gIsTrackingApiReady) {
        DLOGE(MODULE_NAME, "TRACKINGAPI_Init failed.");
    }
    if (gGazeImage) {
        gIsGuardianApiReady = (0 == GUARDIANAPI_Init());
        if (!gIsGuardianApiReady) {
            DLOGE(MODULE_NAME, "GUARDIANAPI_Init failed.");
        }
    }

    // show gaze image
    if (gGazeImage) {
        GUARDIANAPI_ShowGazeImage(true);
        usleep(3000000);
    }

    // do something
    cout << "Start calibration.\n";
    const float IPD_MIN = 59.0, IPD_MAX = 69.5;
    const float IPD_STEP = 1.0;
    float currIPD;

    std::vector<PGData> vecPGDataOutwards, vecPGDataInwards, vecPGData;
    // moving outwards
    for (currIPD = IPD_MIN; currIPD < IPD_MAX; currIPD += IPD_STEP) {
        PGData pgData;
        doOnce(currIPD, pgData);
        vecPGDataOutwards.emplace_back(pgData);
    }
    // moving inwards
    for (currIPD = IPD_MAX; currIPD > IPD_MIN; currIPD -= IPD_STEP) {
        PGData pgData;
        doOnce(currIPD, pgData);
        vecPGDataInwards.emplace_back(pgData);
    }

    std::copy(vecPGDataOutwards.begin(), vecPGDataOutwards.end(), std::back_inserter(vecPGData));
    std::copy(vecPGDataInwards.begin(), vecPGDataInwards.end(), std::back_inserter(vecPGData));
    // sort
    /* sort_any_object(vecPGData, [](const PGData& a, const PGData& b) {
        return (a.ipd < b.ipd);
    }); */

    if (gDump) {
        dumpCsv(vecPGData);
    }

    CalibrationData calData;
    if (getCalibrationResult(vecPGDataOutwards, vecPGDataInwards, calData)) {
        if (gOutputCalibrationDataFile) {
            saveIpdAdjustmentCalibrationDataFile(calData);
        }
    } else {
        cout << "Failed to get calibration result." << std::endl;
    }

    // quit gaze image
    if (gGazeImage) {
        GUARDIANAPI_ShowGazeImage(false);
    }

    // deinit
    if (gIsGuardianApiReady) {
        GUARDIANAPI_DeInit();
        gIsGuardianApiReady = false;
    }
    if (gIsTrackingApiReady) {
        TRACKINGAPI_DeInit();
        gIsTrackingApiReady = false;
    }
#else
    bool bTest = true;
    int ret = EXT_MODULE_ERROR_FAILED;

    extModuleLoader emLoader(EXT_MODULE_WEARING_DETECTION_LIB_PATH);

    /* printf("Try to load extModule in %s.\n", EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    if (emLoader.isExtModuleReady()) {
        printf("%s is loaded.\n", EXT_MODULE_WEARING_DETECTION_LIB_PATH);
    }

    string moduleName;
    emLoader.getModuleName(moduleName);
    printf("Test extModule Name: %s\n", moduleName.c_str());

    WD_Module_Init_Input_Param init_input_param;
    WD_Module_Init_Output_Param init_output_param;
    WD_Module_Start_Input_Param start_input_param;
    WD_Module_Start_Output_Param start_output_param;
    WD_Module_Status module_status;

    if (gNewThread) {
        printf("[%s] Launch new thread.\n", moduleName.c_str());
    }
    start_input_param.new_thread = gNewThread;
    start_input_param.ia_cb = ia_callback;
    start_input_param.multi_stages_mode = (WEARING_DETECTION_MULTI_STAGES_MODE)gParamInt;  // WEARING_DETECTION_MULTI_STAGES_MODE_DEFAULT;

    do {
        printf("\n\n");

        emLoader.getModuleStatus((void *)&module_status);
        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);

        printf("[%s] Try to init module.\n", moduleName.c_str());
        ret = emLoader.initModule((void *)&init_input_param, (void *)&init_output_param);
        if (EXT_MODULE_ERROR_NONE == ret || EXT_MODULE_ERROR_ALREADY_DONE == ret) {
            printf("[%s] Init module successfully.\n", moduleName.c_str());
            emLoader.getModuleStatus((void *)&module_status);
            printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
            printf("\n");

            printf("[%s] Start calibration.\n", moduleName.c_str());
            const float IPD_MIN = 59.0, IPD_MAX = 69.5;
            const float IPD_STEP = 1.0;

            for (float currIPD = IPD_MIN; currIPD < IPD_MAX; currIPD += IPD_STEP) {
                printf("[%s] IPD: %fmm\n", moduleName.c_str(), currIPD);

                bool bRet = false;
                int ret = -1;

                ret = emLoader.moduleControl(WEARING_DETECTION_CMD_MOVE_IPD_TO_POSITION, currIPD, NULL);
                printf("[%s] WEARING_DETECTION_CMD_MOVE_IPD_TO_POSITION ret: %d\n", moduleName.c_str(), ret);
                if (ret == EXT_MODULE_ERROR_NONE) {
                    WD_Module_CMD_GET_DATA_Output_Param data;
                    ret = emLoader.moduleControl(WEARING_DETECTION_CMD_GET_DATA, WEARING_DETECTION_DATA_TYPE_PG, (Cmd_Param)&data);
                    printf("[%s] WEARING_DETECTION_CMD_GET_DATA ret: %d\n", moduleName.c_str(), ret);
                    if (ret == EXT_MODULE_ERROR_NONE) {
                        printf("[%s] left: X %f, Y %f, right: X %f, Y %f\n", moduleName.c_str(), data.pg_data.left_pg_X, data.pg_data.left_pg_Y, data.pg_data.right_pg_X, data.pg_data.right_pg_Y);
                    }
                }
            }
        }

        sleep(3);
    // } while(gContinuous);
    } while (0);

    emLoader.getModuleStatus((void *)&module_status);
    while (WEARING_DETECTION_STATUS_INITIALIZED != module_status.status) {
        printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);
        sleep(1);
    }

    printf("\n");
    printf("[%s] Deinit module.\n", moduleName.c_str());
    emLoader.deinitModule();
    emLoader.getModuleStatus((void *)&module_status);
    printf("[%s] Status: %d.\n", moduleName.c_str(), module_status.status);

    emLoader.unloadExtModule(); */
#endif

    return 0;
}
