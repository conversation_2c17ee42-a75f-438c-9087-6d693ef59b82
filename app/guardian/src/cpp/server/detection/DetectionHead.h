#pragma once

enum DetectionThreadMessage {
    DT_MSG_INIT = 0,

    // Wearing Detection
    DT_MSG_WEARING_DETECTION_MODULE_STATE_CHANGE,
    DT_MSG_WEARING_DETECTION_CALLBACK_HANDLE,
    DT_MSG_WEARING_DETECTION_TAKE_PICTURE,
    // others

    // quit
    DT_MSG_QUIT = 999
};

enum WearingDetectionStatus {
    DETECTION_DONE = 0,
    DETECTION_FORCE_STOP = 1,
    DETECTION_FAIL = 2,

    Y_WAITING_DETECTION = 10,
    Y_DETECTION_SUCCEED = 11,
    Y_DOWNWARD_ADJUSTMENT = 12,
    Y_UPWARD_ADJUSTMENT = 13,

    X_WAITING_DETECTION = 20,
    X_DETECTION_SUCCEED = 21,
    X_INWARD_ADJUSTMENT = 22,
    X_OUTWARD_ADJUSTMENT = 23,

    Z_WAITING_DETECTION = 30,
    Z_DETECTION_SUCCEED = 31,

    IPD_WAITING_AUTOMATIC_ADJUSTMENT = 40,
    IPD_DETECTION_SUCCEED = 41,
    IPD_AUTOMATIC_ADJUSTMENT = 42,
    IPD_INWARD_ADJUSTMENT = 43,
    IPD_OUTWARD_ADJUSTMENT = 44,

    DIOPTER_LENS_CHECK_WAITING = 50,
    DIOPTER_LENS_CHECK_SUCCEED = 51,
    DIOPTER_LENS_CHECK_LEFT_BAD = 52,
    DIOPTER_LENS_CHECK_RIGHT_BAD = 53,
    DIOPTER_LENS_CHECK_ALL_BAD = 54,
};

enum WEARING_DETECTION_CMD_ACTION {
    POSITION_Y_JUDGEMENT = 0,
    POSITION_X_JUDGEMENT = 1,
    POSITION_Y_X_JUDGEMENT = 2,
    POSITION_Z_JUDGEMENT = 3,
    IPD_ADJUSTMENT = 4,
    DIOPTER_LENS_CHECK = 5,
    FORCE_STOP = 9,
};

enum WearingDetectionModuleState { INIT = 1, START, PAUSE, RESUME, STOP, DEINIT };