#pragma once

#include <yvrmath/YVRMath.h>

namespace foo {
struct BoundaryFrameStatus {
    uint64_t boundaryId;
    YVRMath::vec4 headCDUV;
    YVRMath::vec4 leftCDUV;
    YVRMath::vec4 rightCDUV;
    YVRMath::vec4 timeParam;
    YVRMath::vec4 boundaryParams;
    float rollDegrees;
    float blendRate;
    bool inSeeThrough;
    bool isOut;
    bool isCollisionOccurred;
    float headToBoundaryDistance;
    float leftToBoundaryDistance;
    float rightToBoundaryDistance;
    int effectType;
    int boundaryType;
};
}  // namespace foo