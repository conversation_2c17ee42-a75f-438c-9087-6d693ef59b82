#pragma once

#include <stdint.h>

namespace foo {
namespace device {
typedef uint32_t CapabilityFlags;
const CapabilityFlags Position = 1u << 1u;
const CapabilityFlags Orientation = 1u << 2u;
const CapabilityFlags Present = 1u << 3u;
const CapabilityFlags AngularAcceleration = 1u << 5u;
const CapabilityFlags LinearAcceleration = 1u << 6u;
const CapabilityFlags StageParameters = 1u << 7u;
const CapabilityFlags MountDetection = 1u << 8u;
const CapabilityFlags PositionEmulated = 1u << 9u;
const CapabilityFlags InlineSession = 1u << 10u;
const CapabilityFlags ImmersiveVRSession = 1u << 11u;
const CapabilityFlags ImmersiveARSession = 1u << 12u;
const CapabilityFlags GripSpacePosition = 1u << 13u;

enum class Eye { Left, Right };
inline int32_t EyeIndex(const Eye aEye) { return aEye == Eye::Left ? 0 : 1; }

struct EyeRect {
    float mX, mY;
    float mWidth, mHeight;
    EyeRect() : mX(0.0f), mY(0.0f), mWidth(0.0f), mHeight(0.0f) {}
    EyeRect(const float aX, const float aY, const float aWidth, const float aHeight) : mX(aX), mY(aY), mWidth(aWidth), mHeight(aHeight) {}
    EyeRect& operator=(const EyeRect& aRect) {
        mX = aRect.mX;
        mY = aRect.mY;
        mWidth = aRect.mWidth;
        mHeight = aRect.mHeight;
        return *this;
    }

    bool IsDefault() const { return mX == 0.0f && mY == 0.0f && mWidth == 1.0f && mHeight == 1.0f; }
};

}  // namespace device
}  // namespace foo
