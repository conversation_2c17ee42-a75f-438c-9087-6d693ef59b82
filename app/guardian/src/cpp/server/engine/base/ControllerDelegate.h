
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"
#include <memory>
#include <string>
#include <vector>
#include <openxr/openxr.h>
#include <pfdm_openxr_preview/openxr_yvr.h>

namespace foo {

class ControllerDelegate;
typedef std::shared_ptr<ControllerDelegate> ControllerDelegatePtr;
typedef std::weak_ptr<ControllerDelegate> ControllerDelegateWeakPtr;

enum class ControllerMode { None, Device, Hand };

class ControllerDelegate {
 public:
    enum Button {
        BUTTON_TRIGGER = 1u << 0u,
        BUTTON_SQUEEZE = 1u << 1u,
        BUTTON_TOUCHPAD = 1u << 2u,
        BUTTON_APP = 1u << 3u,
        BUTTON_A = 1u << 4u,
        BUTTON_B = 1u << 5u,
        BUTTON_X = 1u << 6u,
        BUTTON_Y = 1u << 7u,
        BUTTON_OTHERS = 1u << 8u,  // Other buttons only for the immersive mode.
    };

    enum ControllerIndex { LEFT = 0, RIGHT = 1 };

    virtual void CreateController(const int32_t aControllerIndex, const int32_t aModelIndex) = 0;
    virtual void LoadControllerModel(const std::string &aFileName, const int32_t aModelIndex) = 0;
    virtual void InitialBeam(const int32_t aControllerIndex) = 0;
    //  virtual void CreateController(const int32_t aControllerIndex, const int32_t aModelIndex, const std::string& aImmersiveName,
    //                                const YVRMath::mat4& aBeamTransform) = 0;
    virtual void SetBeamTransform(const int32_t aControllerIndex, const YVRMath::mat4 &aBeamTransform) = 0;
    virtual void SetFocused(const int32_t aControllerIndex) = 0;
    virtual void DestroyController(const int32_t aControllerIndex) = 0;
    virtual uint32_t GetControllerCount() = 0;
    virtual void SetEnabled(const int32_t aControllerIndex, const bool aEnabled) = 0;
    virtual void SetTransform(const int32_t aControllerIndex, const YVRMath::mat4 &aTransform) = 0;
    virtual void SetButtonCount(const int32_t aControllerIndex, const uint32_t aNumButtons) = 0;
    virtual void SetButtonState(const int32_t aControllerIndex, const Button aWhichButton, const bool aPressed, const bool aTouched, const float aTriggerValue) = 0;
    virtual void SetHapticCount(const int32_t aControllerIndex, const uint32_t aNumHaptics) = 0;
    virtual uint32_t GetHapticCount(const int32_t aControllerIndex) = 0;
    virtual void SetHapticFeedback(const int32_t aControllerIndex, const uint64_t aInputFrameID, const float aPulseDuration, const float aPulseIntensity) = 0;
    virtual void GetHapticFeedback(const int32_t aControllerIndex, uint64_t &aInputFrameID, float &aPulseDuration, float &aPulseIntensity) = 0;
    virtual void SetSelectActionStart(const int32_t aControllerIndex) = 0;
    virtual void SetSelectActionStop(const int32_t aControllerIndex) = 0;
    virtual void SetSqueezeActionStart(const int32_t aControllerIndex) = 0;
    virtual void SetSqueezeActionStop(const int32_t aControllerIndex) = 0;
    virtual void SetLeftHanded(const int32_t aControllerIndex, const bool aLeftHanded) = 0;
    virtual void SetTouchPosition(const int32_t aControllerIndex, const float aTouchX, const float aTouchY) = 0;
    virtual void EndTouch(const int32_t aControllerIndex) = 0;
    virtual void SetScrolledDelta(const int32_t aControllerIndex, const float aScrollDeltaX, const float aScrollDeltaY) = 0;
    virtual void SetBatteryLevel(const int32_t aControllerIndex, const int32_t aBatteryLevel) = 0;
    virtual bool IsEnable(const int32_t aControllerIndex) = 0;
    virtual bool IsVisible() const = 0;
    virtual void SetVisible(const bool aVisible) = 0;
    virtual void SetGazeModeIndex(const int32_t aControllerIndex) = 0;
    virtual void SetSignTransform(const int32_t aControllerIndex, const YVRMath::mat4 &aTransform) = 0;
    virtual void SetIndicatorTransform(const int32_t aControllerIndex, const YVRMath::mat4 &aTransform) = 0;
    virtual void SetHandLinearVelocity(const int32_t aControllerIndex, XrSpaceVelocity velocity) = 0;
    virtual void SetHandSpaceLocation(const int32_t aControllerIndex, XrSpaceLocation pose) = 0;

    virtual XrSpaceVelocity GetHandLinearVelocity(const int32_t aControllerIndex) = 0;
    virtual XrSpaceLocation GetHandSpaceLocation(const int32_t aControllerIndex) = 0;
    virtual void SetAimEnabled(const int32_t aControllerIndex, bool aEnabled = true) = 0;
    virtual void SetMode(const int32_t aControllerIndex, ControllerMode aMode = ControllerMode::None) = 0;
    virtual void SetPinchFactor(const int32_t aControllerIndex, float aFactor = 1.0f) = 0;
    virtual void CreateHand(const int32_t aHandIndex, XrHandTrackingMeshYVR *mesh) = 0;
    virtual void UpdataHand(const int32_t aHandIndex, XrHandJointLocationEXT *jointlocations, bool enable) = 0;
    virtual void DrawHand(const int32_t aWitchEye, CameraPtr leftCamera, CameraPtr rightCamera) = 0;

 protected:
    ControllerDelegate() {}
    ~ControllerDelegate() {}

 private:
    FOO_NO_DEFAULTS(ControllerDelegate)
};

}  // namespace foo
