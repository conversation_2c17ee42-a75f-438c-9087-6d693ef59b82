#include "BoundaryProgramBuilder.h"
#include "foo/ResourceGL.h"
namespace foo {
struct BoundaryProgramBuilder::State : public ResourceGL::State {
    LoaderThreadWeak loaderHandle;
    ProgramPtr program;
    uint32_t featureMask;
    std::string customFragmentShader;
    GLuint vertexShader;
    GLuint fragmentShader;
    GLuint programHandle;
    PROGRAM_TYPE programType;

    State() : program(Program::Create()), featureMask(0), vertexShader(0), fragmentShader(0), programHandle(0) {}
    bool IsTexturingEnabled() { return (featureMask & (FeatureTexture | FeatureCubeTexture | FeatureSurfaceTexture)) != 0; }
    bool IsCubeMapTextureEnabled() { return (featureMask & FeatureCubeTexture) != 0; }
    bool IsSurfaceTextureEnabled() { return (featureMask & FeatureSurfaceTexture) != 0; }
    std::string GetFragmentShader(const std::string& aSource) {
        const std::string kPrecisionMacro("FOO_FRAGMENT_PRECISION");
        std::string result = aSource;
        const size_t kUVStart = result.find(kPrecisionMacro);
        if (kUVStart != std::string::npos) {
            const char* precision;
            if ((featureMask & FeatureHighPrecision) != 0) {
                precision = "highp";
            } else if ((featureMask & FeatureLowPrecision) != 0) {
                precision = "lowp";
            } else {
                precision = "mediump";
            }
            result.replace(kUVStart, kPrecisionMacro.length(), precision);
        }
        return result;
    }
};

BoundaryProgramBuilderPtr BoundaryProgramBuilder::Create() {
    BoundaryProgramBuilderPtr result = std::make_shared<ConcreteClass<BoundaryProgramBuilder, BoundaryProgramBuilder::State> >();
    return result;
}

ProgramPtr BoundaryProgramBuilder::GetProgram() { return m.program; }

void BoundaryProgramBuilder::SetFeatures(const uint32_t aFeatureMask, const std::string& aCustomFragShader) {
    m.featureMask = aFeatureMask;
    m.customFragmentShader = aCustomFragShader;
    m.program->SetFeatures(aFeatureMask);
}

void BoundaryProgramBuilder::Finalize() {
    if (!m.program) {
        return;
    }
    m.program->SetProgram(m.programHandle);
}

void BoundaryProgramBuilder::SetProgramType(PROGRAM_TYPE aType) { m.programType = aType; }

bool BoundaryProgramBuilder::SupportOffRenderThreadInitialization() { return true; }

void BoundaryProgramBuilder::InitializeGL() {
    std::string vertexShaderSource;
    std::string frag;
    switch (m.programType) {
        case PROGRAM_TYPE::GAME: {
            vertexShaderSource =
#include "shaders/boundary_mesh_game.vert"
                ;
            frag =
#include "shaders/boundary_mesh_game.fs"
                ;
            break;
        }
        case PROGRAM_TYPE::LOCAL: {
            vertexShaderSource =
#include "shaders/boundary_mesh_local.vert"
                ;
            frag =
#include "shaders/boundary_effect_local.fs"
                ;
            break;
        }
        case PROGRAM_TYPE::GROUND_CIRCLE: {
            vertexShaderSource =
#include "shaders/boundary_mesh_ground.vert"
                ;
            frag =
#include "shaders/boundary_mesh_ground.fs"
                ;
            break;
        }

        default: {
            FOO_ERROR("unsupported program type");
            return;
        }
    }
    m.vertexShader = LoadShader(GL_VERTEX_SHADER, vertexShaderSource.c_str());
    m.fragmentShader = LoadShader(GL_FRAGMENT_SHADER, frag.c_str());
    if (m.fragmentShader && m.vertexShader && (m.programHandle = CreateProgram(m.vertexShader, m.fragmentShader))) {
        FOO_LOG("create Boundary Program [%d] success %d", m.programType, m.programHandle);
    } else {
        FOO_LOG("create Boundary Program fail");
    }
    Finalize();
}

void BoundaryProgramBuilder::ShutdownGL() {
    if (m.programHandle) {
        FOO_GL_CHECK(glDeleteProgram(m.programHandle));
        m.programHandle = 0;
        m.program->SetProgram(0);
    }

    if (m.vertexShader) {
        FOO_GL_CHECK(glDeleteShader(m.vertexShader));
        m.vertexShader = 0;
    }

    if (m.fragmentShader) {
        FOO_GL_CHECK(glDeleteShader(m.fragmentShader));
        m.fragmentShader = 0;
    }
}

BoundaryProgramBuilder::BoundaryProgramBuilder(State& aState) : ResourceGL(aState), m(aState) {}
}  // namespace foo