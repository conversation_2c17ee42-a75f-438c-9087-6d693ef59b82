#include "BoundaryGeometry.h"
#include "foo/private/AbstractGeometryState.h"
#include "BoundaryGeometryDrawable.h"
#include "foo/ConcreteClass.h"
#include "foo/Logger.h"
#include "foo/VertexArray.h"
#include "foo/GLError.h"
#include "foo/Color.h"

namespace foo {

struct BoundaryGeometry::State : public AbstractGeometry::State, public BoundaryGeometryDrawable::State {};

BoundaryGeometryPtr BoundaryGeometry::Create(CreationContextPtr& aContext) { return std::make_shared<ConcreteClass<BoundaryGeometry, BoundaryGeometry::State>>(aContext); }
void BoundaryGeometry::UpdateBuffers() {
    GLuint vertexObjectId = m.renderBuffer->GetVertexObject();
    GLuint indexObjectId = m.renderBuffer->GetIndexObject();
    if (vertexObjectId == 0 || indexObjectId == 0) {
        FOO_WARN("Geometry GL objects not created");
        return;
    }

    const bool kHasTextureCoords = m.vertexArray->GetUVCount() > 0;
    const bool kHasColor = m.vertexArray->GetColorCount() > 0;
    const GLsizei kPositionSize = m.renderBuffer->PositionSize();
    const GLsizei kNormalSize = m.renderBuffer->NormalSize();
    const GLsizei kUVSize = m.renderBuffer->UVSize();
    const GLsizei kColorSize = m.renderBuffer->ColorSize();
    FOO_LOG("UpdateBuffers %d,%d,%d, %d, %d, %d, %d, %d,%d,%d,%d, ", kHasTextureCoords ? 1 : 0, kHasColor ? 1 : 0, kPositionSize, kNormalSize, kUVSize, kColorSize, vertexObjectId, m.faces.size(),
            m.vertexArray ? m.vertexArray->GetVertexCount() : 0, m.vertexCount, m.triangleCount);

    FOO_GL_CHECK(glBindBuffer(GL_ARRAY_BUFFER, vertexObjectId));

    std::vector<GLushort> indices;
    GLushort count = 0;
    GLintptr offset = 0;

    for (int i = 0; i < m.faces.size(); i++) {
        auto face = m.faces[i];
        if (face.vertices.empty()) {
            break;
        }
        if (face.vertices.size() < 3) {
            std::string message;
            for (auto index : face.vertices) {
                message += " ";
                message += std::to_string(index);
            }
            FOO_ERROR("Face with only %d vertices:%s", (int32_t)face.vertices.size(), message.c_str());
            continue;
        }
        const auto vertexIndex = (GLushort)(face.vertices[0] - 1);
        // const auto normalIndex = (GLushort)(face.normals[0] - 1);
        const auto uvIndex = (GLushort)(kHasTextureCoords ? face.uvs[0] - 1 : -1);
        const YVRMath::vec3& firstVertex = m.vertexArray->GetVertex(vertexIndex);
        // const YVRMath::vec3& firstNormal = m.vertexArray->GetNormal(normalIndex);
        const YVRMath::vec3& firstUV = m.vertexArray->GetUV(uvIndex);
        for (int ix = 1; ix <= face.vertices.size() - 2; ix++) {
            FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kPositionSize, YVRMath::value_ptr(firstVertex)));
            offset += kPositionSize;
            // offset += kNormalSize;
            if (kHasTextureCoords) {
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kUVSize, YVRMath::value_ptr(firstUV)));
                offset += kUVSize;
            }
            if (kHasColor) {
                const Color& firstColor = m.vertexArray->GetColor(vertexIndex);
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kColorSize, firstColor.Data()));
                offset += kColorSize;
            }
            indices.push_back(count);
            count++;

            const YVRMath::vec3 v1 = m.vertexArray->GetVertex(face.vertices[ix] - 1);
            // const YVRMath::vec3 n1 = m.vertexArray->GetNormal(face.normals[ix] - 1);
            FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kPositionSize, YVRMath::value_ptr(v1)));
            offset += kPositionSize;
            // offset += kNormalSize;
            if (kHasTextureCoords) {
                const YVRMath::vec3 uv1 = m.vertexArray->GetUV(face.uvs[ix] - 1);
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kUVSize, YVRMath::value_ptr(uv1)));
                offset += kUVSize;
            }
            if (kHasColor) {
                const Color& color1 = m.vertexArray->GetColor(face.vertices[ix] - 1);
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kColorSize, color1.Data()));
                offset += kColorSize;
            }
            indices.push_back(count);
            count++;

            const YVRMath::vec3 v2 = m.vertexArray->GetVertex(face.vertices[ix + 1] - 1);
            // const YVRMath::vec3 n2 = m.vertexArray->GetNormal(face.normals[ix + 1] - 1);
            FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kPositionSize, YVRMath::value_ptr(v2)));
            offset += kPositionSize;
            // offset += kNormalSize;
            if (kHasTextureCoords) {
                const YVRMath::vec3 uv2 = m.vertexArray->GetUV(face.uvs[ix + 1] - 1);
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kUVSize, YVRMath::value_ptr(uv2)));
                offset += kUVSize;
            }
            if (kHasColor) {
                const Color& color2 = m.vertexArray->GetColor(face.vertices[ix + 1] - 1);
                FOO_GL_CHECK(glBufferSubData(GL_ARRAY_BUFFER, offset, kColorSize, color2.Data()));
                offset += kColorSize;
            }
            indices.push_back(count);
            count++;
        }
    }

    FOO_GL_CHECK(glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, indexObjectId));
    FOO_GL_CHECK(glBufferSubData(GL_ELEMENT_ARRAY_BUFFER, 0, sizeof(GLushort) * indices.size(), indices.data()));

    FOO_GL_CHECK(glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, 0));
    FOO_GL_CHECK(glBindBuffer(GL_ARRAY_BUFFER, 0));
}

// From ResourceGL
bool BoundaryGeometry::SupportOffRenderThreadInitialization() { return false; }
void BoundaryGeometry::InitializeGL() {
    if (!m.vertexArray) {
        FOO_ERROR("Unable to initialize Geometry Node. No VertexArray set");
        return;
    }
    size_t definedOffset = 0;
    m.renderBuffer->DefinePosition(definedOffset);
    definedOffset = m.renderBuffer->PositionOffset() + m.renderBuffer->PositionSize();
    // m.renderBuffer->DefineNormal(definedOffset);
    // definedOffset = m.renderBuffer->NormalOffset() + m.renderBuffer->NormalSize();
    if (m.vertexArray->GetUVCount() > 0) {
        m.renderBuffer->DefineUV(definedOffset, m.vertexArray->GetUVLength());
        definedOffset = m.renderBuffer->UVOffset() + m.renderBuffer->UVSize();
    }
    if (m.vertexArray->GetColorCount() > 0) {
        m.renderBuffer->DefineColor(definedOffset);
    }

    GLuint vertexObjectId = 0;
    GLuint indexObjectId = 0;
    FOO_GL_CHECK(glGenBuffers(1, &vertexObjectId));
    FOO_GL_CHECK(glBindBuffer(GL_ARRAY_BUFFER, vertexObjectId));
    GLsizei kTriangleSize = m.renderBuffer->VertexSize() * 3;

    FOO_GL_CHECK(glBufferData(GL_ARRAY_BUFFER, kTriangleSize * m.triangleCount, nullptr, GL_STATIC_DRAW));
    FOO_LOG("Allocate: %d for GL_ARRAY_BUFFER: %d", kTriangleSize * m.triangleCount, vertexObjectId);

    FOO_GL_CHECK(glGenBuffers(1, &indexObjectId));
    FOO_GL_CHECK(glBindBuffer(GL_ELEMENT_ARRAY_BUFFER, indexObjectId));
    FOO_GL_CHECK(glBufferData(GL_ELEMENT_ARRAY_BUFFER, sizeof(GLushort) * m.triangleCount * 3, nullptr, GL_STATIC_DRAW));
    FOO_LOG("Allocate: %d for GL_ELEMENT_ARRAY_BUFFER: %d", (int32_t)sizeof(GLushort) * m.triangleCount * 3, indexObjectId);
    m.renderBuffer->SetVertexObject(vertexObjectId, m.vertexCount);
    m.renderBuffer->SetIndexObject(indexObjectId, m.triangleCount * 3);

    UpdateBuffers();
}
void BoundaryGeometry::ShutdownGL() {}

BoundaryGeometry::BoundaryGeometry(State& aState, CreationContextPtr& aContext) : AbstractGeometry(aState, aContext), BoundaryGeometryDrawable(aState, aContext), m(aState) {
    m.renderBuffer = RenderBuffer::Create(aContext);
}
BoundaryGeometry::~BoundaryGeometry() {}

}  // namespace foo