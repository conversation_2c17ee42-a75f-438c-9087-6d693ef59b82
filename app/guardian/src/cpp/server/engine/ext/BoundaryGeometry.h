#pragma once

#include "foo/AbstractGeometry.h"
#include "foo/MacroUtils.h"
#include "BoundaryGeometryDrawable.h"

namespace foo {

class BoundaryGeometry;
using BoundaryGeometryPtr = std::shared_ptr<BoundaryGeometry>;

class BoundaryGeometry : public AbstractGeometry, public BoundaryGeometryDrawable {
 public:
    static BoundaryGeometryPtr Create(CreationContextPtr& aContext);
    void UpdateBuffers() override;

 protected:
    struct State;
    BoundaryGeometry(State& aState, CreationContextPtr& aContext);
    ~BoundaryGeometry();

    // From ResourceGL
    bool SupportOffRenderThreadInitialization() override;
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    BoundaryGeometry() = delete;

    FOO_NO_DEFAULTS(BoundaryGeometry)
};

}  // namespace foo