#pragma once

#include "foo/AbstractGeometryDrawable.h"
#include "foo/private/AbstractGeometryDrawableState.h"

namespace foo {
class BoundaryGeometryDrawable;
using BoundaryGeometryDrawablePtr = std::shared_ptr<BoundaryGeometryDrawable>;

const class BoundaryGeometryDrawable : public AbstractGeometryDrawable {
 public:
    static BoundaryGeometryDrawablePtr Create(CreationContextPtr& aContext);
    void Draw(const Camera& aCamera, const YVRMath::mat4& aModelTransform) override;

 protected:
    struct State : public AbstractGeometryDrawable::State {};

    BoundaryGeometryDrawable(State& aState, CreationContextPtr& aContext);
    ~BoundaryGeometryDrawable();

 private:
    State& m;
    BoundaryGeometryDrawable() = delete;

    FOO_NO_DEFAULTS(BoundaryGeometryDrawable)
};

}  // namespace foo