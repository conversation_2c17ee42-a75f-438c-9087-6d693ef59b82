#pragma once
#include "foo/ProgramFactory.h"

#include <foo/Mutex.h>
#include "foo/BasicShaders.h"
#include "foo/ConcreteClass.h"
#include "foo/CreationContext.h"
#include "foo/LoaderThread.h"
#include "foo/ResourceGL.h"
#include "foo/private/ResourceGLState.h"

#include <foo/GLError.h>
#include <foo/Program.h>
#include <foo/ShaderUtil.h>
#include <string>
#include <unordered_map>

namespace foo {

class BoundaryProgramBuilder;
typedef std::shared_ptr<BoundaryProgramBuilder> BoundaryProgramBuilderPtr;

class BoundaryProgramBuilder : public ResourceGL {
 public:
    enum PROGRAM_TYPE {
        LOCAL,
        GAME,
        GROUND_CIRCLE,
    };

 public:
    static BoundaryProgramBuilderPtr Create();

    ProgramPtr GetProgram();
    void SetFeatures(const uint32_t aFeatureMask, const std::string& aCustomFragShader);
    void SetProgramType(PROGRAM_TYPE aType);
    void Finalize();

 protected:
    // ResourceGL Interface
    bool SupportOffRenderThreadInitialization() override;
    void InitializeGL() override;
    void ShutdownGL() override;

 protected:
    struct State;
    BoundaryProgramBuilder(State& aState);

 private:
    State& m;
};
}  // namespace foo