#include "BoundaryGeometryDrawable.h"
#include "foo/ConcreteClass.h"
#include "foo/Logger.h"
#include "foo/Camera.h"
#include "foo/GLError.h"
#include "foo/CameraEye.h"

namespace foo {

BoundaryGeometryDrawablePtr BoundaryGeometryDrawable::Create(CreationContextPtr& aContext) {
    return std::make_shared<ConcreteClass<BoundaryGeometryDrawable, BoundaryGeometryDrawable::State>>(aContext);
}

void BoundaryGeometryDrawable::Draw(const Camera& aCamera, const YVRMath::mat4& aModelTransform) {
    if (m.renderState->Enable(aCamera.GetPerspective(), aCamera.GetView(), aModelTransform)) {
        const bool kUseTexture = m.UseTexture();
        const bool kUseColor = m.UseColor();
        const GLsizei kSize = m.renderBuffer->VertexSize();
        m.renderBuffer->Bind();

        FOO_GL_CHECK(glVertexAttribPointer((GLuint)m.renderState->AttributePosition(), m.renderBuffer->PositionLength(), GL_FLOAT, GL_FALSE, kSize, (const GLvoid*)m.renderBuffer->PositionOffset()));

        bool hasNormal = m.renderState->AttributeNormal() != -1;
        if (hasNormal) {
            FOO_GL_CHECK(glVertexAttribPointer((GLuint)m.renderState->AttributeNormal(), m.renderBuffer->NormalLength(), GL_FLOAT, GL_FALSE, kSize, (const GLvoid*)m.renderBuffer->NormalOffset()));
        }
        if (kUseTexture) {
            FOO_GL_CHECK(glVertexAttribPointer((GLuint)m.renderState->AttributeUV(), m.renderBuffer->UVLength(), GL_FLOAT, GL_FALSE, kSize, (const GLvoid*)m.renderBuffer->UVOffset()));
        }
        if (kUseColor) {
            FOO_GL_CHECK(glVertexAttribPointer((GLuint)m.renderState->AttributeColor(), m.renderBuffer->ColorLength(), GL_FLOAT, GL_FALSE, kSize, (const GLvoid*)m.renderBuffer->ColorOffset()));
        }

        FOO_GL_CHECK(glEnableVertexAttribArray((GLuint)m.renderState->AttributePosition()));
        if (hasNormal) {
            FOO_GL_CHECK(glEnableVertexAttribArray((GLuint)m.renderState->AttributeNormal()));
        }
        if (kUseTexture) {
            FOO_GL_CHECK(glEnableVertexAttribArray((GLuint)m.renderState->AttributeUV()));
        }
        if (kUseColor) {
            FOO_GL_CHECK(glEnableVertexAttribArray((GLuint)m.renderState->AttributeColor()));
        }
        const int32_t maxLength = m.renderBuffer->IndexCount();
        if (m.rangeLength == 0) {
            FOO_GL_CHECK(glDrawElements(GL_TRIANGLES, maxLength, GL_UNSIGNED_SHORT, 0));
        } else if ((m.rangeStart + m.rangeLength) <= maxLength) {
            FOO_GL_CHECK(glDrawElements(GL_TRIANGLES, m.rangeLength, GL_UNSIGNED_SHORT, reinterpret_cast<void*>(m.rangeStart * sizeof(GLushort))));
        } else {
            FOO_WARN("Invalid geometry range (%u-%u). Max geometry length %d", m.rangeStart, m.rangeLength + m.rangeLength, maxLength);
        }
        FOO_GL_CHECK(glDisableVertexAttribArray((GLuint)m.renderState->AttributePosition()));
        if (hasNormal) {
            FOO_GL_CHECK(glDisableVertexAttribArray((GLuint)m.renderState->AttributeNormal()));
        }
        if (kUseTexture) {
            FOO_GL_CHECK(glDisableVertexAttribArray((GLuint)m.renderState->AttributeUV()));
        }
        if (kUseColor) {
            FOO_GL_CHECK(glDisableVertexAttribArray((GLuint)m.renderState->AttributeColor()));
        }
        m.renderBuffer->Unbind();
        m.renderState->Disable();
    }
}

BoundaryGeometryDrawable::BoundaryGeometryDrawable(State& aState, CreationContextPtr& aContext) : AbstractGeometryDrawable(aState, aContext), m(aState) {
    m.renderBuffer = RenderBuffer::Create(aContext);
}
BoundaryGeometryDrawable::~BoundaryGeometryDrawable() {}

}  // namespace foo