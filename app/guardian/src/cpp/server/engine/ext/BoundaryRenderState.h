
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "yvrmath/YVRMath.h"
#include "foo/gl.h"
#include "foo/AbstractRenderState.h"
#include "CommonType.h"

namespace foo {

class BoundaryRenderState;
typedef std::shared_ptr<BoundaryRenderState> BoundaryRenderStatePtr;

class BoundaryRenderState : public AbstractRenderState {
 public:
    static BoundaryRenderStatePtr Create(CreationContextPtr& aContext);
    void SetProgram(ProgramPtr& aProgram);
    GLint AttributePosition() const;
    GLint AttributeNormal() const;
    GLint AttributeUV() const;
    GLint AttributeColor() const;
    uint32_t GetLightId() const;
    void ResetLights(const uint32_t aId);
    void AddLight(const YVRMath::vec3& aDirection, const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular);
    void SetMaterial(const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular, const float aSpecularExponent);
    void SetAmbient(const Color& aColor);
    void SetDiffuse(const Color& aColor);
    void GetMaterial(Color& aAmbient, Color& aDiffuse, Color& aSpecular, float& aSpecularExponent) const;
    GLint UVLength() const;
    TexturePtr GetTexture() const;
    void SetTexture(const TexturePtr& aTexture);
    bool HasTexture() const;
    const Color& GetTintColor() const;
    void SetTintColor(const Color& aColor);
    bool Enable(const YVRMath::mat4& aPerspective, const YVRMath::mat4& aView, const YVRMath::mat4& aModel);
    void Disable();
    void SetLightsEnabled(bool aEnabled);
    void SetUVTransform(const YVRMath::mat4& aMatrix);

    void UpdateFrameStatus(BoundaryFrameStatus aStatus);

 protected:
    struct State;
    BoundaryRenderState(State& aState, CreationContextPtr& aContext);
    ~BoundaryRenderState() = default;

    // ResourceGL interface
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    BoundaryRenderState() = delete;
    FOO_NO_DEFAULTS(BoundaryRenderState)
};

}  // namespace foo
