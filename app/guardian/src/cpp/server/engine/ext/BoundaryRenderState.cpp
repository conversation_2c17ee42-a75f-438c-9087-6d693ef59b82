
#include "foo/private/ResourceGLState.h"

#include "foo/BasicShaders.h"
#include "foo/Color.h"
#include "foo/ConcreteClass.h"
#include "foo/Logger.h"
#include "foo/Program.h"
#include "foo/ShaderUtil.h"
#include "foo/Texture.h"
#include <foo/ProgramFactory.h>
#include <string>
#include "foo/gl.h"
#include <vector>
#include "BoundaryRenderState.h"
#include "foo/private/AbstractRenderState_.h"
#include "foo/Logger.h"
#include "foo/GLError.h"
#include "trackingapi/TrackingAPI_Helper.h"
#include "yvrmath/YVRMath.h"

namespace foo {

struct BoundaryRenderState::State : public AbstractRenderState::State {
    // struct Light {
    //     const YVRMath::vec3 direction;
    //     const Color ambient;
    //     const Color diffuse;
    //     const Color specular;

    //     Light(const YVRMath::vec3& aDirection, const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular)
    //         : direction(aDirection), ambient(aAmbient), diffuse(aDiffuse), specular(aSpecular) {}
    // };
    // struct ULight {
    //     GLint direction;
    //     GLint ambient;
    //     GLint diffuse;
    //     GLint specular;
    //     ULight() : direction(0), ambient(0), diffuse(0), specular(0) {}
    // };

    ProgramPtr program;
    bool updateProgram;
    GLint aPosition;
    GLint aNormal;
    GLint aUV;
    GLint aProjMtx;
    GLint aViewMtx;
    GLint aMdlMtx;
    GLint aBoundaryParams;
    GLint aHeadCDUV;
    GLint aLeftCDUV;
    GLint aRightCDUV;
    GLint aTimeParam;
    BoundaryFrameStatus frameStatus;

    State()
        : program(0),
          updateProgram(false),
          aPosition(-1),
          aNormal(-1),
          aUV(-1),
          aProjMtx(-1),
          aViewMtx(-1),
          aMdlMtx(-1),
          aBoundaryParams(-1),
          aHeadCDUV(-1),
          aLeftCDUV(-1),
          aRightCDUV(-1),
          aTimeParam(-1) {}

    void InitializeProgram();
};

void BoundaryRenderState::State::InitializeProgram() {
    if (!program || program->GetProgram() == 0) {
        return;
    }
    aPosition = program->GetAttributeLocation("position");
    aUV = program->GetAttributeLocation("texcoord0");
    aProjMtx = program->GetUniformLocation("projMtx");
    aViewMtx = program->GetUniformLocation("viewMtx");
    aMdlMtx = program->GetUniformLocation("mdlMtx");
    aBoundaryParams = program->GetUniformLocation("BoundaryParams");
    aHeadCDUV = program->GetUniformLocation("headCDUV");
    aLeftCDUV = program->GetUniformLocation("leftCDUV");
    aRightCDUV = program->GetUniformLocation("rightCDUV");
    aTimeParam = program->GetUniformLocation("timeParam");
    FOO_LOG("BoundaryRenderState::State::InitializeProgram [%d,%d,%d,%d,%d,%d,%d,%d,%d,%d]", aPosition, aUV, aProjMtx, aViewMtx, aMdlMtx, aBoundaryParams, aHeadCDUV, aLeftCDUV, aRightCDUV,
            aTimeParam);

    updateProgram = false;
}

BoundaryRenderStatePtr BoundaryRenderState::Create(CreationContextPtr& aContext) { return std::make_shared<ConcreteClass<BoundaryRenderState, BoundaryRenderState::State>>(aContext); }

void BoundaryRenderState::SetProgram(ProgramPtr& aProgram) {
    m.program = aProgram;
    m.updateProgram = true;
    FOO_LOG("BoundaryRenderState::SetProgram [%d]", m.program->GetProgram());
}

GLint BoundaryRenderState::AttributePosition() const { return m.aPosition; }

GLint BoundaryRenderState::AttributeNormal() const { return m.aNormal; }

GLint BoundaryRenderState::AttributeUV() const { return m.aUV; }

GLint BoundaryRenderState::AttributeColor() const { return -1; }

uint32_t BoundaryRenderState::GetLightId() const { return -1; }

void BoundaryRenderState::ResetLights(const uint32_t aId) {
    // m.lightId = aId;
    // m.lights.clear();
}

void BoundaryRenderState::AddLight(const YVRMath::vec3& aDirection, const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular) {
    // m.lights.push_back(State::Light(aDirection, aAmbient, aDiffuse, aSpecular));
}

void BoundaryRenderState::SetMaterial(const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular, const float aSpecularExponent) {
    // m.ambient = aAmbient;
    // m.diffuse = aDiffuse;
    // m.specular = aSpecular;
    // m.specularExponent = aSpecularExponent;
}

void BoundaryRenderState::SetAmbient(const Color& aColor) {
    //  m.ambient = aColor;
}

void BoundaryRenderState::SetDiffuse(const Color& aColor) {
    //  m.diffuse = aColor;
}

void BoundaryRenderState::GetMaterial(Color& aAmbient, Color& aDiffuse, Color& aSpecular, float& aSpecularExponent) const {
    // aAmbient = m.ambient;
    // aDiffuse = m.diffuse;
    // aSpecular = m.specular;
    // aSpecularExponent = m.specularExponent;
}

GLint BoundaryRenderState::UVLength() const {
    // TexturePtr texture = m.texture.lock();
    // if (!texture) {
    //     return 0;
    // }
    // return texture->GetTarget() == GL_TEXTURE_CUBE_MAP ? 3 : 2;

    return 2;
}

TexturePtr BoundaryRenderState::GetTexture() const {
    // return m.texture.lock();

    return nullptr;
}

void BoundaryRenderState::SetTexture(const TexturePtr& aTexture) {
    // m.texture = aTexture;
}

bool BoundaryRenderState::HasTexture() const {
    // return m.texture.lock() != nullptr;
    return m.aUV != -1;
}

const Color& BoundaryRenderState::GetTintColor() const {
    // return m.tintColor;
    return Color();
}

void BoundaryRenderState::SetTintColor(const Color& aColor) {
    // m.tintColor = aColor;
}

// #define SECONDS_TO_NANOSECONDS 1e9
// #define NANOSECONDS_TO_SECONDS 1e-9
// double GetTime_Sec() {
//     struct timespec now;
//     clock_gettime(CLOCK_MONOTONIC, &now);
//     return (now.tv_sec * SECONDS_TO_NANOSECONDS + now.tv_nsec) * NANOSECONDS_TO_SECONDS;
// }

bool BoundaryRenderState::Enable(const YVRMath::mat4& aPerspective, const YVRMath::mat4& aView, const YVRMath::mat4& aModel) {
    if (!m.program) {
        FOO_LOG("BoundaryRenderState::program empty");
        return false;
    }
    if (!m.program->Enable()) {
        FOO_LOG("BoundaryRenderState::program not enable");
        return false;
    }
    if (m.updateProgram) {
        FOO_LOG("BoundaryRenderState::InitializeProgram");
        m.InitializeProgram();
    }

    // Disable Face Culling
    FOO_GL_CHECK(glDisable(GL_CULL_FACE));
    FOO_GL_CHECK(glUniformMatrix4fv(m.aProjMtx, 1, GL_FALSE, YVRMath::value_ptr(aPerspective)));
    FOO_GL_CHECK(glUniformMatrix4fv(m.aViewMtx, 1, GL_FALSE, YVRMath::value_ptr(aView)));
    FOO_GL_CHECK(glUniformMatrix4fv(m.aMdlMtx, 1, GL_FALSE, YVRMath::value_ptr(aModel)));
    auto frameStatus = m.frameStatus;
    FOO_GL_CHECK(glUniform4fv(m.aBoundaryParams, 1, YVRMath::value_ptr(frameStatus.boundaryParams)));
    FOO_GL_CHECK(glUniform4fv(m.aHeadCDUV, 1, YVRMath::value_ptr(frameStatus.headCDUV)));
    FOO_GL_CHECK(glUniform4fv(m.aLeftCDUV, 1, YVRMath::value_ptr(frameStatus.leftCDUV)));
    FOO_GL_CHECK(glUniform4fv(m.aRightCDUV, 1, YVRMath::value_ptr(frameStatus.rightCDUV)));
    FOO_GL_CHECK(glUniform4fv(m.aTimeParam, 1, YVRMath::value_ptr(frameStatus.timeParam)));
    // float time = GetTime_Sec();
    // YVRMath::vec4 BoundaryParams = YVRMath::vec4(time, 1.0, 0.f, 0.0f);
    // FOO_GL_CHECK(glUniform4fv(m.aBoundaryParams, 1, YVRMath::value_ptr(BoundaryParams)));
    // FOO_GL_CHECK(glUniformMatrix4fv(m.aProjMtx, 1, GL_FALSE, YVRMath::value_ptr(aPerspective)));

    // FOO_GL_CHECK(glUniform4fv(location, 1, YVRMath::value_ptr(vector)));
    // GLAPI void APIENTRY glUniform4fv(GLint location, GLsizei count, const GLfloat* value);
    return true;
}

void BoundaryRenderState::Disable() {
    // if (TexturePtr texture = m.texture.lock()) {
    //     glActiveTexture(GL_TEXTURE0);
    //     texture->Unbind();
    // }
}
void BoundaryRenderState::UpdateFrameStatus(BoundaryFrameStatus aStatus) { m.frameStatus = aStatus; }

void BoundaryRenderState::SetLightsEnabled(bool aEnabled) {}

void BoundaryRenderState::SetUVTransform(const YVRMath::mat4& aMatrix) {}

BoundaryRenderState::BoundaryRenderState(State& aState, CreationContextPtr& aContext) : AbstractRenderState(aState, aContext), m(aState) {}

void BoundaryRenderState::InitializeGL() {}

void BoundaryRenderState::ShutdownGL() { m.updateProgram = true; }

}  // namespace foo
