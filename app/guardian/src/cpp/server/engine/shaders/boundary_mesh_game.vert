R"SHADER(
#version 300 es

in vec3 position;      // Position
in vec2 texcoord0;     // Tex Coord

uniform mat4 projMtx;
uniform mat4 viewMtx;
uniform mat4 mdlMtx;
uniform  vec4 BoundaryParams;
uniform  vec4 headCDUV;
uniform  vec4 leftCDUV;
uniform  vec4 rightCDUV;
uniform  vec4 timeParam;

out vec2 vTexcoord;

void main()
{
	vTexcoord.xy = texcoord0.xy;
	gl_Position = projMtx * (viewMtx * (mdlMtx * vec4(position.xyz, 1.0)));
}
)SHADER";