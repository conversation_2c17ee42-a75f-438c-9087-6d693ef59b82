R"SHADER(
#version 300 es
precision highp float;

uniform  mat4 projMtx;
uniform  mat4 viewMtx;
uniform  mat4 mdlMtx;
uniform  vec4 BoundaryParams;
uniform  vec4 headCDUV;
uniform  vec4 leftCDUV;
uniform  vec4 rightCDUV;
uniform  vec4 timeParam;

in vec2 vTexcoord; 
out vec4 outColor;

vec4 clearColor = vec4(0.0,0.0,0.0,0.0);
vec4 BaseColor1 = vec4(0.2157, 0.2039,0.8706,1.0);
vec4 BaseColor2 = vec4(0.1647, 0.5725,0.9569,1.0);
const float numOfGrid = 6.;

float distanceToCDPos(vec3 CDPos)   
{
    vec2 coord = vTexcoord * vec2(BoundaryParams.x, BoundaryParams.y);
    vec2 midV = CDPos.xy - coord;
    float dis1 = dot(midV, midV);
    midV.x = BoundaryParams.x + CDPos.x - coord.x;
    float dis2 = dot(midV, midV);
    midV.x = CDPos.x - coord.x - BoundaryParams.x;
    float dis3 = dot(midV, midV);
    float dis = min(min(dis1, dis2), dis3);
    dis = dis + CDPos.z * CDPos.z;
    return dis;
}

void normalBaseColor(float judgeDis)
{
     float headDistance = distanceToCDPos(headCDUV.xyz); 
     if(headDistance > 0.7) { 
          discard;  
     } 
     vec4 BaseColor = mix(BaseColor1, BaseColor2, vTexcoord.y);
     vec2 coord = vec2(0.0);
     coord.y = vTexcoord.y * numOfGrid;
     float rate = floor(numOfGrid * BoundaryParams.x / BoundaryParams.y);
     coord.x = vTexcoord.x * rate;
     vec2 fractPart = fract(coord.xy);
     vec2 gridLerp = step(0.98, fractPart);
     float lerpValue = step(1.0, gridLerp.x + gridLerp.y);
     vec4 color = mix(clearColor, BaseColor, lerpValue);
     vec2 clearBand = fract(coord.xy + vec2(0.51));
     vec2 judgeV = step((1.0 - judgeDis * 2.5), abs(clearBand - vec2(0.5)));
     vec4 c1 = color * (1.0 - judgeV.x) * (1.0 - judgeV.y);
     if(c1.w < 0.5)
     {
          discard;
     }
     outColor = c1;
}

void normalBaseColor()
{
     vec4 BaseColor = mix(BaseColor1, BaseColor2, vTexcoord.y);
     vec2 coord = vec2(0.0);
     coord.y = vTexcoord.y * numOfGrid;
     float rate = floor(numOfGrid * BoundaryParams.x / BoundaryParams.y);
     coord.x = vTexcoord.x * rate;
     vec2 fractPart = fract(coord.xy);
     vec2 gridLerp = step(0.98, fractPart);
     float lerpValue = step(1.0, gridLerp.x + gridLerp.y);
     vec4 color = mix(clearColor, BaseColor, lerpValue);
     if(color.w < 0.5)
     {
          discard;
     }
     outColor = color;
}

void normalRedCircle(float inten, float dis, float judgeDis)
{
    vec4 BaseColor = mix(BaseColor1,BaseColor2, vTexcoord.y);
    vec4 BaseEventColor = vec4(1.0, 0.0,0.129,1.0);
    vec2 Texcoord = vec2(0.0);
    Texcoord.y = vTexcoord.y * numOfGrid;
    float rate = floor(numOfGrid * BoundaryParams.x / BoundaryParams.y);
    Texcoord.x = vTexcoord.x * rate;
  
    BaseColor = mix(BaseEventColor, BaseColor, smoothstep(0.6666666666 * inten, inten, dis));
    vec2 fractPart = fract(Texcoord.xy);
    vec2 gridLerp = step(0.98, fractPart);
    float lerpValue = step(1.0, gridLerp.x + gridLerp.y);
    vec4 color = mix(clearColor, BaseColor, lerpValue);
     vec2 clearBand = fract(Texcoord.xy + vec2(0.51));
     vec2 judgeV = step((1.0 - judgeDis * 2.5), abs(clearBand - vec2(0.5)));
     vec4 c1 = color * (1.0 - judgeV.x) * (1.0 - judgeV.y);
     if(c1.w < 0.5)
     {
          discard;
     }
     outColor = c1;
}

void main()
{
    float headDis = 10000000.0;
    if(headCDUV.z < headCDUV.w)
    {
         headDis = distanceToCDPos(headCDUV.xyz);
    }

   float dis = headDis;
   vec2 inten = headCDUV.zw;// headDis < leftDis ? headCDUV.zw : leftCDUV.zw;
   dis = sqrt(dis);
   float judgeDis = headCDUV.z;
   if(dis < 0.425 * inten.y)
   {
     discard;
   }
   if(dis > 0.425 * inten.y && dis < 0.5 * inten.y)
   {
     outColor = vec4(1.0, 0.0,0.129,1.0);
     return;
   }
   if(dis > 0.5 * inten.y  && dis < inten.y)
   {
     normalRedCircle(inten.y, dis, judgeDis);
     return;
   }
   if(BoundaryParams.z > 0.0)
   {
     normalBaseColor();
   }
   else if(dis > inten.y)
   {
     normalBaseColor(judgeDis);
     return;
   }
}
)SHADER";