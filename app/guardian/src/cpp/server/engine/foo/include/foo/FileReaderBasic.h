

#pragma once

#include "foo/FileReader.h"
#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <memory>
#include "foo/gl.h"

namespace foo {

class FileReaderBasic : public FileReader {
 public:
    static FileReaderBasicPtr Create();
    void ReadRawFile(const std::string& aFileName, FileHandlerPtr aHandler) override;
    void ReadImageFile(const std::string& aFileName, FileHandlerPtr aHandler) override;

 protected:
    struct State;
    FileReaderBasic(State& aState);
    ~FileReaderBasic();

 private:
    State& m;
    FileReaderBasic() = delete;
    FOO_NO_DEFAULTS(FileReaderBasic)
};

}  // namespace foo
