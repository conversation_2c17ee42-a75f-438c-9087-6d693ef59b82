

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include "foo/gl.h"

namespace foo {

class GLExtensions {
 public:
    // Known GL extensions that can be queried by IsExtensionSupported.
    enum class Ext { EXT_multisampled_render_to_texture, OVR_multiview, OVR_multiview2, OVR_multiview_multisampled_render_to_texture };

    // GL extension function pointers
    struct Functions {
        PFNGLRENDERBUFFERSTORAGEMULTISAMPLEEXTPROC glRenderbufferStorageMultisampleEXT;
        PFNGLFRAMEBUFFERTEXTURE2DMULTISAMPLEEXTPROC glFramebufferTexture2DMultisampleEXT;
        PFNGLFRAMEBUFFERTEXTUREMULTIVIEWOVRPROC glFramebufferTextureMultiviewOVR;
        PFNGLFRAMEBUFFERTEXTUREMULTISAMPLEMULTIVIEWOVRPROC glFramebufferTextureMultisampleMultiviewOVR;
    };

    static GLExtensionsPtr Create(RenderContextPtr& aContext);
    void Initialize();
    bool IsExtensionSupported(GLExtensions::Ext aExtension) const;
    const GLExtensions::Functions& GetFunctions() const;

 protected:
    struct State;
    GLExtensions(State& aState);
    ~GLExtensions();

 private:
    State& m;
    GLExtensions() = delete;
    FOO_NO_DEFAULTS(GLExtensions)
};

}  // namespace foo
