
#pragma once

#include "foo/CullVisitor.h"

namespace foo {

struct CullVisitor::State {
    struct TransformNode {
        TransformNode* prev;
        YVRMath::mat4 transform;
        TransformNode() : prev(nullptr), transform(YVRMath::mat4()) {}
    };

    const YVRMath::mat4 identity;
    TransformNode* transformList;

    State() : identity(YVRMath::mat4()), transformList(nullptr) {}
    ~State() { Reset(); }
    void Reset();
};

}  // namespace foo
