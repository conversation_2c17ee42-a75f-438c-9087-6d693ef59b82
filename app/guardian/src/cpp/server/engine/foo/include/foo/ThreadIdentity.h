#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class ThreadIdentity {
 public:
    static ThreadIdentityPtr Create();
    bool IsOnInitializationThread() const;
    bool IsSameThread(const ThreadIdentity& Identity) const;

 protected:
    struct State;
    ThreadIdentity(State& aState);
    ~ThreadIdentity() = default;

 private:
    State& m;
    ThreadIdentity() = delete;
    FOO_NO_DEFAULTS(ThreadIdentity)
};

}  // namespace foo
