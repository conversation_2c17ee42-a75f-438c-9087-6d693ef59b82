
#pragma once

#if defined(ANDROID)
#include <android/log.h>
#if defined(NDEBUG)
#define FOO_DEBUG(...)
#else
#define FOO_DEBUG(format, ...) __android_log_print(ANDROID_LOG_DEBUG, "FOO", format, ##__VA_ARGS__);
#endif
#define FOO_LOG(format, ...) __android_log_print(ANDROID_LOG_INFO, "FOO", format, ##__VA_ARGS__);
#define FOO_WARN(format, ...) __android_log_print(ANDROID_LOG_WARN, "FOO", format, ##__VA_ARGS__);
#define FOO_ERROR(format, ...) __android_log_print(ANDROID_LOG_ERROR, "FOO", format, ##__VA_ARGS__);
#else
#include <stdio.h>
#define FOO_DEBUG(format, ...) fprintf(stderr, "FOO DEBUG: " format "\n", ##__VA_ARGS__);
#define FOO_LOG(format, ...) fprintf(stderr, "FOO: " format "\n", ##__VA_ARGS__);
#define FOO_WARN(format, ...) fprintf(stderr, "FOO WARNING: " format "\n", ##__VA_ARGS__);
#define FOO_ERROR(format, ...) fprintf(stderr, "FOO ERROR: " format "\n", ##__VA_ARGS__);
#endif
#define FOO_LINE FOO_DEBUG("%s:%s:%d", __FILE__, __FUNCTION__, __LINE__);
