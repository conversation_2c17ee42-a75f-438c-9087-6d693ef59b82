
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "foo/Texture.h"

#include <string>
#include "foo/gl.h"

namespace foo {

class TextureGL : public Texture, protected ResourceGL {
 public:
    static TextureGLPtr Create(CreationContextPtr& aContext);

    void SetImageData(std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat);
    GLsizei GetWidth() const;
    GLsizei GetHeight() const;

 protected:
    struct State;
    TextureGL(State& aState, CreationContextPtr& aContext);
    ~TextureGL();

    // Texture interface
    void AboutToBind() override;

    // ResourceGL interface
    bool SupportOffRenderThreadInitialization() override;
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    TextureGL() = delete;
    FOO_NO_DEFAULTS(TextureGL)
};

}  // namespace foo
