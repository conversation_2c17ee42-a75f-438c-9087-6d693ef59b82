
#pragma once

#include <stddef.h>

#define FOO_NO_COPY(Class)        \
    Class(const Class&) = delete; \
    Class& operator=(const Class&) = delete;

#define FOO_NO_MOVE(Class)   \
    Class(Class&&) = delete; \
    Class& operator=(Class&&) = delete;

#define FOO_NO_DEFAULTS(Class) \
    FOO_NO_COPY(Class)         \
    FOO_NO_MOVE(Class)

#define FOO_NO_NEW_DELETE                         \
    static void* operator new(size_t) = delete;   \
    static void* operator new[](size_t) = delete; \
    static void operator delete(void*) = delete;  \
    static void operator delete[](void*) = delete;
