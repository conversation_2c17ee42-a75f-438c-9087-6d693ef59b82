
#pragma once
#include "foo/Texture.h"

#include <string>
#include <unordered_map>
#include "foo/gl.h"

namespace foo {

struct Texture::State {
    std::unordered_map<GLenum, GLint> intMap;
    std::string name;
    GLenum target;
    GLuint texture;

    State() : target(GL_TEXTURE_2D), texture(0) {
        intMap[GL_TEXTURE_MAG_FILTER] = GL_NEAREST;
        intMap[GL_TEXTURE_MIN_FILTER] = GL_NEAREST;
        intMap[GL_TEXTURE_WRAP_S] = GL_CLAMP_TO_EDGE;
        intMap[GL_TEXTURE_WRAP_T] = GL_CLAMP_TO_EDGE;
    }
};

}  // namespace foo
