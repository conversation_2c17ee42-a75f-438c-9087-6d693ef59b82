
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include "foo/gl.h"

namespace foo {

class FBO {
 public:
    struct Attributes {
        Attributes();
        Attributes(bool aDepth, bool aStencil, bool aMultiview, int aSamples);
        bool depth;
        bool stencil;
        bool multiview;
        int samples;
    };
    static FBOPtr Create(RenderContextPtr& aContext);
    bool IsValid() const;
    void SetTextureHandle(const GLuint aHandle, const int32_t aWidth, const int32_t aHeight, const FBO::Attributes& aAttributes = {});
    void Bind(GLenum target = GL_FRAMEBUFFER);
    void Unbind();
    const FBO::Attributes& GetAttributes() const;
    GLuint GetHandle() const;

 protected:
    struct State;
    FBO(State& aState);
    ~FBO();

 private:
    State& m;
    FBO() = delete;
    FOO_NO_DEFAULTS(FBO)
};

}  // namespace foo
