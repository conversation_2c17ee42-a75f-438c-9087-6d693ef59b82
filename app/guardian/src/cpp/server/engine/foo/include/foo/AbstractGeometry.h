
#pragma once

#include "foo/Forward.h"
// #include "foo/AbstractGeometryDrawable.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "foo/gl.h"

#include <vector>

namespace foo {

class AbstractGeometry : protected ResourceGL {
 public:
    struct Face {
        std::vector<GLushort> vertices;
        std::vector<GLushort> uvs;
        std::vector<GLushort> normals;
    };

    // Geometry interface
    VertexArrayPtr GetVertexArray() const;
    void SetVertexArray(const VertexArrayPtr& aVertexArray);
    virtual void UpdateBuffers() = 0;

    void AddFace(const std::vector<int>& aVerticies, const std::vector<int>& aUVs, const std::vector<int>& aNormals);

    int32_t GetFaceCount() const;
    const Face& GetFace(int32_t aIndex) const;

 protected:
    struct State;
    AbstractGeometry(State& aState, CreationContextPtr& aContext);
    ~AbstractGeometry();

    // From ResourceGL
    virtual bool SupportOffRenderThreadInitialization() = 0;
    virtual void InitializeGL() = 0;
    virtual void ShutdownGL() = 0;

 private:
    State& m;
    AbstractGeometry() = delete;

    FOO_NO_DEFAULTS(AbstractGeometry)
};

}  // namespace foo
