
#pragma once

#include "foo/gl.h"
#if defined(ANDROID)
#include <android/log.h>
#else
#include <stdio.h>
#endif

namespace foo {

const char* GLErrorString(GLenum aError);
const char* GLErrorCheck();

#if defined(ANDROID)

#define FOO_GL_CHECK(X)                                                                                                          \
    X;                                                                                                                           \
    {                                                                                                                            \
        const char* str = foo::GLErrorCheck();                                                                                   \
        if (str) {                                                                                                               \
            __android_log_print(ANDROID_LOG_ERROR, "FOO", "OpenGL Error: %s at%s:%s:%d", str, __FILE__, __FUNCTION__, __LINE__); \
        }                                                                                                                        \
    }

#else

#define FOO_GL_CHECK(X)                                                                                 \
    X;                                                                                                  \
    {                                                                                                   \
        const char* str = foo::GLErrorCheck();                                                          \
        if (str) {                                                                                      \
            fprintf(stderr, "FOO: OpenGL Error: %s at%s:%s:%d", str, __FILE__, __FUNCTION__, __LINE__); \
        }                                                                                               \
    }

#endif

}  // namespace foo
