
#pragma once
#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class ResourceGL {
 public:
    virtual bool SupportOffRenderThreadInitialization() { return false; }
    virtual void InitializeGL() = 0;
    virtual void ShutdownGL() = 0;

 protected:
    struct State;
    ResourceGL(State& aState, CreationContextPtr& aContext);
    ResourceGL(State& aState);
    virtual ~ResourceGL();

 private:
    State& m;
    ResourceGL() = delete;
    FOO_NO_DEFAULTS(ResourceGL)
};

}  // namespace foo
