
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#if defined(ANDROID)
#include <jni.h>
#endif  // defined(ANDROID)

namespace foo {

class RenderContext {
 public:
    static RenderContextPtr Create();

#if defined(ANDROID)
    void InitializeJava(JNIEnv* aEnv, jobject& aActivity, jobject& aAssetManager, jobject& aResource);
    void ShutdownJava();
#endif  // defined(ANDROID)
    bool IsOnRenderThread();
    bool InitializeGL();
    void ShutdownGL();
    void Update();
    double GetTimestamp();
    double GetFrameDelta();

    ThreadIdentityPtr& GetRenderThreadIdentity();
    DataCachePtr& GetDataCache();
    TextureCachePtr& GetTextureCache();
    ProgramFactoryPtr& GetProgramFactory();
    CreationContextPtr& GetRenderThreadCreationContext();
    GLExtensionsPtr GetGLExtensions() const;
#if defined(ANDROID)
    SurfaceTextureFactoryPtr GetSurfaceTextureFactory();
#endif  // defined(ANDROID)

    // Internal interface
    ResourceGLList& GetUninitializedResourceGLList();
    ResourceGLList& GetResourceGLList();
    UpdatableList& GetUpdatableList();
    void RegisterContextSynchronizer(ContextSynchronizerPtr& aSynchronizer);

 protected:
    struct State;
    RenderContext(State& aState);
    ~RenderContext();

 private:
    State& m;
    RenderContext() = delete;
    FOO_NO_DEFAULTS(RenderContext)
};

}  // namespace foo
