#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Texture.h"

#include <string>
#include "foo/gl.h"

namespace foo {

class TextureSurface : public Texture {
 public:
    static TextureSurfacePtr Create(RenderContextPtr& aContext, const std::string& aName);

    void SetTextureHandle(const GLuint aHandle);

 protected:
    struct State;
    TextureSurface(State& aState, CreationContextPtr& aContext);
    ~TextureSurface();

    // Texture interface
    void AboutToBind() override;

 private:
    State& m;
    TextureSurface() = delete;
    FOO_NO_DEFAULTS(TextureSurface)
};

}  // namespace foo
