

#pragma once
#include <android/log.h>
#include "foo/gl.h"

namespace foo {

const char* EGLErrorString();
const char* EGLErrorString(GLenum aError);
const char* EGLErrorCheck();

#define FOO_EGL_CHECK(X)                                                                                                      \
    X;                                                                                                                        \
    {                                                                                                                         \
        const char* str = foo::EGLErrorCheck();                                                                               \
        if (str) {                                                                                                            \
            __android_log_print(ANDROID_LOG_ERROR, "FOO", "EGL Error: %s at%s:%s:%d", str, __FILE__, __FUNCTION__, __LINE__); \
        }                                                                                                                     \
    }

}  // namespace foo
