
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <functional>
#include <string>
#include <vector>

namespace foo {

class Node {
 public:
    const std::string& GetName() const;
    void SetName(const std::string& aName);
    void GetParents(std::vector<GroupPtr>& aParents) const;
    void RemoveFromParents();
    virtual void Cull(CullVisitor& aVisitor, DrawableList& aDrawables) = 0;
    using TraverseFunction = std::function<bool(const NodePtr& aNode, const GroupPtr& aTraversingFrom)>;
    static bool Traverse(const NodePtr& aRootNode, const TraverseFunction& aTraverseFunction);

 protected:
    struct State;
    Node(State& aState, CreationContextPtr& aContext);
    virtual ~Node();
    static void AddToParents(GroupWeak& aParent, Node& aChild);
    static void RemoveFromParents(Group& aParent, Node& aChild);
    virtual bool Traverse(const GroupPtr& aParent, const TraverseFunction& aTraverseFunction);

 private:
    State& m;
    Node() = delete;
    FOO_NO_DEFAULTS(Node)
};

}  // namespace foo
