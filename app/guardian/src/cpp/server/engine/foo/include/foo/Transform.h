
#pragma once

#include "foo/Forward.h"
#include "foo/Group.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class Transform : public Group {
 public:
    static TransformPtr Create(CreationContextPtr& aContext);
    // Node interface
    void Cull(CullVisitor& aVisitor, DrawableList& aDrawables) override;
    // Transform interface
    const YVRMath::mat4 GetWorldTransform() const;
    const YVRMath::mat4& GetTransform() const;
    virtual void SetTransform(const YVRMath::mat4& aTransform);

 protected:
    struct State;
    Transform(State& aState, CreationContextPtr& aContext);
    ~Transform();

 private:
    State& m;
    Transform() = delete;
    FOO_NO_DEFAULTS(Transform)
};

}  // namespace foo
