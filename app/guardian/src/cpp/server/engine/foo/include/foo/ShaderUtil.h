

#pragma once

#include <string>
#include "foo/gl.h"

namespace foo {

GLint GetAttributeLocation(GLuint aProgram, const char* aName);
GLint GetAttributeLocation(GLuint aProgram, const std::string& aName);
GLint GetUniformLocation(GLuint aProgram, const char* aName);
GLint GetUniformLocation(GLuint aProgram, const std::string& aName);
GLuint LoadShader(GLenum type, const char* src);
GLuint CreateProgram(GLuint aVertexShader, GLuint aFragmentShader);

}  // namespace foo
