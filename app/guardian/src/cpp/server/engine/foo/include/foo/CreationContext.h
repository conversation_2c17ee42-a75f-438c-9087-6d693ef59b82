

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include <string>

namespace foo {

class CreationContext {
 public:
    static CreationContextPtr Create(RenderContextPtr& aContext);
    void BindToThread();
    void Synchronize();

    void RegisterContextSynchronizerObserver(ContextSynchronizerObserverPtr& aObserver);
    void ReleaseContextSynchronizerObserver(ContextSynchronizerObserverPtr& aObserver);
    void SetFileReader(FileReaderPtr aFileReader);
    DataCachePtr GetDataCache();
    FileReaderPtr GetFileReader();
    ProgramFactoryPtr GetProgramFactory();
    TextureGLPtr LoadTexture(const std::string& TextureName, const bool aUseCache = true);
    void UpdateResourceGL();
    void AddResourceGL(ResourceGL* aResource);
    void AddUpdatable(Updatable* aUpdatable);
    foo::TextureGLPtr GetDefaultTexture() const;

 protected:
    struct State;
    CreationContext(State& aState);
    ~CreationContext();

 private:
    State& m;
    CreationContext() = delete;
    FOO_NO_DEFAULTS(CreationContext)
};

}  // namespace foo
