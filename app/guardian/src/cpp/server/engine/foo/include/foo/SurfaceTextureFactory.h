#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include "foo/ResourceGL.h"
#include "foo/Updatable.h"

#include <jni.h>
#include <string>
#include "foo/gl.h"

namespace foo {

class SurfaceTextureObserver {
 public:
    virtual void SurfaceTextureCreated(const std::string& aName, GLuint aHandle, jobject aSurfaceTexture) = 0;
    virtual void SurfaceTextureHandleUpdated(const std::string aName, GLuint aHandle) = 0;
    virtual void SurfaceTextureDestroyed(const std::string& aName) = 0;
    virtual void SurfaceTextureCreationError(const std::string& aName, const std::string& aReason) = 0;

 protected:
    SurfaceTextureObserver() {}
    virtual ~SurfaceTextureObserver() {}

 private:
    FOO_NO_DEFAULTS(SurfaceTextureObserver)
};

class SurfaceTextureFactory : protected Updatable, protected ResourceGL {
 public:
    static SurfaceTextureFactoryPtr Create(CreationContextPtr& aContext);
    void InitializeJava(JNIEnv* aEnv);
    void ShutdownJava();

    void CreateSurfaceTexture(const std::string& aName, SurfaceTextureObserverPtr aObserver);
    void DestroySurfaceTexture(const std::string& aName);
    void AddGlobalObserver(SurfaceTextureObserverPtr aObserver);
    void RemoveGlobalObserver(const SurfaceTextureObserver& aObserver);

    jobject LookupSurfaceTexture(const std::string& aName);

 protected:
    struct State;
    SurfaceTextureFactory(State& aState, CreationContextPtr& aContext);
    ~SurfaceTextureFactory();

    // Updatable interface
    void UpdateResource(RenderContext& aContext) override;

    // ResourceGL interface
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    SurfaceTextureFactory() = delete;
    FOO_NO_DEFAULTS(SurfaceTextureFactory)
};

}  // namespace foo
