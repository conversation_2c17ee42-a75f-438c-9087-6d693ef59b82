
#pragma once

#include "foo/Forward.h"
#include "foo/Group.h"
#include "foo/MacroUtils.h"

namespace foo {

class Toggle : public Group {
 public:
    static TogglePtr Create(CreationContextPtr& aContext);

    // Group interface
    void RemoveNode(Node& aNode) override;

    // Toggle interface
    void ToggleAll(const bool aEnabled);
    bool IsEnabled(const Node& aNode);
    void ToggleChild(const Node& aNode, const bool aEnabled);

 protected:
    typedef Group Super;
    struct State;
    Toggle(State& aState, CreationContextPtr& aContext);
    ~Toggle();

 private:
    State& m;
    Toggle() = delete;
    FOO_NO_DEFAULTS(Toggle)
};

}  // namespace foo
