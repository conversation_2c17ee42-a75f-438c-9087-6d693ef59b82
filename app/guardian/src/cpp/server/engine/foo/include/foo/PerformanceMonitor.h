
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Updatable.h"

#include <memory>
#include <string>

namespace foo {

class PerformanceMonitorObserver {
 public:
    virtual void PoorPerformanceDetected(const double& aTargetFrameRate, const double& aAverageFrameRate) = 0;
    virtual void PerformanceRestored(const double& aTargetFrameRate, const double& aAverageFrameRate) = 0;

 protected:
    explicit PerformanceMonitorObserver() = default;
    ~PerformanceMonitorObserver() = default;
    FOO_NO_DEFAULTS(PerformanceMonitorObserver)
    FOO_NO_NEW_DELETE
};

class PerformanceMonitor : protected Updatable {
 public:
    static PerformanceMonitorPtr Create(CreationContextPtr& aContext);
    double GetAverageFrameRate() const;
    double GetPerfomranceDelta() const;
    void SetPerformanceDelta(const double aDelta);
    void Pause();
    void Resume();
    void Resample();
    void AddPerformanceMonitorObserver(PerformanceMonitorObserverPtr aObserver);
    void RemovePerformanceMonitorObserver(const PerformanceMonitorObserver& aObserver);

 protected:
    struct State;
    PerformanceMonitor(State& aState, CreationContextPtr& aContext);
    ~PerformanceMonitor() = default;

    // Updatable interface
    void UpdateResource(RenderContext& aContext) override;

 private:
    FOO_NO_DEFAULTS(PerformanceMonitor)
    State& m;
};

class PausePerformanceMonitor {
 private:
    PerformanceMonitor& mMonitor;
    FOO_NO_DEFAULTS(PausePerformanceMonitor)
    PausePerformanceMonitor() = delete;

 public:
    PausePerformanceMonitor(PerformanceMonitor& aMonitor) : mMonitor(aMonitor) { mMonitor.Pause(); }
    ~PausePerformanceMonitor() { mMonitor.Resume(); }
};

}  // namespace foo
