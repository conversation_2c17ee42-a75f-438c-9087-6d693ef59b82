
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Transform.h"
#include "foo/Updatable.h"

namespace foo {

enum class AnimationState { Play, Stop };

class AnimatedTransform : public Transform, protected Updatable {
 public:
    static AnimatedTransformPtr Create(CreationContextPtr& aContext);
    void SetAnimationState(const AnimationState aState);
    AnimatedTransform& ResetAnimations();
    AnimatedTransform& ClearAnimations();
    AnimatedTransform& AddStaticTransform(const YVRMath::mat4& aTransform);
    AnimatedTransform& AddRotationAnimation(const YVRMath::vec3& aAxis, const float aAngularVelocity);
    AnimatedTransform& AddTranslationAnimation(const YVRMath::vec3& aDirection, const float aSpeed);

    // Transform Interface
    void SetTransform(const YVRMath::mat4& aTransform) override;

 protected:
    struct State;
    AnimatedTransform(State& aState, CreationContextPtr& aContext);
    ~AnimatedTransform() = default;

    // Updatable Interface
    void UpdateResource(RenderContext& aContext) override;

 private:
    State& m;
    FOO_NO_DEFAULTS(AnimatedTransform);
};

}  // namespace foo
