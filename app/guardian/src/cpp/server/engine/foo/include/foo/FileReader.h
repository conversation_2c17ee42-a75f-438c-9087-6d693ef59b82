
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/gl.h"

#include <memory>
#include <string>

namespace foo {

class FileHandler {
 public:
    virtual void BindFileHandle(const std::string& aFileName, const int aFileHandle) = 0;
    virtual void LoadFailed(const int aFileHandle, const std::string& aReason) = 0;
    virtual void ProcessRawFileChunk(const int aFileHandle, const char* aBuffer, const size_t aSize) = 0;
    virtual void FinishRawFile(const int aFileHandle) = 0;
    virtual void ProcessImageFile(const int aFileHandle, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat) = 0;

 protected:
    FileHandler() {}
    virtual ~FileHandler() {}

 private:
    FOO_NO_DEFAULTS(FileHandler)
};

class FileReader {
 public:
    virtual void ReadRawFile(const std::string& aFileName, FileHandlerPtr aHandler) = 0;
    virtual void ReadImageFile(const std::string& aFileName, FileHandlerPtr aHandler) = 0;
    virtual void ReadResourceFile(const int aResId, FileHandlerPtr aHandler) = 0;

 protected:
    FileReader() {}
    virtual ~FileReader() {}

 private:
    FOO_NO_DEFAULTS(FileReader)
};

}  // namespace foo
