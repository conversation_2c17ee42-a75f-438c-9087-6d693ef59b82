
#pragma once

#include "foo/MacroUtils.h"

#include <pthread.h>

namespace foo {

class Mutex {
 public:
    Mutex() { pthread_mutex_init(&mMutex, nullptr); }
    ~Mutex() { pthread_mutex_destroy(&mMutex); }
    bool Lock() { return pthread_mutex_lock(&mMutex) == 0; }
    bool TryLock() { return pthread_mutex_trylock(&mMutex) == 0; }
    bool Unlock() { return pthread_mutex_unlock(&mMutex) == 0; }

 protected:
    pthread_mutex_t mMutex;

 private:
    FOO_NO_DEFAULTS(Mutex)
};

class MutexAutoLock {
 public:
    MutexAutoLock(Mutex& aMutex) : mMutex(aMutex) { mMutex.Lock(); }
    ~MutexAutoLock() { mMutex.Unlock(); }

 private:
    Mutex& mMutex;
    MutexAutoLock() = delete;
    FOO_NO_DEFAULTS(MutexAutoLock)
    FOO_NO_NEW_DELETE
};

class MutexAutoUnlock {
 public:
    MutexAutoUnlock(Mutex& aMutex) : mMutex(aMutex) { mMutex.Unlock(); }
    ~MutexAutoUnlock() { mMutex.Lock(); }

 private:
    Mutex& mMutex;
    MutexAutoUnlock() = delete;
    FOO_NO_DEFAULTS(MutexAutoUnlock)
    FOO_NO_NEW_DELETE
};

}  // namespace foo
