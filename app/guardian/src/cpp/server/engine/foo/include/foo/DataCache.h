

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class DataCache {
 public:
    static DataCachePtr Create();
    void SetCachePath(const std::string& aPath);
    uint32_t CacheData(std::unique_ptr<uint8_t[]>& aData, const size_t aDataSize);
    size_t LoadData(const uint32_t aHandle, std::unique_ptr<uint8_t[]>& aData);
    void RemoveData(const uint32_t aHandle);

 protected:
    struct State;
    DataCache(State& aState);
    ~DataCache();

 private:
    State& m;
    DataCache() = delete;
    FOO_NO_DEFAULTS(DataCache)
};

}  // namespace foo
