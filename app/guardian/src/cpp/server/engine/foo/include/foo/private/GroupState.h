
#pragma once

#include <vector>
#include "foo/Forward.h"
#include "foo/private/NodeState.h"

namespace foo {

class LambdaDrawable;
typedef std::shared_ptr<LambdaDrawable> LambdaDrawablePtr;

struct Group::State : public Node::State {
    std::vector<NodePtr> children;
    std::vector<LightPtr> lights;
    GroupWeak self;
    LambdaDrawablePtr preRenderLambda;
    LambdaDrawablePtr postRenderLambda;
    LambdaDrawablePtr createLambdaDrawable(CreationContextPtr& aContext, const RenderLambda& aLambda);
    bool Contains(const Node& aNode);
    bool Contains(const Light& aLight);
    virtual bool IsEnabled(const Node&) { return true; }
    virtual void Clear() { children.clear(); }
};

}  // namespace foo
