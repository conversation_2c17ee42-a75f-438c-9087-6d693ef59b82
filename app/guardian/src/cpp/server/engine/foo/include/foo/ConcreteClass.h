#pragma once

#include "foo/Forward.h"

// FOO_COUNT_OBJECTS enables leak detection.
// #define FOO_COUNT_OBJECTS
#include "foo/ObjectCounter.h"

namespace foo {

template <class Base, class State>
class ConcreteClass : private State, public Base {
 public:
    ConcreteClass() : Base(*(State*)this) { FOO_ADD_TO_OBJECT_COUNT; }
    ConcreteClass(CreationContextPtr& aContext) : Base(*(State*)this, aContext) { FOO_ADD_TO_OBJECT_COUNT; }
    ConcreteClass(RenderContextPtr& aContext) : Base(*(State*)this, aContext) { FOO_ADD_TO_OBJECT_COUNT; }
    ~ConcreteClass() { FOO_REMOVE_FROM_OBJECT_COUNT; }
};

template <class Base>
class ConcreteClassSimple : public Base {
 public:
    ConcreteClassSimple() : Base() { FOO_ADD_TO_OBJECT_COUNT; }
    explicit ConcreteClassSimple(CreationContextPtr& aContext) : Base(aContext) { FOO_ADD_TO_OBJECT_COUNT; }
    explicit ConcreteClassSimple(RenderContextPtr& aContext) : Base(aContext) { FOO_ADD_TO_OBJECT_COUNT; }
    ~ConcreteClassSimple() { FOO_REMOVE_FROM_OBJECT_COUNT; }
};

template <class Base, class Object>
class ConcreteClassAny : public Base {
 public:
    ConcreteClassAny() : Base() { FOO_ADD_TO_OBJECT_COUNT; }
    explicit ConcreteClassAny(Object& aObject) : Base(aObject) { FOO_ADD_TO_OBJECT_COUNT; }
    explicit ConcreteClassAny(CreationContextPtr& aContext, Object& aObject) : Base(aContext, aObject) { FOO_ADD_TO_OBJECT_COUNT; }
    explicit ConcreteClassAny(RenderContextPtr& aContext, Object& aObject) : Base(aContext, aObject) { FOO_ADD_TO_OBJECT_COUNT; }
    ~ConcreteClassAny() { FOO_REMOVE_FROM_OBJECT_COUNT; }
};

}  // namespace foo
