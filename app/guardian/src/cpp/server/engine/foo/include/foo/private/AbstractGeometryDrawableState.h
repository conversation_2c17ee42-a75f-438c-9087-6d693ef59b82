#pragma once

#include "foo/Logger.h"
#include "foo/gl.h"
#include "foo/GeometryDrawable.h"
#include "foo/RenderBuffer.h"
#include "foo/RenderState.h"
#include "foo/private/DrawableState.h"
#include "foo/private/NodeState.h"

namespace foo {

struct AbstractGeometryDrawable::State : public Node::State, public Drawable::State {
    std::shared_ptr<AbstractRenderState> renderState;
    RenderBufferPtr renderBuffer;

    uint32_t rangeStart = 0;
    uint32_t rangeLength = 0;

    bool UseTexture() const {
        if (!renderState || !renderBuffer) {
            return false;
        }
        if (renderState->UVLength() != renderBuffer->UVLength()) {
            // FOO_WARN("RenderState UVLength(%d) != RenderBuffer UVLength(%d)", renderState->UVLength(), renderBuffer->UVLength());
            return false;
        }
        return renderState->HasTexture() && (renderBuffer->UVLength() > 0);
    }

    bool UseColor() const {
        if (!renderBuffer) {
            return false;
        }
        return renderBuffer->ColorLength() > 0;
    }
};

}  // namespace foo
