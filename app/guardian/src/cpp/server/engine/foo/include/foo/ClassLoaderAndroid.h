

#pragma once

#include "foo/FileReader.h"
#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <jni.h>
#include <memory>

namespace foo {

class ClassLoaderAndroid {
 public:
    static ClassLoaderAndroidPtr Create();
    void Init(JNIEnv* aEnv, jobject& aActivity);
    void Shutdown();
    jclass FindClass(const std::string& aClassName) const;

 protected:
    struct State;
    ClassLoaderAndroid(State& aState);
    ~ClassLoaderAndroid();

 private:
    State& m;
    ClassLoaderAndroid() = delete;
    FOO_NO_DEFAULTS(ClassLoaderAndroid)
};

}  // namespace foo
