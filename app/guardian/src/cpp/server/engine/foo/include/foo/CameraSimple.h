

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include "foo/Camera.h"
namespace foo {

class CameraSimple : public Camera {
 public:
    static CameraSimplePtr Create(CreationContextPtr& aContext);
    // Camera interface
    const YVRMath::mat4& GetTransform() const override;
    const YVRMath::mat4& GetView() const override;
    const YVRMath::mat4& GetPerspective() const override;
    const YVRMath::mat4& GetEyeTransform() const override;
    void SetEyeTransform(const YVRMath::mat4& aTransform) override;

    // CameraSimple interface
    void SetTransform(const YVRMath::mat4& aTransform);
    float GetNearClip() const;
    float GetFarClip() const;
    void SetClipRange(const float aNear, const float aFar);
    float GetViewportWidth() const;
    float GetViewportHeight() const;
    void SetViewport(const int aWidth, const int aHeight);
    void SetFieldOfView(const float aHorizontal, const float aVertical);

 protected:
    struct State;
    CameraSimple(State& aState, CreationContextPtr& aContext);
    ~CameraSimple();

 private:
    State& m;
    CameraSimple() = delete;
    FOO_NO_DEFAULTS(CameraSimple)
};

}  // namespace foo
