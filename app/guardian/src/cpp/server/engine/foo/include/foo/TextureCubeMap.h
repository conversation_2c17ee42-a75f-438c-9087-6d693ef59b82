
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "foo/Texture.h"

#include <string>
#include "foo/gl.h"

namespace foo {

class TextureCubeMap : public Texture, protected ResourceGL {
 public:
    static TextureCubeMapPtr Create(CreationContextPtr& aContext, GLuint aExternalTexture = 0);

    static void Load(CreationContextPtr& aContext, const TextureCubeMapPtr& aTexture, const std::string& aFileXPos, const std::string& aFileXNeg, const std::string& aFileYPos,
                     const std::string& aFileYNeg, const std::string& aFileZPos, const std::string& aFileZNeg);
    void SetImageData(const GLenum aFaceTarget, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat);

 protected:
    struct State;
    TextureCubeMap(State& aState, CreationContextPtr& aContext);
    ~TextureCubeMap();

    // Texture interface
    void AboutToBind() override;

    // ResourceGL interface
    bool SupportOffRenderThreadInitialization() override;
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    TextureCubeMap() = delete;
    FOO_NO_DEFAULTS(TextureCubeMap)
};

}  // namespace foo
