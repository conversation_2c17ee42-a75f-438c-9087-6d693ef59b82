
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "foo/gl.h"
#include "foo/AbstractGeometry.h"
#include "foo/GeometryDrawable.h"

#include <vector>

namespace foo {

class Geometry : public AbstractGeometry, public GeometryDrawable {
 public:
    static GeometryPtr Create(CreationContextPtr& aContext);

    void UpdateBuffers() override;
    //  struct Face {
    //      std::vector<GLushort> vertices;
    //      std::vector<GLushort> uvs;
    //      std::vector<GLushort> normals;
    //  };

    // Geometry interface
    //  VertexArrayPtr GetVertexArray() const override;
    //  void SetVertexArray(const VertexArrayPtr& aVertexArray) override;
    //  void UpdateBuffers() override;

    //  void AddFace(const std::vector<int>& aVerticies, const std::vector<int>& aUVs, const std::vector<int>& aNormals) override;

    //  int32_t GetFaceCount() const override;
    //  const Face& GetFace(int32_t aIndex) const override;

 protected:
    struct State;
    Geometry(State& aState, CreationContextPtr& aContext);
    ~Geometry();

    // From ResourceGL
    bool SupportOffRenderThreadInitialization() override;
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    Geometry() = delete;

    FOO_NO_DEFAULTS(Geometry)
};

}  // namespace foo
