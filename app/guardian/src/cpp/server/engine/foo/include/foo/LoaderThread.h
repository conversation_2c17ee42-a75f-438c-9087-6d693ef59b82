
#pragma once

#include <functional>
#include <string>
#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

typedef std::function<void(GroupPtr&)> LoadFinishedCallback;
typedef std::function<GroupPtr(CreationContextPtr&)> LoadTask;

class LoaderThread {
 public:
    virtual void RunLoadTask(GroupPtr aTargetNode, LoadTask& aTask) = 0;
    virtual void RunLoadTask(GroupPtr aTargetNode, LoadTask& aTask, LoadFinishedCallback& aCallback) = 0;
    virtual void AddFinishedCallback(LoadFinishedCallback& aCallback) = 0;
    virtual bool IsOnLoaderThread() const = 0;

 protected:
    LoaderThread() = default;
    ~LoaderThread() = default;

 private:
    FOO_NO_DEFAULTS(LoaderThread);
};

}  // namespace foo
