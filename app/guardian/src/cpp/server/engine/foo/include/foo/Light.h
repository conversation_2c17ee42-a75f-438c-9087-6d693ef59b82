

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class Light {
 public:
    static LightPtr Create(CreationContextPtr& aContext);

    const YVRMath::vec3& GetDirection() const;
    const Color& GetAmbientColor() const;
    const Color& GetDiffuseColor() const;
    const Color& GetSpecularColor() const;

    void SetDirection(const YVRMath::vec3& aDirection);
    void SetAmbientColor(const Color& aColor);
    void SetDiffuseColor(const Color& aColor);
    void SetSpecularColor(const Color& aColor);

 protected:
    struct State;
    Light(State& aState, CreationContextPtr& aContext);
    ~Light();

 private:
    State& m;
    Light() = delete;
    FOO_NO_DEFAULTS(Light)
};

}  // namespace foo
