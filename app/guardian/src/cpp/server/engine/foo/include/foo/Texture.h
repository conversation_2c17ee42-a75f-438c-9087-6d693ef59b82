#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <string>
#include "foo/gl.h"

namespace foo {

class Texture {
 public:
    void Bind();
    void Unbind();
    std::string GetName() const;
    GLenum GetTarget() const;
    void SetName(const std::string& aName);
    void SetTextureParameter(GLenum aName, GLint aParam);
    GLuint GetHandle() const;

 protected:
    struct State;
    Texture(State& aState, CreationContextPtr& aContext);
    virtual ~Texture();

    virtual void AboutToBind() {}

 private:
    State& m;
    Texture() = delete;
    FOO_NO_DEFAULTS(Texture)
};

}  // namespace foo
