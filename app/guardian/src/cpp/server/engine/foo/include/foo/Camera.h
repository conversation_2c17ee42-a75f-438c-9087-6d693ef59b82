

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class Camera {
 public:
    virtual const YVRMath::mat4& GetTransform() const = 0;
    virtual const YVRMath::mat4& GetView() const = 0;
    virtual const YVRMath::mat4& GetPerspective() const = 0;
    virtual const YVRMath::mat4& GetEyeTransform() const = 0;
    virtual void SetEyeTransform(const YVRMath::mat4& aTransform) = 0;

 protected:
    Camera() {}
    virtual ~Camera() {}

 private:
    FOO_NO_DEFAULTS(Camera)
};

}  // namespace foo
