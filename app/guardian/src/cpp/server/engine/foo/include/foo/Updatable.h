
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class Updatable {
 public:
    virtual void UpdateResource(RenderContext& aContext) = 0;

 protected:
    struct State;
    Updatable(State& aState, CreationContextPtr& aContext);
    Updatable(State& aState);
    virtual ~Updatable();

 private:
    State& m;
    Updatable() = delete;
    FOO_NO_DEFAULTS(Updatable)
};

}  // namespace foo
