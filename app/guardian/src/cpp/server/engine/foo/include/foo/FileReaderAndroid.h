

#pragma once

#include "foo/FileReader.h"
#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <jni.h>
#include <memory>
#include "foo/gl.h"

namespace foo {

class FileReaderAndroid : public FileReader {
 public:
    static FileReaderAndroidPtr Create();
    void ReadRawFile(const std::string& aFileName, FileHandlerPtr aHandler) override;
    void ReadImageFile(const std::string& aFileName, FileHandlerPtr aHandler) override;
    void ReadResourceFile(const int aResId, FileHandlerPtr aHandler);
    void LoadAppIconInMainDisplayExcepHome(const std::string appPackage, FileHandlerPtr aHandler);
    void Init(JNIEnv* aEnv, jobject& aAssetManager, jobject& aResource, const ClassLoaderAndroidPtr& classLoader);
    void Shutdown();
    void ProcessImageFile(const int aFileHandle, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat);
    void ImageFileLoadFailed(const int aFileHandle, const std::string& aReason);

 protected:
    struct State;
    FileReaderAndroid(State& aState);
    ~FileReaderAndroid();

 private:
    State& m;
    FileReaderAndroid() = delete;
    FOO_NO_DEFAULTS(FileReaderAndroid)
};

}  // namespace foo
