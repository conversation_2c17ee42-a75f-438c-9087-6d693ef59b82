#pragma once

#include <cstddef>

#if defined(FOO_COUNT_OBJECTS)
#define FOO_ADD_TO_OBJECT_COUNT AddObject(typeid(*this).hash_code(), typeid(*this).name())
#define FOO_REMOVE_FROM_OBJECT_COUNT RemoveObject(typeid(*this).hash_code())
#else
#define FOO_ADD_TO_OBJECT_COUNT
#define FOO_REMOVE_FROM_OBJECT_COUNT
#endif

namespace foo {

void InitializeObjectCounter();
void LogObjectCount();
void ShutdownObjectCounter();
void AddObject(std::size_t aHandle, const char* aName);
void RemoveObject(std::size_t aHandle);

}  // namespace foo
