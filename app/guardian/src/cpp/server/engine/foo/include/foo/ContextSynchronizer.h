

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class ContextSynchronizerObserver {
 public:
    virtual void ContextsSynchronized(RenderContextPtr& aRenderContext) = 0;
};

class ContextSynchronizer {
 public:
    static ContextSynchronizerPtr Create(RenderContextPtr& aContext);
    void BindToThread();
    void RegisterObserver(ContextSynchronizerObserverPtr& aObserver);
    void ReleaseObserver(ContextSynchronizerObserverPtr& aObserver);
    void AdoptLists(ResourceGLList& aUninitializedResources, ResourceGLList& aResources, UpdatableList& aUpdatables);
    void Signal(bool& aIsActive);
    void Release();

 protected:
    struct State;
    ContextSynchronizer(State& aState);
    ~ContextSynchronizer();

 private:
    State& m;
    ContextSynchronizer() = delete;
    FOO_NO_DEFAULTS(ContextSynchronizer)
};

}  // namespace foo
