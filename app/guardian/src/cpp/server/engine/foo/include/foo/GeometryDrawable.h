
#pragma once

#include "foo/Drawable.h"
#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Node.h"
#include "foo/AbstractGeometryDrawable.h"

namespace foo {

const class GeometryDrawable : public AbstractGeometryDrawable {
 public:
    static GeometryDrawablePtr Create(CreationContextPtr& aContext);
    void Draw(const Camera& aCamera, const YVRMath::mat4& aModelTransform) override;

 protected:
    struct State;
    GeometryDrawable(State& aState, CreationContextPtr& aContext);
    ~GeometryDrawable() = default;

 private:
    State& m;
    GeometryDrawable() = delete;
    FOO_NO_DEFAULTS(GeometryDrawable)
};

}  // namespace foo
