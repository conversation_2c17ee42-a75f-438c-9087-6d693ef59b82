
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/gl.h"

#include <string>

namespace foo {

class Program {
 public:
    static ProgramPtr Create();
    bool Enable();
    void SetFeatures(const uint32_t aFeatures);
    bool SupportsFeatures(const uint32_t aFeatures);
    void SetProgram(GLuint aProgram);
    GLuint GetProgram() const;
    GLint GetAttributeLocation(const char* aName);
    GLint GetAttributeLocation(const std::string& aName) { return GetAttributeLocation(aName.c_str()); }
    GLint GetUniformLocation(const char* aName);
    GLint GetUniformLocation(const std::string& aName) { return GetUniformLocation(aName.c_str()); }

 protected:
    struct State;
    Program(State& aState);
    ~Program() = default;

 private:
    State& m;
    Program() = delete;
    FOO_NO_DEFAULTS(Program)
};

}  // namespace foo
