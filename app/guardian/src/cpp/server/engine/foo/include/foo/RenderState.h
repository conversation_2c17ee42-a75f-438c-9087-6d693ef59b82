
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "yvrmath/YVRMath.h"
#include "foo/gl.h"
#include "foo/AbstractRenderState.h"

namespace foo {

class RenderState : public AbstractRenderState {
 public:
    static RenderStatePtr Create(CreationContextPtr& aContext);
    void SetProgram(ProgramPtr& aProgram) override;
    GLint AttributePosition() const override;
    GLint AttributeNormal() const override;
    GLint AttributeUV() const override;
    GLint AttributeColor() const override;
    uint32_t GetLightId() const override;
    void ResetLights(const uint32_t aId) override;
    void AddLight(const YVRMath::vec3& aDirection, const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular) override;
    void SetMaterial(const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular, const float aSpecularExponent) override;
    void SetAmbient(const Color& aColor) override;
    void SetDiffuse(const Color& aColor) override;
    void GetMaterial(Color& aAmbient, Color& aDiffuse, Color& aSpecular, float& aSpecularExponent) const override;
    GLint UVLength() const override;
    TexturePtr GetTexture() const override;
    void SetTexture(const TexturePtr& aTexture) override;
    bool HasTexture() const override;
    const Color& GetTintColor() const override;
    void SetTintColor(const Color& aColor) override;
    bool Enable(const YVRMath::mat4& aPerspective, const YVRMath::mat4& aView, const YVRMath::mat4& aModel) override;
    void Disable() override;
    void SetLightsEnabled(bool aEnabled) override;
    void SetUVTransform(const YVRMath::mat4& aMatrix) override;

 protected:
    struct State;
    RenderState(State& aState, CreationContextPtr& aContext);
    ~RenderState() = default;

    // ResourceGL interface
    void InitializeGL() override;
    void ShutdownGL() override;

 private:
    State& m;
    RenderState() = delete;
    FOO_NO_DEFAULTS(RenderState)
};

}  // namespace foo
