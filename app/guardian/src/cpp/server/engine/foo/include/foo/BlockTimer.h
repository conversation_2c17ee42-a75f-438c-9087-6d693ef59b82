#pragma once

#include <functional>
#include "foo/MacroUtils.h"

namespace foo {

typedef std::function<void()> BlockTimerCallback;

#define FOO_BLOCK_TIMER(limit, cb) BlockTimer bt(__FILE__, __FUNCTION__, __LINE__, limit, cb)

#define FOO_BLOCK_TIMER_DEFAULT_TIME 0.025

#define FOO_BLOCK_TIMER_DEFAULT FOO_BLOCK_TIMER(FOO_BLOCK_TIMER_DEFAULT_TIME, nullptr)

#define FOO_BLOCK_TIMER_LIMIT(limit) FOO_BLOCK_TIMER(limit, nullptr)

#define FOO_BLOCK_TIMER_CB(cb) BlockTimer bt(__FILE__, __FUNCTION__, __LINE__, FOO_BLOCK_TIMER_DEFAULT_TIME, cb)

class BlockTimer {
 public:
    BlockTimer(const char* aFileName, const char* aFunctionName, const int aLineNumber, const double aMaxTime, BlockTimerCallback aCallback);
    ~BlockTimer();

 private:
    const char* mFileName;
    const char* mFunctionName;
    const int mLineNumber;
    const double mMaxTime;
    double mStartTime;
    BlockTimerCallback mCallback;
    BlockTimer() = delete;
    FOO_NO_DEFAULTS(BlockTimer)
    FOO_NO_NEW_DELETE
};

}  // namespace foo
