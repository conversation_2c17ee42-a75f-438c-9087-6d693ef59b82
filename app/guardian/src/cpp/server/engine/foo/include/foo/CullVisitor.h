
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class CullVisitor {
 public:
    static CullVisitorPtr Create(CreationContextPtr& aContext);
    const YVRMath::mat4& GetTransform() const;
    void PushTransform(const YVRMath::mat4& aTransform);
    void PopTransform();

 protected:
    struct State;
    CullVisitor(State& aState, CreationContextPtr& aContext);
    ~CullVisitor();

 private:
    State& m;
    CullVisitor() = delete;
    FOO_NO_DEFAULTS(CullVisitor)
};

}  // namespace foo
