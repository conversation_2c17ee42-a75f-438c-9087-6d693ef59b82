
#pragma once

#include "foo/CameraEye.h"
namespace foo {

struct CameraEye::State {
    bool dirty;
    YVRMath::mat4 headTransform;
    YVRMath::mat4 eyeTransform;
    YVRMath::mat4 perspective;
    YVRMath::mat4 eyeMatrix;

    // calculated from headTransform * eyeTransform
    YVRMath::mat4 transform;
    // calculated from (headTransform * eyeTransform).Inverse()
    YVRMath::mat4 view;

    State();
    void Update();
};

}  // namespace foo
