

#pragma once
#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class DrawableList {
 public:
    static DrawableListPtr Create(CreationContextPtr& aContext);

    void Reset();
    void PushLight(const Light& aLight);
    void PopLights(const int aCount);
    void AddDrawable(DrawablePtr&& aDrawable, const YVRMath::mat4& aTransform);
    void Draw(const Camera& aCamera);

 protected:
    struct State;
    DrawableList(State& aState, CreationContextPtr& aContext);
    ~DrawableList();

 private:
    State& m;
    DrawableList() = delete;
    FOO_NO_DEFAULTS(DrawableList)
};

}  // namespace foo
