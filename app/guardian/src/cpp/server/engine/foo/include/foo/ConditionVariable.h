
#pragma once

#include "foo/MacroUtils.h"
#include "foo/Mutex.h"

#include <pthread.h>

namespace foo {

class ConditionVariable : public Mutex {
 public:
    ConditionVariable() { pthread_cond_init(&mCond, nullptr); }
    ~ConditionVariable() { pthread_cond_destroy(&mCond); }
    bool Wait() { return pthread_cond_wait(&mCond, &mMutex) == 0; }
    bool Signal() { return pthread_cond_signal(&mCond) == 0; }

 protected:
    pthread_cond_t mCond;

 private:
    FOO_NO_DEFAULTS(ConditionVariable);
};

}  // namespace foo
