#pragma once

#include "foo/Logger.h"

#include "jni.h"

namespace foo {

inline void CheckJNIException(JNIEnv* aEnv, const char* aName, const char* aFile, const int aLine) {
    if (!aEnv) {
        return;
    }
    if (aEnv->ExceptionCheck() == JNI_TRUE) {
        aEnv->ExceptionDescribe();
        aEnv->ExceptionClear();
        FOO_ERROR("Java exception encountered when calling %s in %s:%d", aName, aFile, aLine);
    }
}

}  // namespace foo

#define FOO_CHECK_JNI_EXCEPTION(env) foo::CheckJNIException(env, __FUNCTION__, __FILE__, __LINE__)
