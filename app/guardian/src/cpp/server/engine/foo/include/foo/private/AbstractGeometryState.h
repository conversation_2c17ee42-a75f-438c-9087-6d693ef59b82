#pragma once

#include "foo/AbstractGeometry.h"
#include "foo/private/AbstractGeometryDrawableState.h"
#include "foo/private/ResourceGLState.h"

namespace foo {
struct AbstractGeometry::State : public ResourceGL::State {
    VertexArrayPtr vertexArray;
    std::vector<Face> faces;
    GLsizei vertexCount = 0;
    GLsizei triangleCount = 0;

    State() = default;
    ~State() = default;
};
}  // namespace foo