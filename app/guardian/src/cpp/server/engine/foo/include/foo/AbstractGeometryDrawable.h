
#pragma once

#include "foo/Drawable.h"
#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Node.h"

namespace foo {

class AbstractGeometryDrawable : public Node, public Drawable {
 public:
    // Node interface
    void Cull(CullVisitor& aVisitor, DrawableList& aDrawables) override;
    // From Drawable
    AbstractRenderStatePtr& GetRenderState();
    void SetRenderState(AbstractRenderStatePtr aRenderState);
    // AbstractGeometryDrawable interface
    RenderBufferPtr& GetRenderBuffer();
    void SetRenderBuffer(RenderBufferPtr& aRenderBuffer);
    void SetRenderRange(uint32_t aStartIndex, uint32_t aLength);
    virtual void Draw(const Camera& aCamera, const YVRMath::mat4& aModelTransform) = 0;

 protected:
    struct State;
    AbstractGeometryDrawable(State& aState, CreationContextPtr& aContext);
    ~AbstractGeometryDrawable() = default;

 private:
    State& m;
    AbstractGeometryDrawable() = delete;
    FOO_NO_DEFAULTS(AbstractGeometryDrawable)
};

}  // namespace foo
