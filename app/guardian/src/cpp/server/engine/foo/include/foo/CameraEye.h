

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"
#include "foo/Camera.h"
namespace foo {

class CameraEye : public Camera {
 public:
    static CameraEyePtr Create(CreationContextPtr& aContext);
    // Camera interface
    const YVRMath::mat4& GetTransform() const override;
    const YVRMath::mat4& GetView() const override;
    const YVRMath::mat4& GetPerspective() const override;
    const YVRMath::mat4& GetEyeTransform() const override;
    void SetEyeTransform(const YVRMath::mat4& aTransform) override;

    // CameraEye interface
    void SetPerspective(const YVRMath::mat4& aPerspective);
    const YVRMath::mat4& GetHeadTransform() const;
    void SetHeadTransform(const YVRMath::mat4& aTransform);
    void SetEyeMatrix(const YVRMath::mat4& aMatrix);

 protected:
    struct State;
    CameraEye(State& aState, CreationContextPtr& aContext);
    virtual ~CameraEye();

 private:
    State& m;
    CameraEye() = delete;
    FOO_NO_DEFAULTS(CameraEye)
};

}  // namespace foo
