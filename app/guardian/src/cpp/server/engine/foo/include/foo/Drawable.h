
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Node.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class Drawable : public std::enable_shared_from_this<Drawable> {
 public:
    DrawablePtr CreateDrawablePtr();
    virtual AbstractRenderStatePtr& GetRenderState() = 0;
    virtual void SetRenderState(AbstractRenderStatePtr aRenderState) = 0;
    virtual void Draw(const Camera& aCamera, const YVRMath::mat4& aModelTransform) = 0;

 protected:
    struct State;
    Drawable(State& aState, CreationContextPtr& aContext);
    ~Drawable();

 private:
    State& m;
    Drawable() = delete;
    FOO_NO_DEFAULTS(Drawable)
};

}  // namespace foo
