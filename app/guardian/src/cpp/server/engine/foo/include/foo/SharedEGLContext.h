
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

namespace foo {

class SharedEGLContext {
 public:
    static SharedEGLContextPtr Create();
    bool Initialize();
    bool MakeCurrent();

 protected:
    struct State;
    SharedEGLContext(State& aState);
    ~SharedEGLContext();

 private:
    State& m;
    SharedEGLContext() = delete;
    FOO_NO_DEFAULTS(SharedEGLContext)
};

}  // namespace foo
