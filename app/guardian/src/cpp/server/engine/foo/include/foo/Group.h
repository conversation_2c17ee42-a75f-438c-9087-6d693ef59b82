

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/Node.h"

namespace foo {

class Group : public Node {
 public:
    static GroupPtr Create(CreationContextPtr& aContext);

    // Node interface
    void Cull(CullVisitor& aVisitor, DrawableList& aDrawables) override;

    // Group interface
    void AddLight(LightPtr aLight);
    void RemoveLight(const Light& aLight);
    void AddNode(NodePtr aNode);
    virtual void RemoveNode(Node& aNode);
    void InsertNode(NodePtr aNode, uint32_t aIndex);
    const NodePtr& GetNode(uint32_t aIndex) const;
    int32_t GetNodeCount() const;
    void SortNodes(const std::function<bool(const foo::NodePtr&, const foo::NodePtr&)>& aFunction);
    void TakeChildren(GroupPtr& aGroup);
    void SetPreRenderLambda(CreationContextPtr& aContext, const RenderLambda& aLambda);
    void SetPostRenderLambda(CreationContextPtr& aContext, const RenderLambda& aLambda);

 protected:
    bool Traverse(const GroupPtr& aParent, const Node::TraverseFunction& aTraverseFunction) override;
    struct State;
    Group(State& aState, CreationContextPtr& aContext);
    ~Group();

 private:
    State& m;
    Group() = delete;
    FOO_NO_DEFAULTS(Group)
};

}  // namespace foo
