
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"
#include "yvrmath/YVRMath.h"
#include "foo/gl.h"

namespace foo {

class AbstractRenderState : protected ResourceGL {
 public:
    virtual void SetProgram(ProgramPtr& aProgram) = 0;
    virtual GLint AttributePosition() const = 0;
    virtual GLint AttributeNormal() const = 0;
    virtual GLint AttributeUV() const = 0;
    virtual GLint AttributeColor() const = 0;
    virtual uint32_t GetLightId() const = 0;
    virtual void ResetLights(const uint32_t aId) = 0;
    virtual void AddLight(const YVRMath::vec3& aDirection, const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular) = 0;
    virtual void SetMaterial(const Color& aAmbient, const Color& aDiffuse, const Color& aSpecular, const float aSpecularExponent) = 0;
    virtual void SetAmbient(const Color& aColor) = 0;
    virtual void SetDiffuse(const Color& aColor) = 0;
    virtual void GetMaterial(Color& aAmbient, Color& aDiffuse, Color& aSpecular, float& aSpecularExponent) const = 0;
    virtual GLint UVLength() const = 0;
    virtual TexturePtr GetTexture() const = 0;
    virtual void SetTexture(const TexturePtr& aTexture) = 0;
    virtual bool HasTexture() const = 0;
    virtual const Color& GetTintColor() const = 0;
    virtual void SetTintColor(const Color& aColor) = 0;
    virtual bool Enable(const YVRMath::mat4& aPerspective, const YVRMath::mat4& aView, const YVRMath::mat4& aModel) = 0;
    virtual void Disable() = 0;
    virtual void SetLightsEnabled(bool aEnabled) = 0;
    virtual void SetUVTransform(const YVRMath::mat4& aMatrix) = 0;

 protected:
    struct State;
    AbstractRenderState(State& aState, CreationContextPtr& aContext);
    ~AbstractRenderState() = default;

    // // ResourceGL interface
    virtual void InitializeGL() = 0;
    virtual void ShutdownGL() = 0;

 private:
    State& m;
    AbstractRenderState() = delete;
    FOO_NO_DEFAULTS(AbstractRenderState)
};

}  // namespace foo
