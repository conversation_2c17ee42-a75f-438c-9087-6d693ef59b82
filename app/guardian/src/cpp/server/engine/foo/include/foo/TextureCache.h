#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <string>

namespace foo {

class TextureCache {
 public:
    static TextureCachePtr Create();
    void Init(CreationContextPtr& aContext);
    void Shutdown();
    TextureGLPtr FindTexture(const std::string& aTextureName);
    void AddTexture(const std::string& aTextureName, TextureGLPtr& aTexture);
    TextureGLPtr GetDefaultTexture();

 protected:
    struct State;
    TextureCache(State& aState);
    ~TextureCache();

 private:
    State& m;
    TextureCache() = delete;
    FOO_NO_DEFAULTS(TextureCache)
};

}  // namespace foo
