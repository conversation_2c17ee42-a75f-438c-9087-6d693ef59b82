
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include <jni.h>

namespace foo {

class RunnableQueue {
 public:
    static RunnableQueuePtr Create(JavaVM* aVM);

    bool AttachToThread();
    void Clear();
    void AddRunnable(JNIEnv* aEnv, jobject aRunnable);
    void ProcessRunnables();
    bool IsEmpty();

 protected:
    struct State;
    RunnableQueue(State& aState);
    ~RunnableQueue() = default;

 private:
    State& m;
    RunnableQueue() = delete;
    FOO_NO_DEFAULTS(RunnableQueue)
};

}  // namespace foo
