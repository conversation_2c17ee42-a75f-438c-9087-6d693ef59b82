

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"

#include "foo/gl.h"

namespace foo {

class RenderBuffer {
 public:
    static RenderBufferPtr Create(CreationContextPtr& aContext);
    void SetVertexObject(GLuint aObject, GLsizei aCount);
    GLuint GetVertexObject() const;
    void SetIndexObject(GLuint aObject, const GLsizei aCount);
    GLuint GetIndexObject() const;
    GLsizei VertexCount() const;
    GLsizei VertexSize() const;
    GLsizei IndexCount() const;
    void DefinePosition(const size_t aOffset, const GLsizei aLength = 3);
    size_t PositionOffset() const;
    GLsizei PositionSize() const;
    GLsizei PositionLength() const;
    void DefineNormal(const size_t aOffset, const GLsizei aLength = 3);
    size_t NormalOffset() const;
    GLsizei NormalSize() const;
    GLsizei NormalLength() const;
    void DefineUV(const size_t aOffset, const GLsizei aLength = 2);
    size_t UVOffset() const;
    GLsizei UVSize() const;
    GLsizei UVLength() const;
    void DefineColor(const size_t aOffset, const GLsizei aLength = 4);
    size_t ColorOffset() const;
    GLsizei ColorSize() const;
    GLsizei ColorLength() const;
    void Bind();
    void Unbind();

 protected:
    struct State;
    RenderBuffer(State& aState, CreationContextPtr& aContext);
    ~RenderBuffer() = default;

 private:
    State& m;
    RenderBuffer() = delete;
    FOO_NO_DEFAULTS(RenderBuffer)
};

}  // namespace foo
