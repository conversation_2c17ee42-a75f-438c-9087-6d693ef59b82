

#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "foo/ParserObj.h"
#include "yvrmath/YVRMath.h"
#include <string>

namespace foo {

class NodeFactoryObj : public ParserObserverObj {
 public:
    static NodeFactoryObjPtr Create(CreationContextPtr& aContext);

    // ParserObserverObj interface
    void StartModel(const std::string& aFileName) override;
    void FinishModel() override;
    void LoadMaterialLibrary(const std::string& aFile) override;
    void SetGroupNames(const std::vector<std::string>& aNames) override;
    void SetObjectName(const std::string& aName) override;
    void SetMaterialName(const std::string& aName) override;
    void AddVertex(const YVRMath::vec3& aPoint, const float aW) override;
    void AddNormal(const YVRMath::vec3& aNormal) override;
    void AddUV(const float aU, const float aV, const float aW) override;
    void SetSmoothingGroup(const int aGroup) override;
    void AddFace(const std::vector<int>& aVerticies, const std::vector<int>& aUVs, const std::vector<int>& aNormals) override;

    void StartMaterialFile(const std::string& aFileName) override;
    void FinishMaterialFile() override;
    void CreateMaterial(const std::string& aName) override;
    void SetAmbientColor(const YVRMath::vec3& aColor) override;
    void SetDiffuseColor(const YVRMath::vec3& aColor) override;
    void SetSpecularColor(const YVRMath::vec3& aColor) override;
    void SetSpecularExponent(const float aValue) override;
    void SetIlluniationModel(const int aValue) override;
    void SetAmbientTexture(const std::string& aFileName) override;
    void SetDiffuseTexture(const std::string& aFileName) override;
    void SetSpecularTexture(const std::string& aFileName) override;

    // NodeFactoryObj interface
    void SetModelRoot(GroupPtr aGroup);
    GroupPtr& GetModelRoot();

 protected:
    struct State;
    NodeFactoryObj(State& aState, CreationContextPtr& aContext);
    ~NodeFactoryObj();

 private:
    State& m;
    NodeFactoryObj() = delete;
    FOO_NO_DEFAULTS(NodeFactoryObj);
};

}  // namespace foo
