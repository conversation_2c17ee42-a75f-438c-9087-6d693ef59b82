
#pragma once

#include "foo/Forward.h"
#include "foo/MacroUtils.h"
#include "yvrmath/YVRMath.h"

namespace foo {

class VertexArray {
 public:
    static VertexArrayPtr Create(CreationContextPtr& aContext);

    int GetVertexCount() const;
    int GetNormalCount() const;
    int GetUVCount() const;
    int GetColorCount() const;

    void SetNormalCount(const int aCount);

    int GetUVLength() const;
    void SetUVLength(const int aLength);

    const YVRMath::vec3& GetVertex(const int aIndex) const;
    const YVRMath::vec3& GetNormal(const int aIndex) const;
    const YVRMath::vec3& GetUV(const int aIndex) const;
    const Color& GetColor(const int aIndex) const;

    void SetVertex(const int aIndex, const YVRMath::vec3& aPoint);
    void SetNormal(const int aIndex, const YVRMath::vec3& aNormal);
    void SetUV(const int aIndex, const YVRMath::vec3& aUV);
    void SetColor(const int aIndex, const Color& aColor);

    int AppendVertex(const YVRMath::vec3& aPoint);
    int AppendNormal(const YVRMath::vec3& aNormal);
    int AppendUV(const YVRMath::vec3& aUV);
    int AppendColor(const Color& aUV);

    void AddNormal(const int aIndex, const YVRMath::vec3& aNormal);

 protected:
    struct State;
    VertexArray(State& aState, CreationContextPtr& aContext);
    ~VertexArray() = default;

 private:
    State& m;
    VertexArray() = delete;
    FOO_NO_DEFAULTS(VertexArray);
};

}  // namespace foo
