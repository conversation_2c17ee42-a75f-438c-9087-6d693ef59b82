
#pragma once

#include <jni.h>
#include <functional>
#include <string>
#include "foo/Forward.h"
#include "foo/LoaderThread.h"
#include "foo/MacroUtils.h"
#include "foo/ResourceGL.h"

namespace foo {

class ModelLoaderAndroid : public LoaderThread {
 public:
    static ModelLoaderAndroidPtr Create(RenderContextPtr& aContext);
    void InitializeJava(JNIEnv* eEnv, jobject aActivity, jobject aAssets, jobject aResource);
    void ShutdownJava();
    void InitializeGL();
    void ShutdownGL();
    void LoadModel(const std::string& aModelName, GroupPtr aTargetNode);
    void LoadModel(const std::string& aModelName, GroupPtr aTargetNode, LoadFinishedCallback& aCallback);
    // LoaderThread Interface
    void RunLoadTask(GroupPtr aTargetNode, LoadTask& aTask) override;
    void RunLoadTask(GroupPtr aTargetNode, LoadTask& aTask, LoadFinishedCallback& aCallback) override;
    void AddFinishedCallback(LoadFinishedCallback& aCallback) override;
    bool IsOnLoaderThread() const override;

 protected:
    struct State;
    ModelLoaderAndroid(State& aState, RenderContextPtr& aContext);
    ~ModelLoaderAndroid();

 private:
    State& m;
    static void* Run(void* data);
    ModelLoaderAndroid() = delete;
    FOO_NO_DEFAULTS(ModelLoaderAndroid);
};

}  // namespace foo
