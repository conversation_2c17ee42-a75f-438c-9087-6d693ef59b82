
#include "foo/VertexArray.h"

#include "foo/Color.h"
#include "foo/ConcreteClass.h"
#include "foo/Logger.h"

#include <vector>

namespace foo {

const int DEFAULT_UV_LENGTH = 2;

struct VertexArray::State {
    struct NormalState {
        YVRMath::vec3 normal;
        float count;
        NormalState() : count(0.0f) {}
        explicit NormalState(const YVRMath::vec3& aNormal) : normal(aNormal), count(1.0f) {}
    };
    int uvLength = 0;
    std::vector<YVRMath::vec3> vertices;
    std::vector<NormalState> normals;
    std::vector<YVRMath::vec3> uvs;
    std::vector<Color> colors;
};

VertexArrayPtr VertexArray::Create(CreationContextPtr& aContext) { return std::make_shared<ConcreteClass<VertexArray, VertexArray::State> >(aContext); }

int VertexArray::GetVertexCount() const { return m.vertices.size(); }

int VertexArray::GetNormalCount() const { return m.normals.size(); }

int VertexArray::GetUVCount() const { return m.uvs.size(); }

int VertexArray::GetColorCount() const { return m.colors.size(); }

void VertexArray::SetNormalCount(const int aCount) {
    if (m.normals.size() < aCount) {
        m.normals.resize(aCount);
    }
}

int VertexArray::GetUVLength() const {
    if ((GetUVCount() > 0) && (m.uvLength == 0)) {
        FOO_WARN("Normal size is not set when normals defined. Defaulting to %d.", DEFAULT_UV_LENGTH);
        return DEFAULT_UV_LENGTH;
    }
    return m.uvLength;
}

void VertexArray::SetUVLength(const int aLength) {
    int length = aLength;
    if (length > 3) {
        length = 3;
        FOO_ERROR("UV Length can not be larger than 3. Size requested: %d", aLength);
    } else if (length < 2) {
        length = 2;
        FOO_ERROR("UV length can not be smaller than 2. Size requested: %d", aLength);
    }
    m.uvLength = length;
}

const YVRMath::vec3& VertexArray::GetVertex(const int aIndex) const {
    if (aIndex >= m.vertices.size()) {
        static YVRMath::vec3 v = YVRMath::vec3(0.0f);
        return v;
    }
    return m.vertices[aIndex];
}

const YVRMath::vec3& VertexArray::GetNormal(const int aIndex) const {
    if (aIndex >= m.normals.size()) {
        static YVRMath::vec3 v = YVRMath::vec3(0.0f);
        return v;
    }
    return m.normals[aIndex].normal;
}

const YVRMath::vec3& VertexArray::GetUV(const int aIndex) const {
    if (aIndex >= m.uvs.size()) {
        static YVRMath::vec3 v = YVRMath::vec3(0.0f);
        return v;
    }
    return m.uvs[aIndex];
}

const Color& VertexArray::GetColor(const int aIndex) const {
    if (aIndex >= m.colors.size()) {
        static Color c = Color();
        return c;
    }
    return m.colors[aIndex];
}

void VertexArray::SetVertex(const int aIndex, const YVRMath::vec3& aPoint) {
    if (m.vertices.size() < (aIndex + 1)) {
        m.vertices.resize(aIndex + 1);
    }
    m.vertices[aIndex] = aPoint;
}

void VertexArray::SetNormal(const int aIndex, const YVRMath::vec3& aNormal) {
    if (m.normals.size() < (aIndex + 1)) {
        m.normals.resize(aIndex + 1);
    }
    m.normals[aIndex].normal = aNormal;
}

void VertexArray::SetUV(const int aIndex, const YVRMath::vec3& aUV) {
    if (m.uvs.size() < (aIndex + 1)) {
        m.uvs.resize(aIndex + 1);
    }
    m.uvs[aIndex] = aUV;
}

void VertexArray::SetColor(const int aIndex, const Color& aColor) {
    if (m.colors.size() < (aIndex + 1)) {
        m.colors.resize(aIndex + 1);
    }
    m.colors[aIndex] = aColor;
}

int VertexArray::AppendVertex(const YVRMath::vec3& aPoint) {
    m.vertices.push_back(aPoint);
    return m.vertices.size() - 1;
}

int VertexArray::AppendNormal(const YVRMath::vec3& aNormal) {
    m.normals.emplace_back(State::NormalState(aNormal));
    return m.normals.size() - 1;
}

void VertexArray::AddNormal(const int aIndex, const YVRMath::vec3& aNormal) {
    if (m.normals.size() < (aIndex + 1)) {
        m.normals.resize(aIndex + 1);
    }
    State::NormalState& ns = m.normals[aIndex];
    const float originalCount = ns.count;
    ns.count++;
    YVRMath::vec3 orig = ns.normal;
    ns.normal = YVRMath::normalize(((ns.normal * originalCount) + aNormal) / ns.count);
}

int VertexArray::AppendUV(const YVRMath::vec3& aUV) {
    m.uvs.push_back(aUV);
    return m.uvs.size() - 1;
}

int VertexArray::AppendColor(const Color& aColor) {
    m.colors.push_back(aColor);
    return m.colors.size() - 1;
}

VertexArray::VertexArray(State& aState, CreationContextPtr& aContext) : m(aState) {}

}  // namespace foo
