

#include "foo/Light.h"
#include "foo/private/LightState.h"

#include "foo/ConcreteClass.h"

namespace foo {

LightPtr Light::Create(CreationContextPtr& aContext) { return std::make_shared<ConcreteClass<Light, Light::State> >(aContext); }

const YVRMath::vec3& Light::GetDirection() const { return m.direction; }

const Color& Light::GetAmbientColor() const { return m.ambient; }

const Color& Light::GetDiffuseColor() const { return m.diffuse; }

const Color& Light::GetSpecularColor() const { return m.specular; }

void Light::SetDirection(const YVRMath::vec3& aDirection) {
    if (YVRMath::length(aDirection) > 0.0f) {
        m.direction = YVRMath::normalize(aDirection);
    }
}

void Light::SetAmbientColor(const Color& aColor) { m.ambient = aColor; }

void Light::SetDiffuseColor(const Color& aColor) { m.diffuse = aColor; }

void Light::SetSpecularColor(const Color& aColor) { m.specular = aColor; }

Light::Light(State& aState, CreationContextPtr& aContext) : m(aState) {}
Light::~Light() {}

}  // namespace foo
