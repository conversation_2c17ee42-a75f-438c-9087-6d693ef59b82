

#include "foo/Transform.h"
#include "foo/private/TransformState.h"

#include "foo/ConcreteClass.h"
#include "foo/CullVisitor.h"
#include "foo/Logger.h"

#include <memory>

namespace foo {

TransformPtr Transform::Create(CreationContextPtr& aContext) {
    TransformPtr transform = std::make_shared<ConcreteClass<Transform, Transform::State> >(aContext);
    transform->m.self = transform;
    return transform;
}

void Transform::Cull(CullVisitor& aVisitor, DrawableList& aDrawables) {
    aVisitor.PushTransform(m.transform);
    Group::Cull(aVisitor, aDrawables);
    aVisitor.PopTransform();
}

const YVRMath::mat4 Transform::GetWorldTransform() const {
    YVRMath::mat4 result = m.transform;
    std::vector<GroupPtr> parents;
    GetParents(parents);
    while (parents.size() > 0) {
        if (parents.size() > 1) {
            FOO_WARN("Calculating world transform where node has more than one parent");
        }
        GroupPtr parent = parents[0];
        TransformPtr transform = std::dynamic_pointer_cast<Transform>(parent);
        if (transform) {
            result = transform->GetTransform() * result;
        }
        parents.clear();
        parent->GetParents(parents);
    }
    return result;
}

const YVRMath::mat4& Transform::GetTransform() const { return m.transform; }

void Transform::SetTransform(const YVRMath::mat4& aTransform) { m.transform = aTransform; }

Transform::Transform(State& aState, CreationContextPtr& aContext) : Group(aState, aContext), m(aState) {}
Transform::~Transform() {}

}  // namespace foo
