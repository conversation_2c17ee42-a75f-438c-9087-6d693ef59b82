

#include "foo/TextureCubeMap.h"
#include "foo/ConcreteClass.h"
#include "foo/private/TextureState.h"

#include "foo/CreationContext.h"
#include "foo/DataCache.h"
#include "foo/FileReader.h"
#include "foo/GLError.h"
#include "foo/Logger.h"
#include "foo/RenderContext.h"
#include "foo/private/ResourceGLState.h"

#include <cstring>
#include <vector>
#include "foo/gl.h"

namespace {

struct CubeMapFace {
    GLenum target;
    GLint level;
    GLint internalFormat;
    GLsizei width;
    GLsizei height;
    GLint border;
    GLenum format;
    GLenum type;
    GLsizei dataSize;
    std::unique_ptr<uint8_t[]> data;
    uint32_t dataCacheHandle;

    CubeMapFace() : target(GL_TEXTURE_CUBE_MAP_POSITIVE_X), level(0), internalFormat(GL_RGB), width(0), height(0), border(0), format(GL_RGB), type(GL_UNSIGNED_BYTE), dataSize(0), dataCacheHandle(0) {}

 private:
    CubeMapFace(const CubeMapFace&) = delete;
    CubeMapFace& operator=(const CubeMapFace&) = delete;
};

class CubeMapTextureHandler;
typedef std::shared_ptr<CubeMapTextureHandler> CubeMapTextureHandlerPtr;

class CubeMapTextureHandler : public foo::FileHandler {
 public:
    static CubeMapTextureHandlerPtr Create(const foo::TextureCubeMapPtr& aTexture, GLenum aFaceTarget);
    void BindFileHandle(const std::string& aFileName, const int aFileHandle) override;
    void LoadFailed(const int aFileHandle, const std::string& aReason) override;
    void ProcessRawFileChunk(const int aFileHandle, const char* aBuffer, const size_t aSize) override {};
    void FinishRawFile(const int aFileHandle) override {};
    void ProcessImageFile(const int aFileHandle, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat) override;
    CubeMapTextureHandler() {}
    ~CubeMapTextureHandler() {}

 protected:
    foo::TextureCubeMapPtr mTexture;
    GLenum mFaceTarget;

 private:
    FOO_NO_DEFAULTS(CubeMapTextureHandler);
};

CubeMapTextureHandlerPtr CubeMapTextureHandler::Create(const foo::TextureCubeMapPtr& aTexture, GLenum aFaceTarget) {
    CubeMapTextureHandlerPtr result = std::make_shared<CubeMapTextureHandler>();
    result->mTexture = aTexture;
    result->mFaceTarget = aFaceTarget;
    return result;
}

void CubeMapTextureHandler::BindFileHandle(const std::string& aFileName, const int aFileHandle) {}

void CubeMapTextureHandler::LoadFailed(const int aFileHandle, const std::string& aReason) { FOO_ERROR("Failed to load CubeMap texture for target %d: %s", mFaceTarget, aReason.c_str()); }

void CubeMapTextureHandler::ProcessImageFile(const int aFileHandle, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat) {
    if (mTexture) {
        mTexture->SetImageData(mFaceTarget, aImage, aImageLength, aWidth, aHeight, aFormat);
    }
}

}  // namespace

namespace foo {

struct TextureCubeMap::State : public Texture::State, public ResourceGL::State {
    bool dirty;
    GLuint externalTexture;
    CubeMapFace faces[6];
    DataCachePtr dataCache;

    void CreateTexture();
    void DestroyTexture();
};

void TextureCubeMap::State::CreateTexture() {
    if (!dirty) {
        return;
    }
    for (CubeMapFace& face : faces) {
        if (!face.data) {
            if (dataCache && (face.dataCacheHandle > 0)) {
                dataCache->LoadData(face.dataCacheHandle, face.data);
            }
            if (!face.data) {
                return;
            }
        }
    }
    if (externalTexture) {
        texture = externalTexture;
    } else {
        FOO_GL_CHECK(glGenTextures(1, &texture));
    }
    FOO_GL_CHECK(glBindTexture(target, texture));
    for (CubeMapFace& face : faces) {
        const bool isRGB = face.format == GL_RG8 || face.format == GL_RGBA;
        if (externalTexture && isRGB) {
            FOO_GL_CHECK(glTexSubImage2D(face.target, face.level, 0, 0, face.width, face.height, face.format, face.type, (void*)face.data.get()));
        } else if (isRGB) {
            FOO_GL_CHECK(glTexImage2D(face.target, face.level, face.internalFormat, face.width, face.height, face.border, face.format, face.type, (void*)face.data.get()));
        } else if (externalTexture) {
            FOO_GL_CHECK(glCompressedTexSubImage2D(face.target, face.level, 0, 0, face.width, face.height, face.format, face.dataSize, (void*)face.data.get()));
        } else {
            FOO_GL_CHECK(glCompressedTexImage2D(face.target, face.level, face.internalFormat, face.width, face.height, face.border, face.dataSize, (void*)face.data.get()));
        }
        if (!face.dataCacheHandle && dataCache) {
            face.dataCacheHandle = dataCache->CacheData(face.data, (size_t)face.dataSize);
        } else if (face.dataCacheHandle > 0) {
            face.data = nullptr;
        }
    }

    for (auto param = intMap.begin(); param != intMap.end(); param++) {
        FOO_GL_CHECK(glTexParameteri(target, param->first, param->second));
    }
    dirty = false;
}

void TextureCubeMap::State::DestroyTexture() {
    if (externalTexture) {
        // Texture life cycle is handled outside of this class (e.g TimeWarp Cubemap Layer)
        texture = 0;
    } else if (texture > 0) {
        FOO_GL_CHECK(glDeleteTextures(1, &texture));
        texture = 0;
    }
    dirty = true;
}

TextureCubeMapPtr TextureCubeMap::Create(CreationContextPtr& aContext, GLuint aExternalTexture) {
    auto result = std::make_shared<ConcreteClass<TextureCubeMap, TextureCubeMap::State> >(aContext);
    result->m.externalTexture = aExternalTexture;
    return result;
}

void TextureCubeMap::Load(CreationContextPtr& aContext, const TextureCubeMapPtr& aTexture, const std::string& aFileXPos, const std::string& aFileXNeg, const std::string& aFileYPos,
                          const std::string& aFileYNeg, const std::string& aFileZPos, const std::string& aFileZNeg) {
    FileReaderPtr reader = aContext->GetFileReader();

    if (!reader) {
        FOO_ERROR("FileReaderPtr not found while loading a CubeMap");
        return;
    }

    reader->ReadImageFile(aFileXPos, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_POSITIVE_X));
    reader->ReadImageFile(aFileXNeg, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_NEGATIVE_X));
    reader->ReadImageFile(aFileYPos, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_POSITIVE_Y));
    reader->ReadImageFile(aFileYNeg, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_NEGATIVE_Y));
    reader->ReadImageFile(aFileZPos, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_POSITIVE_Z));
    reader->ReadImageFile(aFileZNeg, CubeMapTextureHandler::Create(aTexture, GL_TEXTURE_CUBE_MAP_NEGATIVE_Z));
}

void TextureCubeMap::SetImageData(const GLenum aFaceTarget, std::unique_ptr<uint8_t[]>& aImage, const uint64_t aImageLength, const int aWidth, const int aHeight, const GLenum aFormat) {
    if ((aWidth <= 0) || (aHeight <= 0)) {
        return;
    }

    if (!aImage) {
        return;
    }

    int index = aFaceTarget - GL_TEXTURE_CUBE_MAP_POSITIVE_X;
    if (index < 0 || index >= 6) {
        return;
    }

    CubeMapFace& face = m.faces[index];
    face.width = aWidth;
    face.height = aHeight;
    face.internalFormat = aFormat;
    face.format = aFormat;
    face.dataSize = (GLsizei)aImageLength;
    face.data = std::move(aImage);
    m.dirty = true;
}

TextureCubeMap::TextureCubeMap(State& aState, CreationContextPtr& aContext) : Texture(aState, aContext), ResourceGL(aState, aContext), m(aState) {
    m.dataCache = aContext->GetDataCache();
    m.target = GL_TEXTURE_CUBE_MAP;
    for (int i = 0; i < 6; ++i) {
        m.faces[i].target = (GLenum)GL_TEXTURE_CUBE_MAP_POSITIVE_X + i;
    }
}

TextureCubeMap::~TextureCubeMap() {
    if (!m.dataCache) {
        return;
    }
    for (CubeMapFace& face : m.faces) {
        if (face.dataCacheHandle > 0) {
            m.dataCache->RemoveData(face.dataCacheHandle);
            face.dataCacheHandle = 0;
        }
    }
}

void TextureCubeMap::AboutToBind() { m.CreateTexture(); }

bool TextureCubeMap::SupportOffRenderThreadInitialization() { return true; }

void TextureCubeMap::InitializeGL() { m.CreateTexture(); }

void TextureCubeMap::ShutdownGL() { m.DestroyTexture(); }

}  // namespace foo
