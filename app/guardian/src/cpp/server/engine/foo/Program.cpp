

#include "foo/Program.h"
#include <foo/GLError.h>
#include <foo/ShaderUtil.h>

#include "foo/ConcreteClass.h"

namespace foo {

struct Program::State {
    GLuint program = 0;
    uint32_t features = 0;
};

ProgramPtr Program::Create() { return std::make_shared<ConcreteClass<Program, Program::State> >(); }

bool Program::Enable() {
    if (!m.program) {
        return false;
    }

    FOO_GL_CHECK(glUseProgram(m.program));
    return true;
}

void Program::SetFeatures(const uint32_t aFeatures) { m.features = aFeatures; }

bool Program::SupportsFeatures(const uint32_t aFeatures) { return (m.features & aFeatures) == aFeatures; }

void Program::SetProgram(GLuint aProgram) { m.program = aProgram; }

GLuint Program::GetProgram() const { return m.program; }

GLint Program::GetAttributeLocation(const char* aName) {
    if (!m.program) {
        return -1;
    }

    return foo::GetAttributeLocation(m.program, aName);
}

GLint Program::GetUniformLocation(const char* aName) {
    if (!m.program) {
        return -1;
    }

    return foo::GetUniformLocation(m.program, aName);
}

Program::Program(State& aState) : m(aState) {}

}  // namespace foo
