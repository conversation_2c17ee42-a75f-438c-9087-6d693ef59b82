

#include "foo/Node.h"
#include "foo/Logger.h"
#include "foo/private/NodeState.h"

namespace foo {

const std::string& Node::GetName() const { return m.name; }

void Node::SetName(const std::string& aName) { m.name = aName; }

void Node::GetParents(std::vector<GroupPtr>& aParents) const {
    for (GroupWeak& weak : m.parents) {
        if (GroupPtr parent = weak.lock()) {
            aParents.push_back(std::move(parent));
        } else {
            FOO_WARN("Deleted weak parent found in parent list in Node::GetParents Node: %s", m.name.c_str());
        }
    }
}

void Node::RemoveFromParents() {
    for (GroupWeak& weak : m.parents) {
        if (GroupPtr parent = weak.lock()) {
            parent->RemoveNode(*this);
        } else {
            FOO_WARN("Deleted weak parent found in parent list in Node::RemoveFromParents Node: %s", m.name.c_str());
        }
    }
    if (m.parents.size() != 0) {
        FOO_WARN("Node::RemoveFromParents failed to remove all parents for Node: %s", m.name.c_str());
    }
}

Node::Node(State& aState, CreationContextPtr& aContext) : m(aState) {}
Node::~Node() {
    if (m.parents.size() != 0) {
        const char* name = (m.name.size() ? m.name.c_str() : "<unnamed>");
        FOO_WARN("Node: %s destructor called with parent count != 0", name);
    }
}

void Node::AddToParents(GroupWeak& aParent, Node& aChild) { aChild.m.parents.push_back(aParent); }

void Node::RemoveFromParents(Group& aParent, Node& aChild) {
    for (auto it = aChild.m.parents.begin(); it != aChild.m.parents.end();) {
        Group* node = it->lock().get();
        if (node == &aParent) {
            it = aChild.m.parents.erase(it);
            return;
        } else if (node == nullptr) {
            it = aChild.m.parents.erase(it);
        } else {
            it++;
        }
    }
}

bool Node::Traverse(const NodePtr& aRootNode, const TraverseFunction& aTraverseFunction) {
    if (aTraverseFunction(aRootNode, nullptr)) {
        return true;
    }
    return aRootNode->Traverse(std::dynamic_pointer_cast<Group>(aRootNode), aTraverseFunction);
}

bool Node::Traverse(const GroupPtr& aParent, const TraverseFunction& aTraverseFunction) { return false; }

}  // namespace foo
