

#include "foo/TextureCache.h"
#include "foo/ConcreteClass.h"

#include "foo/CreationContext.h"
#include "foo/DefaultImageData.h"
#include "foo/FileReader.h"
#include "foo/Logger.h"
#include "foo/Mutex.h"
#include "foo/Texture.h"
#include "foo/TextureGL.h"

#include <cstring>
#include <unordered_map>

namespace foo {

struct TextureCache::State {
    Mutex lock;
    TextureGLPtr defaultTexture;
    std::unordered_map<std::string, TextureGLPtr> cache;
};
TextureCachePtr TextureCache::Create() { return std::make_shared<ConcreteClass<TextureCache, TextureCache::State> >(); }

void TextureCache::Init(CreationContextPtr& aContext) {
    MutexAutoLock lock(m.lock);
    m.defaultTexture = TextureGL::Create(aContext);
    const size_t kArraySize = kDefaultImageDataSize * sizeof(uint32_t);
    std::unique_ptr<uint8_t[]> data = std::make_unique<uint8_t[]>(kArraySize);
    memcpy(data.get(), (void*)kDefaultImageData, kArraySize);
    uint64_t length = kDefaultImageDataWidth * kDefaultImageDataHeight * 4;
    m.defaultTexture->SetImageData(data, length, kDefaultImageDataWidth, kDefaultImageDataHeight, GL_RGBA);
}

void TextureCache::Shutdown() {
    m.defaultTexture = nullptr;
    m.cache.clear();
}

TextureGLPtr TextureCache::FindTexture(const std::string& aTextureName) {
    MutexAutoLock lock(m.lock);
    TextureGLPtr result;

    std::unordered_map<std::string, TextureGLPtr>::iterator it = m.cache.find(aTextureName);
    if (it != m.cache.end()) {
        return it->second;
    }

    return result;
}

void TextureCache::AddTexture(const std::string& aTextureName, TextureGLPtr& aTexture) {
    MutexAutoLock lock(m.lock);
    m.cache[aTextureName] = aTexture;
}

TextureGLPtr TextureCache::GetDefaultTexture() { return m.defaultTexture; }

TextureCache::TextureCache(State& aState) : m(aState) {}

TextureCache::~TextureCache() {}

}  // namespace foo
