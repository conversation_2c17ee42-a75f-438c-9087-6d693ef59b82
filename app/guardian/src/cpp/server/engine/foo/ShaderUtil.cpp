

#include "foo/ShaderUtil.h"
#include "foo/GLError.h"
#include "foo/Logger.h"

#include <memory>

namespace foo {

GLint GetAttributeLocation(GLuint aProgram, const char* aName) {
    GLint result = FOO_GL_CHECK(glGetAttribLocation(aProgram, aName));
    if (result < 0) {
        FOO_ERROR("Failed to glGetAttributLocation for '%s'", aName);
    }
    return result;
}

GLint GetAttributeLocation(GLuint aProgram, const std::string& aName) { return GetAttributeLocation(aProgram, aName.c_str()); }

GLint GetUniformLocation(GLuint aProgram, const char* aName) {
    GLint result = FOO_GL_CHECK(glGetUniformLocation(aProgram, aName));
    if (result < 0) {
        FOO_ERROR("Failed to glGetUniformLocation for '%s'", aName);
    }
    return result;
}

GLint GetUniformLocation(GLuint aProgram, const std::string& aName) { return GetUniformLocation(aProgram, aName.c_str()); }

GLuint LoadShader(GLenum aType, const char* aSrc) {
    GLuint shader = FOO_GL_CHECK(glCreateShader(aType));

    if (shader == 0) {
        FOO_ERROR("FAILED to create shader of type: %s", (aType == GL_VERTEX_SHADER ? "vertex shader" : "fragment shader"));
    }

    FOO_GL_CHECK(glShaderSource(shader, 1, &aSrc, nullptr));
    FOO_GL_CHECK(glCompileShader(shader));
    GLint compiled = 0;
    FOO_GL_CHECK(glGetShaderiv(shader, GL_COMPILE_STATUS, &compiled));

    if (!compiled) {
        GLint length = 0;
        glGetShaderiv(shader, GL_INFO_LOG_LENGTH, &length);
        if (length > 1) {
            std::unique_ptr<char[]> log = std::make_unique<char[]>(length);
            FOO_GL_CHECK(glGetShaderInfoLog(shader, length, nullptr, log.get()));
            FOO_ERROR("Failed to compile shader:\n%s", log.get());
            FOO_ERROR("From source:\n%s", aSrc);
        }
    }

    return shader;
}

GLuint CreateProgram(GLuint aVertexShader, GLuint aFragmentShader) {
    GLuint program = FOO_GL_CHECK(glCreateProgram());
    FOO_GL_CHECK(glAttachShader(program, aVertexShader));
    FOO_GL_CHECK(glAttachShader(program, aFragmentShader));
    FOO_GL_CHECK(glLinkProgram(program));
    GLint linked = 0;
    FOO_GL_CHECK(glGetProgramiv(program, GL_LINK_STATUS, &linked));
    if (!linked) {
        GLint length = 0;
        FOO_GL_CHECK(glGetProgramiv(program, GL_INFO_LOG_LENGTH, &length));
        if (length > 1) {
            std::unique_ptr<char[]> log = std::make_unique<char[]>(length);
            FOO_GL_CHECK(glGetProgramInfoLog(program, length, nullptr, log.get()));
            FOO_ERROR("Failed to link program:\n%s", log.get());
        }
        FOO_GL_CHECK(glDeleteProgram(program));
        program = 0;
    }
    return program;
}

}  // namespace foo
