

#include "foo/Texture.h"
#include "foo/private/TextureState.h"

#include "foo/ConcreteClass.h"
#include "foo/GLError.h"
#include "foo/Logger.h"

namespace foo {

void Texture::Bind() {
    AboutToBind();
    FOO_GL_CHECK(glBindTexture(m.target, m.texture));
}

void Texture::Unbind() { FOO_GL_CHECK(glBindTexture(m.target, 0)); }

std::string Texture::GetName() const { return m.name; }

GLenum Texture::GetTarget() const { return m.target; }

GLuint Texture::GetHandle() const { return m.texture; }

void Texture::SetName(const std::string& aName) { m.name = aName; }

void Texture::SetTextureParameter(GLenum aName, GLint aParam) {
    m.intMap[aName] = aParam;
    if (!m.texture) {
        return;
    }
    FOO_GL_CHECK(glBindTexture(m.target, m.texture));
    FOO_GL_CHECK(glTexParameteri(m.target, aName, aParam));
    FOO_GL_CHECK(glBindTexture(m.target, 0));
}

Texture::Texture(State& aState, CreationContextPtr& aContext) : m(aState) {}
Texture::~Texture() {}

}  // namespace foo
