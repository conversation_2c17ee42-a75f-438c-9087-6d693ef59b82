

#include "foo/TextureSurface.h"
#include "foo/ConcreteClass.h"
#include "foo/private/TextureState.h"

#include "foo/GLError.h"
#include "foo/Logger.h"
#include "foo/RenderContext.h"
#include "foo/SurfaceTextureFactory.h"

#include "foo/gl.h"

namespace {

typedef std::weak_ptr<foo::TextureSurface> SurfaceWeakPtr;
typedef std::weak_ptr<foo::SurfaceTextureFactory> FactoryWeakPtr;

class LocalObserver : public foo::SurfaceTextureObserver {
 public:
    LocalObserver(SurfaceWeakPtr& aTexture);
    void SurfaceTextureCreated(const std::string& aName, GLuint aHandle, jobject aSurfaceTexture) override;
    void SurfaceTextureHandleUpdated(const std::string aName, GLuint aHandle) override;
    void SurfaceTextureDestroyed(const std::string& aName) override;
    void SurfaceTextureCreationError(const std::string& aName, const std::string& aReason) override;

 protected:
    SurfaceWeakPtr mTexture;
};

LocalObserver::LocalObserver(SurfaceWeakPtr& aTexture) : mTexture(aTexture) {}

void LocalObserver::SurfaceTextureCreated(const std::string& aName, GLuint aHandle, jobject aSurfaceTexture) {
    if (foo::TextureSurfacePtr texture = mTexture.lock()) {
        texture->SetTextureHandle(aHandle);
    }
}

void LocalObserver::SurfaceTextureHandleUpdated(const std::string aName, GLuint aHandle) {
    if (foo::TextureSurfacePtr texture = mTexture.lock()) {
        texture->SetTextureHandle(aHandle);
    }
}

void LocalObserver::SurfaceTextureDestroyed(const std::string& aName) {
    if (foo::TextureSurfacePtr texture = mTexture.lock()) {
        texture->SetTextureHandle(0);
    }
}

void LocalObserver::SurfaceTextureCreationError(const std::string& aName, const std::string& aReason) { FOO_ERROR("Failed to create SurfaceTexture[%s]: %s", aName.c_str(), aReason.c_str()); }

}  // namespace

namespace foo {

struct TextureSurface::State : public Texture::State {
    FactoryWeakPtr factory;
};

TextureSurfacePtr TextureSurface::Create(RenderContextPtr& aContext, const std::string& aName) {
    TextureSurfacePtr result = std::make_shared<ConcreteClass<TextureSurface, TextureSurface::State> >(aContext->GetRenderThreadCreationContext());
    result->SetName(aName);
    SurfaceWeakPtr weakPtr = result;
    if (SurfaceTextureFactoryPtr factory = aContext->GetSurfaceTextureFactory()) {
        result->m.factory = factory;
        factory->CreateSurfaceTexture(aName, std::make_shared<LocalObserver>(weakPtr));
    }

    return result;
}

TextureSurface::TextureSurface(State& aState, CreationContextPtr& aContext) : Texture(aState, aContext), m(aState) { m.target = GL_TEXTURE_EXTERNAL_OES; }

TextureSurface::~TextureSurface() {
    if (SurfaceTextureFactoryPtr factory = m.factory.lock()) {
        factory->DestroySurfaceTexture(m.name);
    }
}

void TextureSurface::SetTextureHandle(const GLuint aHandle) { m.texture = aHandle; }

void TextureSurface::AboutToBind() {}

}  // namespace foo
