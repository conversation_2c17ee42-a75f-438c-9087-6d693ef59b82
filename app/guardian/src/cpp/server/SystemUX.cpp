#include "SystemUX.h"
#include <memory>
#include "foo/ConcreteClass.h"
#include "SystemUXService.h"
#include "BoundaryController.h"
#include "trackingapi/TrackingAPI_Helper.h"
#include "foo/Logger.h"
#include "SystemUXJava.h"
#include "SystemUXMessageType.h"
#include "BoundaryFactory.h"
#include "BoundaryDetector.h"
#include "Boundary.h"
#include "EventTriggerConfitionImpl.h"
#include "yvrutils/WPEventLog.h"
#include <cutils/properties.h>
#include "yvrutils/yvrProperties.h"
#include "yvrutils/PerformanceUtils.h"

#define SENSOR_HW_OK (0x7FF)
const std::string SPATIAL_HOME_PACKAGE = "com.yvr.spatialhome";
const float NEAREST_OBJECT_DISTANCE_THRESHOLD = 0.20;
const int HIDE_OBJECT_APPROACH = 0;
const int DISPLAY_OBJECT_APPROACH_WAY_TICK_SEETHROUGH = 1;
const int DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST = 2;

namespace yvr {

namespace systemux {
class BoundaryPauseStateEnter : public StateObserver {
 public:
    using Super = StateObserver;
    using ConditionImpl = EventTriggerConditionImpl<BoundaryPause>;
    static BoundaryPauseStateEnterPtr Create(SystemUXWeakPtr system) {
        BoundaryPauseStateEnterPtr result = std::make_shared<foo::ConcreteClassSimple<BoundaryPauseStateEnter>>();
        result->m_system = system;
        return result;
    }

 protected:
    BoundaryPauseStateEnter() : Super(ConditionImpl()) {}
    void onStateEnter() override {
        auto system = m_system.lock();
        if (system) {
            FOO_LOG("BoundaryPauseStateEnter");
            system->pauseBoundary();
        }
    }

 private:
    SystemUXWeakPtr m_system;
};

class BoundaryResumeStateEnter : public StateObserver {
 public:
    using Super = StateObserver;
    using ConditionImpl = EventTriggerConditionImpl<BoundaryResume>;
    static BoundaryResumeStateEnterPtr Create(SystemUXWeakPtr system) {
        BoundaryResumeStateEnterPtr result = std::make_shared<foo::ConcreteClassSimple<BoundaryResumeStateEnter>>();
        result->m_system = system;
        return result;
    }

 protected:
    BoundaryResumeStateEnter() : Super(ConditionImpl()) {}
    void onStateEnter() override {
        auto system = m_system.lock();
        if (system) {
            system->resumeBoundary();
            FOO_LOG("BoundaryResumeStateEnter");
        }
    }

 private:
    SystemUXWeakPtr m_system;
};

class MeshNearestObjectEnableStateEnter : public StateObserver {
 public:
    using Super = StateObserver;
    using ConditionImpl = EventTriggerConditionImpl<MeshNearestObjectEnable>;
    static MeshNearestObjectEnableStateEnterPtr Create(SystemUXWeakPtr system) {
        MeshNearestObjectEnableStateEnterPtr result = std::make_shared<foo::ConcreteClassSimple<MeshNearestObjectEnableStateEnter>>();
        result->m_system = system;
        return result;
    }

 protected:
    MeshNearestObjectEnableStateEnter() : Super(ConditionImpl()) {}
    void onStateEnter() override {
        auto system = m_system.lock();
        if (system) {
            system->onNearestObjectEnableChange(true);
        }
    }

 private:
    SystemUXWeakPtr m_system;
};

class MeshNearestObjectDisableStateEnter : public StateObserver {
 public:
    using Super = StateObserver;
    using ConditionImpl = EventTriggerConditionImpl<MeshNearestObjectDisable>;
    static MeshNearestObjectDisableStateEnterPtr Create(SystemUXWeakPtr system) {
        MeshNearestObjectDisableStateEnterPtr result = std::make_shared<foo::ConcreteClassSimple<MeshNearestObjectDisableStateEnter>>();
        result->m_system = system;
        return result;
    }

 protected:
    MeshNearestObjectDisableStateEnter() : Super(ConditionImpl()) {}
    void onStateEnter() override {
        auto system = m_system.lock();
        if (system) {
            system->onNearestObjectEnableChange(false);
        }
    }

 private:
    SystemUXWeakPtr m_system;
};

class MainMessageHandler : public yvrutils::MessageHandler {
 public:
    explicit MainMessageHandler(SystemUXWeakPtr system) : m_system(system) {}
    void handleMessage(const yvrutils::Message& msg) override {
        switch (msg.what) {
            case SystemUXMessageType::ADD_RENDER_OBJECT_BOUNDARY: {
                // FOO_LOG("ADD_RENDER_OBJECT_BOUNDARY");
                auto system = m_system.lock();
                auto currentBoundary = system->m_BoundaryFactory->GetWeakLocalBoundary().lock();
                if (currentBoundary) {
                    BoundaryMesh boundaryMesh = currentBoundary->GetMesh();
                    BoundaryMesh groundMesh = currentBoundary->GetGroundMesh();
                    BoundaryMesh sphereMesh = currentBoundary->GetSphereMesh();
                    auto center = currentBoundary->GetCenter();
                    auto spatial = system->m_RenderEngine->GetSpatial();
                    float radius = currentBoundary->GetRadius();
                    int boundaryType = currentBoundary->GetBoundaryType();
                    system->m_RenderEngine->PostRunnableToRenderThread(
                        [spatial, boundaryMesh, groundMesh, sphereMesh, center, radius, boundaryType] { spatial->addBoundary(boundaryType, boundaryMesh, groundMesh, sphereMesh, center, radius); });
                    system->m_CollisionDetectorPtr->AddBoundary(currentBoundary);
                }
                break;
            }
            case SystemUXMessageType::GROUND_DISTANCE_UPDATA: {
                float groundDistance = *reinterpret_cast<float*>(msg.data);
                auto system = m_system.lock();
                system->m_CollisionDetectorPtr->SetGroundOffSet(groundDistance);
                auto spatial = system->m_RenderEngine->GetSpatial();
                system->m_RenderEngine->PostRunnableToRenderThread([spatial, groundDistance] { spatial->UpdateGroundDistance(groundDistance); });
            }

            default:
                break;
        }
    }

 private:
    SystemUXWeakPtr m_system;
};

SystemUXPtr Instance;
SystemUXPtr SystemUX::Create(foo::JavaContext java, foo::RunnableQueuePtr& runnableQueue) {
    SystemUXPtr result = std::make_shared<foo::ConcreteClassSimple<SystemUX>>();
    result->m_self = result;
    result->m_java = java;
    result->init(runnableQueue);
    Instance = result;
    return result;
}
SystemUXPtr SystemUX::getInstance() { return Instance; }

void onDeviceStateEventChangeStatic(EventHeaderTA* eventHeader) {
    switch (eventHeader->type) {
        case EVENT_TYPE_EYE_STATE: {
            EyeStateEventTA* eyeStateEvent = (EyeStateEventTA*)eventHeader;
            FOO_LOG("onEyeStateEventChange %d", eyeStateEvent->state);
            bool isValid = false;
            if (eyeStateEvent->state == EyeStateValid) {
                isValid = true;
            } else {
                isValid = false;
            }

            SystemUX::getInstance()->onEyeStateEventChange(isValid);
            break;
        }
        case EVENT_TYPE_LARGE_SPACE_MAP_RECOGNIZE_RESULT: {
            auto evt = reinterpret_cast<LargeSpaceMapStateChangeEventTA*>(eventHeader);
            SystemUX::getInstance()->onLargeSpaceMapRecognition(evt->result);
            break;
        }
        case EVENT_TYPE_CAMERA_SENSOR_DAMAGE: {
            int32_t sensorMask = property_get_int32(PROPERTY_CAMERA_HW_STATE, SENSOR_HW_OK);
            if (sensorMask != SENSOR_HW_OK) {
                FOO_LOG("camera damage, mask: %x", sensorMask);
                SystemUX::getInstance()->onHardWareDamage(sensorMask);
            }
            break;
        }
        case EVENT_TYPE_HMD_IMU_DAMAGE: {
            FOO_LOG("Imu damage");
            SystemUX::getInstance()->onHardWareDamage(1);
            break;
        }
        case EVENT_TYPE_MESH_NEAREST_OBJECT_UPDATE: {
            auto evt = reinterpret_cast<MeshNearestObjectUpdateEventTA*>(eventHeader);
            SystemUX::getInstance()->onNearestObjectUpdate(evt->distance);
            break;
        }
        default:
            break;
    }
}
void SystemUX::onLargeSpaceMapRecognition(const int resultCode) {
    m_ThreadPool.detach_task([this, resultCode]() {
        if (resultCode == 1) {
            m_LargeSpaceMapRecognized = true;
            m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_LARGE_SPACE_MAP_RECOGNIZED);
            createGameBoundary();
        } else if (resultCode == -1) {
            m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_LARGE_SPACE_MAP_LOSS);
            m_LargeSpaceMapRecognized = false;
        }
    });
}

void SystemUX::onEyeStateEventChange(bool valid) {
    m_ThreadPool.detach_task([=]() { m_SystemUXJava->onMotionChange(valid ? yvrMotionType::MOTION_TYPE_EYE_TRACKING_VALID : yvrMotionType::MOTION_TYPE_EYE_TRACKING_INVALID); });
}

void SystemUX::createGameBoundary() {
    if (!m_LargeSpaceMapRecognized) {
        FOO_ERROR("[SystemUX] createGameBoundary fail largespace map not recognized!");
        return;
    }
    float effectDistance = m_SystemUXJava->GetBoundaryEffectDistance();
    m_EffectDistance = effectDistance;
    m_CollisionDetectorPtr->SetEffectDistance(effectDistance);
    int effectType = m_SystemUXJava->GetBoundaryOutEffectType();
    m_BoundaryEffectType = static_cast<BoundarOutEffectTypeGA>(effectType);
    int32_t length = 0;
    float points[512];
    float groundDistance = 0.0f;
    TRACKINGAPI_GetLargeSpaceBoundaryData(&length, points, &groundDistance);
    std::vector<float> pointVector;
    for (int i = 0; i < length - 2; i++) {
        pointVector.emplace_back(points[i]);
    }
    m_CollisionDetectorPtr->SetGroundOffSet(groundDistance);
    m_BoundaryController->CreateGameBoundary(pointVector);
    SceneConfigurationPtr emptyScence(new SceneConfiguration());
    m_RenderEngine->GetSpatial()->updateSceneConfig(emptyScence);
}

void SystemUX::init(foo::RunnableQueuePtr& runnableQueue) {
    // 连接Tracking helper
    TRACKINGAPI_Init();

    m_InLargeSpaceMode = property_get_bool("persist.yvr.large_space_enable", false);  // 默认关闭大空间模式
    m_StateFlow = StateFlow::Create();
    m_BoundaryPauseStateEnter = BoundaryPauseStateEnter::Create(m_self);
    m_BoundaryResumeStateEnter = BoundaryResumeStateEnter::Create(m_self);

    m_MeshNearestObjectEnableStateEnter = MeshNearestObjectEnableStateEnter::Create(m_self);
    m_MeshNearestObjectDisableStateEnter = MeshNearestObjectDisableStateEnter::Create(m_self);
    m_StateFlow->Subscribe(m_BoundaryPauseStateEnter);
    m_StateFlow->Subscribe(m_BoundaryResumeStateEnter);
    m_StateFlow->Subscribe(m_MeshNearestObjectEnableStateEnter);
    m_StateFlow->Subscribe(m_MeshNearestObjectDisableStateEnter);
    m_BoundaryFactory = BoundaryFactory::Create();
    m_MainHandlerThread = new yvrutils::HandlerThread();
    m_MainHandlerThread->start("main");
    m_MainThreadLooper = m_MainHandlerThread->getLooper();
    m_MainMessageHandler = new MainMessageHandler(m_self);
    m_BoundaryController = BoundaryController::Create(m_MainThreadLooper, m_MainMessageHandler, m_BoundaryFactory);
    m_CollisionDetectorPtr = BoundaryDetector::Create();

    // m_BoundaryController->SetBoundaryUpdateEnable(true);
    // m_BoundaryController->StartDetector();
    m_SystemUXJava = std::make_shared<SystemUXJava>();
    m_RenderEngine = foo::RenderEngine::Create(m_java, runnableQueue, *m_SystemUXJava);
    m_RenderEngine->SetBoundaryDetector(m_CollisionDetectorPtr.get());
    m_PerformanceSystem = PerformanceSystem::Create();
    m_PerformanceSystem->addObserver(m_PerformanceObserverId);
    m_RenderEngine->AssociateTick(m_BoundaryController);
    m_RenderEngine->AssociateTick(m_PerformanceSystem);
    m_RenderEngine->AssociateTick(m_self.lock());

    m_DetectionHandler = new DetectionHandler();
    m_DetectionHandler->SetWearingDetectionStatusCallback(m_SystemUXJava);
    yvrutils::Message stateMsg;
    stateMsg.what = DetectionThreadMessage::DT_MSG_INIT;
    m_DetectionHandler->sendMessage(stateMsg);
    // 初始化引擎模块
    // 初始化其他模块
    SystemUXService::getInstance()->startService();
    TRACKINGAPI_RegisterForNotification(&onDeviceStateEventChangeStatic);
}

bool SystemUX::ready() { return m_RenderEngine != nullptr && m_RenderEngine->Ready(); }
void SystemUX::setTemporaryFilePath(const std::string& aPath) {}
void SystemUX::setHapticFeedback(int controller, float pulseDuration, float pulseIntensity) {}
void SystemUX::addWidget(const int32_t aHandle, const foo::WidgetPlacementPtr& placement) {
    FOO_LOG("addWidget %d", aHandle);
    m_RenderEngine->GetWidgetCoordinator().addWidget(aHandle, placement);
}
void SystemUX::updateWidget(const int32_t aHandle, const foo::WidgetPlacementPtr& aPlacement) { m_RenderEngine->GetWidgetCoordinator().updateWidget(aHandle, aPlacement); }
void SystemUX::removeWidget(const int32_t aHandle) { m_RenderEngine->GetWidgetCoordinator().removeWidget(aHandle); }

void SystemUX::SetGroundDistance(float distance) {
    FOO_LOG("setGroundDistance %f", distance);
    m_RenderEngine->SetGroundDistance(distance);
}

bool SystemUX::isInGazeMode() { return false; }
void SystemUX::setPrimaryController(const int mask) {}
void SystemUX::setWorldActive(bool active) {
    if (active) {
        m_RenderEngine->Resume();
    } else {
        m_RenderEngine->Pause();
    }
}
void SystemUX::adjustIPD(const int step) { m_DetectionHandler->WearingDetectionRequest().MoveIpd(step); }
void SystemUX::loadETCalibrationData() { m_DetectionHandler->WearingDetectionRequest().LoadETCalibrationData(); }

void SystemUX::wearingDetectionY() { m_DetectionHandler->WearingDetectionRequest().StageAction(WEARING_DETECTION_CMD_ACTION::POSITION_Y_JUDGEMENT); }
void SystemUX::wearingDetectionX() { m_DetectionHandler->WearingDetectionRequest().StageAction(WEARING_DETECTION_CMD_ACTION::POSITION_X_JUDGEMENT); }
void SystemUX::wearingDetectionYX() { m_DetectionHandler->WearingDetectionRequest().StageAction(WEARING_DETECTION_CMD_ACTION::POSITION_Y_X_JUDGEMENT); }
void SystemUX::autoAdjustIPD() { m_DetectionHandler->WearingDetectionRequest().StageAction(WEARING_DETECTION_CMD_ACTION::IPD_ADJUSTMENT); }
void SystemUX::diopterLensCheck() { m_DetectionHandler->WearingDetectionRequest().StageAction(WEARING_DETECTION_CMD_ACTION::DIOPTER_LENS_CHECK); }

void SystemUX::toogleSeeThrough(const bool enable) { m_RenderEngine->ToogleSeeThrough(enable); }
float SystemUX::getIpdValue() {
    float ipd[3];

    int ret = TRACKINGAPI_GetSystemPropertyFloatArray(kIpdFrustumProp, ipd, 3);
    if (ret != 0) {
        FOO_ERROR("get ipd value fail %d", ret);
        return 0.0;
    }
    float result = ipd[0] * 1000;
    FOO_LOG("get ipd double value %f", result);
    if (result >= 69.4) {  // 最大值为69.400002，为了好看改为69.5
        result = 69.5;
    }

    return result;
}

float SystemUX::getIpdConfigValue() {
    IPDConfiguration ipdConfig = {0.0f};
    int ret = TRACKINGAPI_GetIPDConfiguration(&ipdConfig);
    if (ret != 0) {
        FOO_ERROR("Failed to get ipd config %d", ret);
        return 0.0;
    }

    return ipdConfig.currIPD;
}

void SystemUX::updateBoundaryFrameStatus(const BoundaryFrameStatus status) {
    bool wasOut = m_BoundaryFrameStatus.isOut;
    bool isOut = status.isOut;

    // FOO_LOG("updateBoundaryFrameStatus isOut:%d,isCollisionOccurred %d,headToBoundaryDistance %f,leftToBoundaryDistance %f,rightToBoundaryDistance %f,blendRate %f", isOut ? 1 : 0,
    //         status.isCollisionOccurred ? 1 : 0, status.headToBoundaryDistance, status.leftToBoundaryDistance, status.rightToBoundaryDistance, status.blendRate);

    // 状态变化的处理
    if (m_BoundaryFrameStatus.boundaryId != status.boundaryId || wasOut != isOut) {
        if (isOut) {
            FOO_LOG("out of boundary %d", status.boundaryId);
            handleOutOfBoundary();
        } else {
            FOO_LOG("in to boundary %d", status.boundaryId);
            handleBackToBoundary();
        }
    }

    // 更新状态
    m_BoundaryFrameStatus = status;
    m_RenderEngine->updateBoundaryFrameStatus(status);
}

void SystemUX::handleOutOfBoundary() {
    m_ThreadPool.detach_task([this]() {
        if (m_BoundaryFrameStatus.boundaryType == BOUNDAY_TYPE_GAME &&
            (m_BoundaryFrameStatus.effectType == BoundarOutEffectTypeGA::EFFECT_GRID || m_BoundaryFrameStatus.effectType == BoundarOutEffectTypeGA::EFFECT_NONE)) {
            // FOO_WARN("SystemUX: out boundary effect node");
            return;
        }
        m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_OUTBOUNDARY);
        if (m_BoundaryFrameStatus.boundaryType == BOUNDAY_TYPE_LOCAL) {
            std::string packageName = m_SystemUXJava->GetMainDisplayAppPackageName();
            if (SPATIAL_HOME_PACKAGE != packageName) {
                FOO_LOG("show app icon %s", packageName.c_str());
                m_RenderEngine->PostRunnableToRenderThread([packageName, this] { m_RenderEngine->GetSpatial()->ShowAppIconObject(packageName); });
            }
        }
    });
}

void SystemUX::handleBackToBoundary() {
    m_ThreadPool.detach_task([this]() { m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_INBOUNDARY); });
    m_RenderEngine->GetSpatial()->HideAppIconObject();
}

void SystemUX::pauseBoundary() {
    m_IsBoundaryAlive = false;
    m_BoundaryFrameStatus.boundaryId = UINT64_MAX;
    m_ThreadPool.detach_task([this]() { m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_INBOUNDARY); });
    m_BoundaryFactory->ClearAllBoundary();
    m_RenderEngine->pauseBoundary();
}

void SystemUX::forceStopWearingDetection() { m_DetectionHandler->WearingDetectionRequest().ForceStop(); }

void SystemUX::updateSceneConfig(const foo::SceneConfigurationPtr& config) { m_RenderEngine->GetSpatial()->updateSceneConfig(config); }

void SystemUX::updateGazeControllerState(const float indexTrigger) { m_RenderEngine->GetSpatial()->updateGazeControllerState(indexTrigger); }
bool SystemUX::IsInGazeMode() { return m_RenderEngine->GetSpatial()->IsInGazeMode(); }

std::string SystemUX::dumpSystemUX(SystemUXFlags_t flags) {
    std::stringstream ss;

    if (flags & SYSTEMUX_DUMP_CREATE_LOCAL_BOUNDARY) {
        ss << "\nCreate test local boundary\n";
        createLocalBoundary();
    }
    return ss.str();
}
void SystemUX::onTick() {
    static bool isFirstTicked = true;
    if (isFirstTicked) {
        isFirstTicked = false;
        int32_t sensorMask = property_get_int32(PROPERTY_CAMERA_HW_STATE, SENSOR_HW_OK);
        if (sensorMask != SENSOR_HW_OK) {
            FOO_LOG("camera damage, mask: %x", sensorMask);
            onHardWareDamage(sensorMask);
        }
    }
    if (m_CollisionDetectorPtr->IsActive()) {
        PoseStateTA pose;
        int32_t ret = TRACKINGAPI_GetHeadPose(&pose);
        yvrPosef headPose = algToSys(pose.pose);
        yvrPosef recenterPose;
        TRACKINGAPI_GetRecenterPose(&recenterPose);
        headPose = ~recenterPose * headPose;
        CollisionResult collisionResult;
        collisionResult.boundaryId = UINT64_MAX;
        if (m_CollisionDetectorPtr->TestCollision(headPose, collisionResult, true) == 0) {
            handleBoundaryCollision(collisionResult);
        }
    }
    m_ThreadPool.detach_task([this]() {
        handleDeviceStatusChange(m_SystemUXJava->GetDeviceStatus());
        if (m_NearestObjectDistance < NEAREST_OBJECT_DISTANCE_THRESHOLD) {
            if (m_DisplayObjectApproachWay != DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST) {
                m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_SHOW_CLOSE_TO_OBJECTS_TOAST);
                m_DisplayObjectApproachWay = DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST;
            }
            // if (m_CollisionDetectorPtr->IsActive()) {
            //     if (!m_BoundaryFrameStatus.isOut && m_DisplayObjectApproachWay != DISPLAY_OBJECT_APPROACH_WAY_TICK_SEETHROUGH) {
            //         m_DisplayObjectApproachWay = DISPLAY_OBJECT_APPROACH_WAY_TICK_SEETHROUGH;
            //         m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_DOUBLE_TAP);
            //     }
            // } else if (m_DisplayObjectApproachWay != DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST) {
            //     m_DisplayObjectApproachWay = DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST;
            //     m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_SHOW_CLOSE_TO_OBJECTS_TOAST);
            // }

        } else {
            if (m_DisplayObjectApproachWay == DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST) {
                m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_HIDE_CLOSE_TO_OBJECTS_TOAST);
            }
            m_DisplayObjectApproachWay = HIDE_OBJECT_APPROACH;

            // m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_HIDE_CLOSE_TO_OBJECTS_TOAST);
            //     if (m_DisplayObjectApproachWay == DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST) {
            //         m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_HIDE_CLOSE_TO_OBJECTS_TOAST);
            //     } else if (m_DisplayObjectApproachWay == DISPLAY_OBJECT_APPROACH_WAY_TICK_SEETHROUGH) {
            //         m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_EXIT_DOUBLE_TAP);
            //     }
            //     m_DisplayObjectApproachWay = HIDE_OBJECT_APPROACH;
        }
    });
}

void SystemUX::handleBoundaryCollision(const CollisionResult result) {
    BoundaryFrameStatus boundaryFrameStatus;
    boundaryFrameStatus.boundaryId = result.boundaryId;
    boundaryFrameStatus.isOut = result.isOut;
    boundaryFrameStatus.isCollisionOccurred = result.distance < m_EffectDistance || result.isOut;
    boundaryFrameStatus.headToBoundaryDistance = result.distance;
    boundaryFrameStatus.effectType = m_BoundaryEffectType;
    boundaryFrameStatus.blendRate = 0.0f;
    auto boundaryWeak = m_BoundaryFactory->GetWeakLocalBoundary().lock();
    boundaryFrameStatus.boundaryType = boundaryWeak->GetBoundaryType();
    if (boundaryFrameStatus.boundaryType == BOUNDAY_TYPE_GAME) {
        auto posForCD = boundaryWeak->GetPosForCD();
        auto lengthOfEachPosToOrigin = boundaryWeak->GetLengthOfEachPosToOrigin();
        YVRMath::vec2 pos1 = posForCD[(result.ID + 1) % (posForCD.size())];
        YVRMath::vec2 pos2 = YVRMath::vec2(result.closestPosition.x, result.closestPosition.z);
        float disOnCD = lengthOfEachPosToOrigin[result.ID] - YVRMath::distance(pos1, pos2);
        boundaryFrameStatus.blendRate = 1.0f;
        boundaryFrameStatus.headCDUV = YVRMath::vec4(disOnCD, result.closestPosition.y, result.distance, m_EffectDistance);
        boundaryFrameStatus.boundaryParams = YVRMath::vec4(lengthOfEachPosToOrigin.back(), 3.0f, 0.0, boundaryWeak->GetRadius());
    }
    updateBoundaryFrameStatus(boundaryFrameStatus);
}

void SystemUX::createLocalBoundary() {
    float radius = m_SystemUXJava->GetLocalBoundaryRadius();
    FOO_LOG("createLocalBoundary radius %f", radius);
    m_BoundaryController->CreateLocalBoundary(radius);
}

void SystemUX::resumeBoundary() {
    m_ThreadPool.detach_task([this]() {
        m_IsBoundaryAlive = true;
        if (m_InLargeSpaceMode) {
            createGameBoundary();
        } else {
            createLocalBoundary();
        }
    });
}

void SystemUX::handleDeviceStatusChange(const int status) {
    m_StateFlow->UpdateState(status);
    // m_DeviceStatus = status;
}
float SystemUX::GetOriginBoundaryRadius() { return m_SystemUXJava->GetLocalBoundaryRadius(); }
void SystemUX::SetOriginBoundaryRadius(float radius) {
    if (m_InLargeSpaceMode) {
        FOO_LOG("[SystemUX] skip SetOriginBoundaryRadius while in largespace mode");
        return;
    }
    m_SystemUXJava->SetLocalBoundaryRadius(radius);

    if (m_BoundaryFrameStatus.boundaryId != UINT64_MAX && m_IsBoundaryAlive) {
        m_BoundaryFactory->ClearAllBoundary();
        m_RenderEngine->pauseBoundary();
        resumeBoundary();
    }
}

void SystemUX::ShowGazeImage(bool bShow) { m_SystemUXJava->ShowGazeImage(bShow); }

void SystemUX::recenter() {
    if (m_InLargeSpaceMode) {
        FOO_LOG("[SystemUX] skip recenter while in largespace mode");
        return;
    }
    if (m_BoundaryFrameStatus.boundaryId != UINT64_MAX && m_IsBoundaryAlive) {
        m_BoundaryFactory->ClearAllBoundary();
        m_RenderEngine->pauseBoundary();
        resumeBoundary();
    } else {
        FOO_LOG("skip recenter boundaryId %llu isOut %d", m_BoundaryFrameStatus.boundaryId, m_BoundaryFrameStatus.isOut);
    }
}

void SystemUX::handlePerformanceUpdate(pid_t pid, int64_t rss_kb, int64_t pss_kb, float cpu_usage) {
    static const float cpuThreshold = 20.0f;
    static const float memThreshold = 500000;  // 500M
    std::string identify;
    std::string summary;

    bool isWearing = m_SystemUXJava->InWearingDetection();
    if (cpu_usage > cpuThreshold) {
        identify = "guardian high cpu usage: ";
        summary = identify + std::to_string(cpu_usage) + "%";
        summary += isWearing ? " in wearing" : " not in wearing\n";
        summary += yvrutils::getTopCpuThreadListInfo(pid);
        FOO_WARN("%s", summary.c_str());
        WPLOG_GUARDIAN_PERF(identify, summary, true);
    } else if (pss_kb > memThreshold || rss_kb > memThreshold) {
        identify = "guardian high memory usage: ";
        summary = identify + "pss: " + std::to_string(pss_kb) + "KB rss: " + std::to_string(rss_kb) + "KB";
        summary += isWearing ? " in wearing" : " not in wearing";
        FOO_WARN("%s", summary.c_str());
        WPLOG_GUARDIAN_PERF(identify, summary, true);
    }
}

void SystemUX::onHardWareDamage(const int32_t damageMask) {
    m_ThreadPool.detach_task([this, damageMask]() {
        m_IsHardWareDamage = true;
        m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_HARDWARE_DAMAGE);
    });
}

void SystemUX::onNearestObjectUpdate(const float distance) {
    m_ThreadPool.detach_task([this, distance]() {
        if (m_NearestObjectEnable) {
            m_NearestObjectDistance = distance;
        }
    });
}

// running in m_threadPool
void SystemUX::onNearestObjectEnableChange(bool enable) {
    m_NearestObjectEnable = enable;
    TRACKINGAPI_SetMeshNearestObjectEnable(enable);
    if (!enable) {
        m_NearestObjectDistance = 100.0f;
        if (m_DisplayObjectApproachWay == DISPLAY_OBJECT_APPROACH_WAY_SHOW_TOAST) {
            m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_HIDE_CLOSE_TO_OBJECTS_TOAST);
        } else if (m_DisplayObjectApproachWay == DISPLAY_OBJECT_APPROACH_WAY_TICK_SEETHROUGH) {
            m_SystemUXJava->onMotionChange(yvrMotionType::MOTION_TYPE_EXIT_DOUBLE_TAP);
        }
        m_DisplayObjectApproachWay = HIDE_OBJECT_APPROACH;
    }
}

void SystemUX::SetBoundaryData(int dataLength, float in[512]) {
    FOO_LOG("SetBoundaryData %d", dataLength);
    // TODO ent or toc
    TRACKINGAPI_SetLargeSpaceBoundaryData(dataLength, in);
}

int32_t SystemUX::GetBoundaryEffectDistance(float* outDistance) {
    float distance = m_SystemUXJava->GetBoundaryEffectDistance();
    FOO_LOG("[SystemUX] GetBoundaryEffectDistance %f", distance);
    *outDistance = distance;
    return 0;
}
int32_t SystemUX::SetBoundaryEffectDistance(float distance) {
    FOO_LOG("[SystemUX] SetBoundaryEffectDistance %f", distance);
    m_EffectDistance = distance;
    m_SystemUXJava->SetBoundaryEffectDistance(distance);
    return 0;
}
int32_t SystemUX::GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) {
    int typeJava = m_SystemUXJava->GetBoundaryOutEffectType();
    FOO_LOG("[SystemUX] GetBoundaryOutEffectType %d", typeJava);
    *type = (BoundarOutEffectTypeGA)typeJava;
    return 0;
}
int32_t SystemUX::SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) {
    FOO_LOG("[SystemUX] SetBoundaryOutEffectType %d", type);
    m_SystemUXJava->SetBoundaryOutEffectType((int)type);
    m_BoundaryEffectType = type;
    return 0;
}

SystemUX::SystemUX()
    : m_EffectDistance(0.3),
      m_BoundaryEffectType(BoundarOutEffectTypeGA::EFFECT_VST_GRID),
      m_PerformanceObserverId({-1, [this](pid_t pid, int64_t rss_kb, int64_t pss_kb, float cpu_usage) { handlePerformanceUpdate(pid, rss_kb, pss_kb, cpu_usage); }}) {}

SystemUX::~SystemUX() {
    m_ThreadPool.purge();
    m_ThreadPool.wait();
}

}  // namespace systemux
}  // namespace yvr
