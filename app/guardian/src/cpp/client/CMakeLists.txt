set(SRC_FILES SystemUXClient.cpp GuardianAPI_API.cpp)

add_library(systemuxclient SHARED ${SRC_FILES})

find_package(GuardianHelper 1.0.0 REQUIRED)
target_link_libraries(systemuxclient GuardianHelper::GuardianHelper)
target_include_directories(systemuxclient PRIVATE ${GuardianHelper_INCLUDE_DIRS})

target_link_libraries(
	systemuxclient
	aosp_utils
	aosp_cutils
	log
	android
	systemuxcommon
	${BINDER_LIBRARY}
	${BASE_LIBRARY}
	${UTILS_LIBRARY}
	${CUTILS_LIBRARY}
	${OPENXR_LOADER_LIBRARY}
	)

target_include_directories(systemuxclient PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/include)
