#include "guardianapi/GuardianAPI_capi.h"
#include <stdlib.h>
#include "SystemUXClient.h"
#include "yvrutils/DynamicLog.h"

#define __FILENAME__ "GuardianAPI_API.cpp"

#define GUARDIANAPI_API __attribute__((visibility("default")))

#define STRINGIZE2(a) #a
#define STRINGIZE(a) STRINGIZE2(a)

#define CONCAT2(a, b) a##b
#define CONCAT(a, b) CONCAT2(a, b)

GUARDIANAPI_API_1_0_0 api_1_0_0{};

#define GA_INST() (yvr::systemux::SystemUXClient::getInstance())

void GetAPIVersion_1_0_0(int* major, int* minor, int* patch) {
    if (major) *major = 1;
    if (minor) *minor = 0;
    if (patch) *patch = 1;
}

int32_t TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* out) { return GA_INST()->TestBoundaryNode(deviceNode, out); }

int32_t TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* out) { return GA_INST()->TestBoundaryPoint(point, out); }

bool GetBoundaryDimensions(BoundaryDimensionsGA* outDimensions) { return GA_INST()->GetBoundaryDimensions(*outDimensions); }

bool GetBoundaryVisible(BoundaryVisibleControlGA* outControl) { return GA_INST()->GetBoundaryVisible(*outControl); }

bool SetBoundaryVisible(const BoundaryVisibleControlGA control) { return GA_INST()->SetBoundaryVisible(control); }

int32_t GetBoundaryGeometryPointsCount() { return GA_INST()->GetBoundaryGeometryPointsCount(); }

int32_t GetBoundaryGeometry(yvrVector3f* geometry) { return GA_INST()->GetBoundaryGeometry(geometry); }

float GetGroundDistance() { return GA_INST()->GetGroundDistance(); }

bool GetBoundaryConfigured() { return GA_INST()->GetBoundaryConfigured(); }

int32_t SetBoundaryData(int dataLength, float in[512]) { return GA_INST()->SetBoundaryData(dataLength, in); }

int32_t SetGroundDistance(float distance) { return GA_INST()->SetGroundDistance(distance); }

int32_t ClearBoundaryData() { return GA_INST()->ClearBoundaryData(); }

int32_t SetBoundaryType(BoundaryTypeGA type) { return GA_INST()->SetBoundaryType(type); }

int32_t GetBoundaryType(BoundaryTypeGA* out) { return GA_INST()->GetBoundaryType(*out); }

bool IsGameBoundaryDataValid() { return GA_INST()->GetBoundaryGameTypeDataStatus(); }

void OnGuardianFinish() { GA_INST()->OnGuardianFinish(); }

void OnGuardianEnter() { GA_INST()->OnGuardianEnter(); }

int32_t AdoptBoundarySetting(const BoundaryTypeGA type, bool adopt) { return GA_INST()->SetThisBoundaryTypeVisible(type, adopt); }

int32_t SetBoundaryShowStyle(const BoundaryShowStyleGA showStyle) { return GA_INST()->SetBoundaryShowStyle(showStyle); }

int32_t SetLocalBoundarySize(const LocalBoundaryRadiusTypeGA radiusType) { return GA_INST()->SetLocalBoundarySize(radiusType); }

int32_t GetLocalBoundarySize(LocalBoundaryRadiusTypeGA* outRadiusType, float* outRadiusValue) { return GA_INST()->GetLocalBoundarySize(*outRadiusType, *outRadiusValue); }

int32_t GetBoundaryData(uint64_t* outBid, int* outLength, float* outPoints) {
    // return GA_INST()->GetBoundaryData(outBid, outLength, outPoints);

    return 0;
}

int32_t GetOriginBoundaryRadius(float* outRadius) { return GA_INST()->GetOriginBoundaryRadius(outRadius); }
int32_t SetOriginBoundaryRadius(float inRadius) { return GA_INST()->SetOriginBoundaryRadius(inRadius); }

int32_t ShowGazeImage(bool bShow) { return GA_INST()->ShowGazeImage(bShow); }

int32_t GetBoundaryEffectDistance(float* outDistance) { return GA_INST()->GetBoundaryEffectDistance(outDistance); }
int32_t SetBoundaryEffectDistance(float distance) { return GA_INST()->SetBoundaryEffectDistance(distance); }
int32_t GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) { return GA_INST()->GetBoundaryOutEffectType(type); }
int32_t SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) { return GA_INST()->SetBoundaryOutEffectType(type); }

void Init_1_0_0() {
    GUARDIANAPI_API_1_0_0& api = api_1_0_0;
    api.GetAPIVersion = &GetAPIVersion_1_0_0;
    api.TestBoundaryNode = &TestBoundaryNode;
    api.TestBoundaryPoint = &TestBoundaryPoint;
    api.GetBoundaryDimensions = &GetBoundaryDimensions;
    api.GetBoundaryVisible = &GetBoundaryVisible;
    api.GetBoundaryGeometryPointsCount = &GetBoundaryGeometryPointsCount;
    api.GetBoundaryGeometry = &GetBoundaryGeometry;
    api.GetGroundDistance = &GetGroundDistance;
    api.GetBoundaryConfigured = &GetBoundaryConfigured;
    api.SetBoundaryData = &SetBoundaryData;
    api.SetGroundDistance = &SetGroundDistance;
    api.ClearBoundaryData = &ClearBoundaryData;
    api.SetBoundaryType = &SetBoundaryType;
    api.GetBoundaryType = &GetBoundaryType;
    api.IsGameBoundaryDataValid = &IsGameBoundaryDataValid;
    api.OnGuardianFinish = &OnGuardianFinish;
    api.OnGuardianEnter = &OnGuardianEnter;
    api.SetBoundaryVisible = &SetBoundaryVisible;
    api.AdoptBoundarySetting = &AdoptBoundarySetting;
    api.SetBoundaryShowStyle = &SetBoundaryShowStyle;
    api.SetLocalBoundarySize = &SetLocalBoundarySize;
    api.GetLocalBoundarySize = &GetLocalBoundarySize;
    api.GetBoundaryData = &GetBoundaryData;
    api.GetOriginalBoundaryRadius = &GetOriginBoundaryRadius;
    api.SetOriginalBounaryRadius = &SetOriginBoundaryRadius;
    api.ShowGazeImage = &ShowGazeImage;
    api.GetBoundaryEffectDistance = &GetBoundaryEffectDistance;
    api.SetBoundaryEffectDistance = &SetBoundaryEffectDistance;
    api.SetBoundaryOutEffectType = &SetBoundaryOutEffectType;
    api.GetBoundaryOutEffectType = &GetBoundaryOutEffectType;
}

extern "C" GUARDIANAPI_API int32_t GUARDIANAPI_GetAPI(GUARDIANAPI_Version version, void** outAPIPointers) {
    int ret = 0;
    int major = 0, minor = 0, patch = 0;
    std::string supportedVersions = "";

    if (outAPIPointers == NULL) {
        YLOGE("Invalid call to GUARDIANAPI_GetAPI with NULL outAPIPointers");
        return 0;
    }

#define API_VERSION_HANDLE(enumver, actualver)                         \
    supportedVersions += " " STRINGIZE(CONCAT(API_, enumver));          \
    if (version == CONCAT(eGUARDIANAPI_API_Version_, enumver)) {       \
        CONCAT(Init_, actualver)();                                    \
        *outAPIPointers = &CONCAT(api_, actualver);                    \
        CONCAT(api_, actualver).GetAPIVersion(&major, &minor, &patch); \
        ret = 1;                                                       \
    }

    API_VERSION_HANDLE(1_0_0, 1_0_0);

#undef API_VERSION_HANDLE

    if (ret) {
        YLOGI("Initialising GuardianAPI API version %d.%d.%d for requested version %d", major, minor, patch, version);
        return 1;
    }

    YLOGE("Unrecognised API version '%d'. Supported versions:%s", version, supportedVersions.c_str());

    return 0;
}
