#pragma once

#include <utils/Singleton.h>
#include <utils/String8.h>
#include <ISystemUXClient.h>
#include <ISystemUXService.h>

using namespace android;

namespace yvr {
namespace systemux {

class SystemUXClient : public IBinder::DeathRecipient {
 public:
    static sp<SystemUXClient> getInstance();
    SystemUXClient();
    ~SystemUXClient();

    int SetBoundaryData(int dataLength, float in[512]);
    int ShowBoundaryData(int dataLength, float in[512]);
    int SetGroundDistance(float distance);
    float GetGroundDistance();
    int TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* result);
    int TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* result);
    bool GetBoundaryDimensions(BoundaryDimensionsGA& dimensions);
    bool GetBoundaryVisible(BoundaryVisibleControlGA& control);
    bool SetBoundaryVisible(BoundaryVisibleControlGA control);
    int GetBoundaryGeometryPointsCount();
    int GetBoundaryGeometry(yvrVector3f* geometry);
    int ClearBoundaryData();
    int SetBoundaryType(BoundaryTypeGA type);
    int GetBoundaryType(BoundaryTypeGA& type);
    bool GetBoundaryGameTypeDataStatus();
    int OnGuardianFinish();
    int OnGuardianEnter();
    int SetThisBoundaryTypeVisible(BoundaryTypeGA type, bool quitWith);
    bool GetBoundaryConfigured();
    int SetBoundaryShowStyle(BoundaryShowStyleGA showStyle);
    int SetLocalBoundarySize(LocalBoundaryRadiusTypeGA radiusType);
    int GetLocalBoundarySize(LocalBoundaryRadiusTypeGA& radiusType, float& radiusValue);
    int32_t GetOriginBoundaryRadius(float* outRadius);
    int32_t SetOriginBoundaryRadius(float inRadius);
    // void connectGuardianService();
    int32_t ShowGazeImage(bool bShow);
    int32_t GetBoundaryEffectDistance(float* outDistance);
    int32_t SetBoundaryEffectDistance(float distance);
    int32_t GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type);
    int32_t SetBoundaryOutEffectType(BoundarOutEffectTypeGA type);

 private:
    void binderDied(const wp<IBinder>& /*who*/) override;
    android::sp<ISystemUXService> service();
    android::sp<ISystemUXClient> client();

 private:
    android::sp<ISystemUXService> m_SystemUXService;
    android::sp<ISystemUXClient> m_SystemUXClient;
};
}  // namespace systemux
}  // namespace yvr