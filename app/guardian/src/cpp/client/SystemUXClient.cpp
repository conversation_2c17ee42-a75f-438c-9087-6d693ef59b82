
#include "SystemUXClient.h"
#include "yvrutils/DynamicLog.h"
#include <binder/IServiceManager.h>

#define IPC_CLIENT(expr)                                            \
    do {                                                            \
        if (!client()) {                                            \
            YLOGE("call %s fail, ipc not connected", __FUNCTION__); \
            return -1;                                              \
        }                                                           \
        if (int err = client()->expr; err != 0) {                   \
            YLOGE("call %s fail, error: %d", __FUNCTION__, err);    \
            return err;                                             \
        }                                                           \
    } while (0)

namespace yvr {
namespace systemux {

sp<SystemUXClient> SystemUXClient::getInstance() {
    static sp<SystemUXClient> instance = new SystemUXClient();
    return instance;
}

int SystemUXClient::SetBoundaryData(int dataLength, float in[512]) {
    IPC_CLIENT(SetBoundaryData(dataLength, in));
    return 0;
}
int SystemUXClient::ShowBoundaryData(int dataLength, float in[512]) {
    IPC_CLIENT(ShowBoundaryData(dataLength, in));
    return 0;
}
int SystemUXClient::SetGroundDistance(float distance) {
    IPC_CLIENT(SetGroundDistance(distance));
    return 0;
}
float SystemUXClient::GetGroundDistance() {
    IPC_CLIENT(GetGroundDistance());
    return 0.0f;
}
int SystemUXClient::TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* result) {
    IPC_CLIENT(TestBoundaryNode(deviceNode, result));
    return 0;
}
int SystemUXClient::TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* result) {
    IPC_CLIENT(TestBoundaryPoint(point, result));
    return 0;
}
bool SystemUXClient::GetBoundaryDimensions(BoundaryDimensionsGA& dimensions) {
    IPC_CLIENT(GetBoundaryDimensions(dimensions));
    return 0;
}
bool SystemUXClient::GetBoundaryVisible(BoundaryVisibleControlGA& control) {
    IPC_CLIENT(GetBoundaryVisible(control));
    return 0;
}
bool SystemUXClient::SetBoundaryVisible(BoundaryVisibleControlGA control) {
    IPC_CLIENT(SetBoundaryVisible(control));
    return 0;
}
int SystemUXClient::GetBoundaryGeometryPointsCount() {
    IPC_CLIENT(GetBoundaryGeometryPointsCount());
    return 0;
}
int SystemUXClient::GetBoundaryGeometry(yvrVector3f* geometry) {
    IPC_CLIENT(GetBoundaryGeometry(geometry));
    return 0;
}
int SystemUXClient::ClearBoundaryData() {
    IPC_CLIENT(ClearBoundaryData());
    return 0;
}
int SystemUXClient::SetBoundaryType(BoundaryTypeGA type) {
    IPC_CLIENT(SetBoundaryType(type));
    return 0;
}
int SystemUXClient::GetBoundaryType(BoundaryTypeGA& type) {
    IPC_CLIENT(GetBoundaryType(type));
    return 0;
}
bool SystemUXClient::GetBoundaryGameTypeDataStatus() {
    IPC_CLIENT(GetBoundaryGameTypeDataStatus());
    return 0;
}
int SystemUXClient::OnGuardianFinish() {
    IPC_CLIENT(OnGuardianFinish());
    return 0;
}
int SystemUXClient::OnGuardianEnter() {
    IPC_CLIENT(OnGuardianEnter());
    return 0;
}
int SystemUXClient::SetThisBoundaryTypeVisible(BoundaryTypeGA type, bool quitWith) {
    IPC_CLIENT(SetThisBoundaryTypeVisible(type, quitWith));
    return 0;
}
bool SystemUXClient::GetBoundaryConfigured() {
    IPC_CLIENT(GetBoundaryConfigured());
    return true;
}
int SystemUXClient::SetBoundaryShowStyle(BoundaryShowStyleGA showStyle) {
    IPC_CLIENT(SetBoundaryShowStyle(showStyle));
    return 0;
}
int SystemUXClient::SetLocalBoundarySize(LocalBoundaryRadiusTypeGA radiusType) {
    IPC_CLIENT(SetLocalBoundarySize(radiusType));
    return 0;
}
int SystemUXClient::GetLocalBoundarySize(LocalBoundaryRadiusTypeGA& radiusType, float& radiusValue) {
    IPC_CLIENT(GetLocalBoundarySize(radiusType, radiusValue));
    return 0;
}

int32_t SystemUXClient::GetOriginBoundaryRadius(float* outRadius) {
    IPC_CLIENT(GetOriginBoundaryRadius(outRadius));
    return 0;
}
int32_t SystemUXClient::SetOriginBoundaryRadius(float inRadius) {
    IPC_CLIENT(SetOriginBoundaryRadius(inRadius));
    return 0;
}
// void SystemUXClient::connectGuardianService() {}

int32_t SystemUXClient::ShowGazeImage(bool bShow) {
    IPC_CLIENT(ShowGazeImage(bShow));
    return 0;
}

int32_t SystemUXClient::GetBoundaryEffectDistance(float* outDistance) {
    IPC_CLIENT(GetBoundaryEffectDistance(outDistance));
    return 0;
}
int32_t SystemUXClient::SetBoundaryEffectDistance(float distance) {
    IPC_CLIENT(SetBoundaryEffectDistance(distance));
    return 0;
}
int32_t SystemUXClient::GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) {
    IPC_CLIENT(GetBoundaryOutEffectType(type));
    return 0;
}
int32_t SystemUXClient::SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) {
    IPC_CLIENT(SetBoundaryOutEffectType(type));
    return 0;
}

android::sp<ISystemUXService> SystemUXClient::service() {
    if (!m_SystemUXService) {
        auto binder = defaultServiceManager()->getService(android::String16(SYSTEMUX_SERVICE_NAME));
        if (!binder) {
            YLOGE(" Failed to get systemux service");
            return nullptr;
        }
        binder->linkToDeath(this);
        m_SystemUXService = interface_cast<ISystemUXService>(binder);
    }

    return m_SystemUXService;
}
android::sp<ISystemUXClient> SystemUXClient::client() {
    if (!m_SystemUXClient) {
        if (service()) {
            m_SystemUXClient = service()->createSystemUXClient();
            if (!m_SystemUXClient) {
                YLOGE(" Failed to create systemux client");
                return nullptr;
            }
        }
    }
    return m_SystemUXClient;
}

void SystemUXClient::binderDied(const wp<IBinder>& /*who*/) {
    m_SystemUXService = nullptr;
    m_SystemUXClient = nullptr;
}

SystemUXClient::SystemUXClient() {}
SystemUXClient::~SystemUXClient() {}
}  // namespace systemux
}  // namespace yvr