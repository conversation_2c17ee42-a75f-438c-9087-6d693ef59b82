#pragma once

#include <binder/Binder.h>
#include <binder/IInterface.h>
#include <binder/SafeInterface.h>
#include <binder/Parcel.h>
#include <string.h>
#include "Guardian_types.h"

using namespace android;

namespace yvr {

namespace systemux {

class ISystemUXClient : public android::IInterface {
    DECLARE_META_INTERFACE(SystemUXClient);

 public:
    enum {
        SET_BOUNDARY_DATA = android::IBinder::FIRST_CALL_TRANSACTION + 0,
        SET_GROUND_DISTANCE,
        GET_GROUND_DISTANCE,
        TEST_BOUNDARY_NODE,
        TEST_BOUNDARY_POINT,
        GET_BOUNDARY_DIMENSIONS,
        GET_BOUNDARY_VISIBLE,
        SET_BOUNDARY_VISIBLE,
        GET_BOUNDARY_GEOMETRY_POINTS_COUNT,
        GET_BOUNDARY_GEOMETRY,
        C<PERSON><PERSON>_BOUNDARY_DATA,
        SET_BOUNDARY_TYPE,
        GET_BOUNDARY_TYPE,
        GET_BOUNDARYGAMETYPEDATA_STATUS,
        SHOW_BOUNDARY_DATA,
        ON_GUARDIAN_FINISH,
        ON_GUARDIAN_ENTER,
        SET_THIS_BOUNDARY_TYPE_VISIBLE,
        // CREATE_CLIENT,
        GET_BOUNDARY_CONFIGURED,
        SET_BOUNDARY_SHOW_STYLE,
        GET_LOCAL_BOUNDARY_SIZE,
        SET_LOCAL_BOUNDARY_SIZE,
        GET_SHMEM_FD,
        GET_ORIGIN_BOUNDARY_RADIUS,
        SET_ORIGIN_BOUNDARY_RADIUS,
        SHOW_DEBUG_IMAGE,
        GET_BOUNDARY_EFFECT_DISTANCE,
        SET_BOUNDARY_EFFECT_DISTANCE,
        GET_BOUNDARY_OUT_EFFECT,
        SET_BOUNDARY_OUT_EFFECT
    };
    virtual int SetBoundaryData(int dataLength, float in[512]) = 0;
    virtual int ShowBoundaryData(int dataLength, float in[512]) = 0;
    virtual int SetGroundDistance(float distance) = 0;
    virtual float GetGroundDistance() = 0;
    virtual int TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* result) = 0;
    virtual int TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* result) = 0;
    virtual bool GetBoundaryDimensions(BoundaryDimensionsGA& dimensions) = 0;
    virtual bool GetBoundaryVisible(BoundaryVisibleControlGA& control) = 0;
    virtual bool SetBoundaryVisible(BoundaryVisibleControlGA control) = 0;
    virtual int GetBoundaryGeometryPointsCount() = 0;
    virtual int GetBoundaryGeometry(yvrVector3f* geometry) = 0;
    virtual int ClearBoundaryData() = 0;
    virtual int SetBoundaryType(BoundaryTypeGA type) = 0;
    virtual int GetBoundaryType(BoundaryTypeGA& type) = 0;
    virtual bool GetBoundaryGameTypeDataStatus() = 0;
    virtual int OnGuardianFinish() = 0;
    virtual int OnGuardianEnter() = 0;
    virtual int SetThisBoundaryTypeVisible(BoundaryTypeGA type, bool quitWith) = 0;
    virtual bool GetBoundaryConfigured() = 0;
    virtual int SetBoundaryShowStyle(BoundaryShowStyleGA showStyle) = 0;
    virtual int SetLocalBoundarySize(LocalBoundaryRadiusTypeGA radiusType) = 0;
    virtual int GetLocalBoundarySize(LocalBoundaryRadiusTypeGA& radiusType, float& radiusValue) = 0;

    virtual int32_t GetOriginBoundaryRadius(float* outRadius) = 0;
    virtual int32_t SetOriginBoundaryRadius(float inRadius) = 0;

    virtual int getSharedMemory(int& fd) = 0;

    virtual int32_t ShowGazeImage(bool bShow) = 0;
    virtual int GetBoundaryEffectDistance(float* outDistance) = 0;
    virtual int SetBoundaryEffectDistance(float distance) = 0;

    virtual int32_t GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) = 0;
    virtual int32_t SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) = 0;
};

class BnSystemUXClient : public android::SafeBnInterface<ISystemUXClient> {
 public:
    BnSystemUXClient() : SafeBnInterface<ISystemUXClient>("BnSystemUXClient") {}
    android::status_t onTransact(uint32_t code, const android::Parcel& data, android::Parcel* reply, uint32_t flags = 0) override;
};

class BpSystemUXClient : public android::SafeBpInterface<ISystemUXClient> {
 public:
    explicit BpSystemUXClient(const android::sp<android::IBinder>& impl) : android::SafeBpInterface<ISystemUXClient>(impl, "BpSystemUXClient") {}
    int getSharedMemory(int& fd) override;
    int SetBoundaryData(int dataLength, float in[512]) override;
    int ShowBoundaryData(int dataLength, float in[512]) override;
    int SetGroundDistance(float distance) override;
    float GetGroundDistance() override;
    int TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* result) override;
    int TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* result) override;
    bool GetBoundaryDimensions(BoundaryDimensionsGA& dimensions) override;
    bool GetBoundaryVisible(BoundaryVisibleControlGA& control) override;
    bool SetBoundaryVisible(BoundaryVisibleControlGA control) override;
    int GetBoundaryGeometryPointsCount() override;
    int GetBoundaryGeometry(yvrVector3f* geometry) override;
    int ClearBoundaryData() override;
    int SetBoundaryType(BoundaryTypeGA type) override;
    int GetBoundaryType(BoundaryTypeGA& type) override;
    bool GetBoundaryGameTypeDataStatus() override;
    int OnGuardianFinish() override;
    int OnGuardianEnter() override;
    int SetThisBoundaryTypeVisible(BoundaryTypeGA type, bool quitWith) override;
    // int createClient(android::sp<android::IBinder>& clientToken, android::String16& processName) override;
    bool GetBoundaryConfigured() override;
    int SetBoundaryShowStyle(BoundaryShowStyleGA showStyle) override;
    int SetLocalBoundarySize(LocalBoundaryRadiusTypeGA radiusType) override;
    int GetLocalBoundarySize(LocalBoundaryRadiusTypeGA& radiusType, float& radiusValue) override;

    int32_t GetOriginBoundaryRadius(float* outRadius) override;
    int32_t SetOriginBoundaryRadius(float inRadius) override;

    int32_t ShowGazeImage(bool bShow) override;
    int GetBoundaryEffectDistance(float* outDistance) override;
    int SetBoundaryEffectDistance(float distance) override;

    int32_t GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) override;
    int32_t SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) override;
};

}  // namespace systemux
}  // namespace yvr
