#include "ISystemUXService.h"
#include "yvrutils/DynamicLog.h"

namespace yvr {

namespace systemux {

IMPLEMENT_META_INTERFACE(SystemUXService, SYSTEMUX_SERVICE_NAME);
android::status_t BnSystemUXService::onTransact(uint32_t code, const android::Parcel& data, android::Parcel* reply, uint32_t flags) {
    switch (code) {
        case CREATE_SYSTEMUX_CLIENT: {
            CHECK_INTERFACE(ISystemUXService, data, reply);
            auto client = createSystemUXClient();
            reply->writeStrongBinder(ISystemUXClient::asBinder(client));
            return android::NO_ERROR;
        }
        default:
            return BBinder::onTransact(code, data, reply, flags);
    }
    return android::NO_ERROR;
}

android::sp<ISystemUXClient> BpSystemUXService::createSystemUXClient() {
    android::Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    remote()->transact(CREATE_SYSTEMUX_CLIENT, data, &reply);
    auto client = reply.readStrongBinder();
    return ISystemUXClient::asInterface(client);
}
}  // namespace systemux
}  // namespace yvr
