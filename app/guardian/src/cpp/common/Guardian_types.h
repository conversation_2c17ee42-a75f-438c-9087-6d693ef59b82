
#ifndef GUARDIAN_TYPES_H
#define GUARDIAN_TYPES_H

#include "YVRDefsBase.h"
#include <stdbool.h>

/**
 * @brief: 设备节点
 *
 */
typedef enum DeviceNodeGA_ { LeftController_GA = 0, RightController_GA, Hmd_GA, LeftEye_GA, RightEye_GA, NodeCount_GA } DeviceNodeGA;

/**
 * @brief: 某个点的边界检测结果
 *
 */
typedef struct BoundaryTestResultGA_ {
    bool IsTriggering;               // 是否在边界外
    float ClosestDistance;           // 测试点离边界最近距离
    yvrVector3f ClosestPoint;        // ClosestDistance所在的点
    yvrVector3f ClosestPointNormal;  // ClosestPoint点的法线
} BoundaryTestResultGA;

/**
 * @brief: 边界点
 *
 */
typedef struct BoundaryPointGA_ {
    yvrVector3f point;
} BoundaryPointGA;

/**
 * @brief: 边界空间大小
 *
 */
typedef struct BoundaryDimensionsGA_ {
    yvrVector3f dimensions;
} BoundaryDimensionsGA;

/**
 * @brief: 边界类型
 *
 */
typedef enum BoundaryTypeGA_ {
    BOUNDARY_TYPE_LOCAL_GA = 0,  // 原地边界
    BOUNDARY_TYPE_GAME_GA = 1,   // 游戏边界
    BOUNDARY_TYPE_ALL = 0xffff,
} BoundaryTypeGA;

/**
 * @brief: 边界网格样式
 *
 */
typedef enum BoundaryShowStyleGA_ {
    BOUNDARY_SHOW_STYLE_NORMAL_GA = 0,  // 正常显示
    BOUNDARY_SHOW_STYLE_NO_INSIDE_GA,   // 在边界内不显示网格，只在边界外显示
    BOUNDARY_SHOW_STYLE_SCONFIG_GA,     // 特殊配置触碰到边界显示水波纹（未用到）
} BoundaryShowStyleGA;

/**
 * @brief: 边界网格显示规则
 *
 */
typedef enum BoundaryVisibleControlGA_ {
    BOUNDARY_VISIBLE_NORMAL_GA = 0,     // 边界网格根据边界检测结果显示，在边界内不显示，触碰边界显示
    BOUNDARY_FORCE_VISIBLE_GA = 1,      // 强制显示边界网格
    BOUNDARY_FORCE_NON_VISIBLE_GA = 2,  // 边界网格强制不显示
    BOUNDARY_DISABLE_DETCTION_GA = 3,   // 暂停边界检测，并且不显示网格
} BoundaryVisibleControlGA;

/**
 * @brief: 原地边界半径大小
 *
 */
typedef enum LocalBoundaryRadiusTypeGA_ {
    LOCAL_BOUNDARY_RADIUS_SMALL_GA = 0,  // 一米
    LOCAL_BOUNDARY_RADIUS_MEDIUM_GA,     // 1.2米
    LOCAL_BOUNDARY_RADIUS_LARGE_GA,      // 1.5米
} LocalBoundaryRadiusTypeGA;

typedef enum BoundarOutEffectTypeGA_ {
    EFFECT_NONE,
    EFFECT_GRID,
    EFFECT_VST,
    EFFECT_VST_GRID,
} BoundarOutEffectTypeGA;

/**
 * @brief SystemUXDump类型
 */
using SystemUXFlags_t = uint64_t;
static const SystemUXFlags_t SYSTEMUX_DUMP_ALL = 0xFFFFFFFFFFFFFFFF;
static const SystemUXFlags_t SYSTEMUX_DUMP_CREATE_LOCAL_BOUNDARY = 1 << 0;

#endif
