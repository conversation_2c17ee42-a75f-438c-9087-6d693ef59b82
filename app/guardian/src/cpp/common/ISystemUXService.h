#pragma once

#include <binder/IInterface.h>
#include <binder/Binder.h>
#include <binder/SafeInterface.h>
#include "ISystemUXClient.h"
#include <binder/Parcel.h>

#define SYSTEMUX_SERVICE_NAME "systemuxservice_native"
using namespace android;

namespace yvr {

namespace systemux {
class ISystemUXService : public android::IInterface {
    DECLARE_META_INTERFACE(SystemUXService);

 public:
    enum {
        CREATE_SYSTEMUX_CLIENT = android::IBinder::FIRST_CALL_TRANSACTION + 0,
    };
    virtual android::sp<ISystemUXClient> createSystemUXClient() = 0;
};

class BnSystemUXService : public android::SafeBnInterface<ISystemUXService> {
 public:
    BnSystemUXService() : android::SafeBnInterface<ISystemUXService>("BnSystemUXService") {}
    android::status_t onTransact(uint32_t code, const android::Parcel& data, android::Parcel* reply, uint32_t flags = 0) override;
};

class BpSystemUXService : public android::SafeBpInterface<ISystemUXService> {
 public:
    explicit BpSystemUXService(const android::sp<android::IBinder>& impl) : android::SafeBpInterface<ISystemUXService>(impl, "BpSystemUXService") {}
    android::sp<ISystemUXClient> createSystemUXClient() override;
};

}  // namespace systemux
}  // namespace yvr
