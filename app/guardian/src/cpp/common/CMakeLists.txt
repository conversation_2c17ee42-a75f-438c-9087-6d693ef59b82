set(SRC_FILES ISystemUXService.cpp ISystemUXClient.cpp)

add_library(systemuxcommon STATIC ${SRC_FILES})

# set(CMAKE_PREFIX_PATH "/Users/<USER>/workspace/utils/export/install")
find_package(TrackingHelper 1.0.0 REQUIRED)
target_link_libraries(systemuxcommon TrackingHelper::TrackingHelper)
target_include_directories(systemuxcommon PRIVATE ${TrackingHelper_INCLUDE_DIRS})

# set(CMAKE_PREFIX_PATH "/Users/<USER>/workspace/utils/yvrutils/install")
find_package(YVRUtils 1.0.0 REQUIRED)

target_include_directories(systemuxcommon PUBLIC ${CMAKE_CURRENT_SOURCE_DIR})

target_link_libraries(
	systemuxcommon
	aosp_binder
	aosp_base
	aosp_utils
	aosp_cutils
	YVRUtils::YVRUtils
	${CUTILS_LIBRARY}
	${BINDER_LIBRARY}
	)
