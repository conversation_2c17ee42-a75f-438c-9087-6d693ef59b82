#include "ISystemUXClient.h"

#define BOUNDARY_DATA_LENGTH 512

namespace yvr {

namespace systemux {

IMPLEMENT_META_INTERFACE(SystemUXClient, "sytemuxclient");

// int BpSystemUXClient::getSharedMemory(int& fd) { return 0; }

int BpSystemUXClient::SetBoundaryData(int dataLength, float in[512]) {
    int result;

    Parcel data, reply;
    uint32_t totalSize = BOUNDARY_DATA_LENGTH * sizeof(float);

    data.writeInterfaceToken(descriptor);
    data.writeInt32(dataLength);

    data.writeUint32(totalSize);
    android::Parcel::WritableBlob blob;
    result = data.writeBlob(totalSize, false, &blob);
    if (result) {
        return -EINVAL;
    }

    // YLOGI("SetBoundaryData:%d,%p", dataLength, &in[0]);
    memcpy(blob.data(), &in[0], totalSize);
    blob.release();

    remote()->transact(SET_BOUNDARY_DATA, data, &reply);

    reply.readInt32(&result);

    return result;
}

int BpSystemUXClient::ShowBoundaryData(int dataLength, float in[512]) {
    int result;
    Parcel data, reply;
    uint32_t totalSize = BOUNDARY_DATA_LENGTH * sizeof(float);

    data.writeInterfaceToken(descriptor);
    data.writeInt32(dataLength);

    data.writeUint32(totalSize);
    android::Parcel::WritableBlob blob;
    result = data.writeBlob(totalSize, false, &blob);
    if (result) {
        return -EINVAL;
    }
    memcpy(blob.data(), &in[0], totalSize);
    blob.release();

    remote()->transact(SHOW_BOUNDARY_DATA, data, &reply);

    reply.readInt32(&result);

    return result;
}

int BpSystemUXClient::SetGroundDistance(float distance) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeFloat(distance);

    remote()->transact(SET_GROUND_DISTANCE, data, &reply);

    reply.readInt32(&result);

    return result;
}

float BpSystemUXClient::GetGroundDistance() {
    float result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_GROUND_DISTANCE, data, &reply);

    reply.readFloat(&result);

    return result;
}

int BpSystemUXClient::TestBoundaryNode(const DeviceNodeGA deviceNode, BoundaryTestResultGA* result) {
    int rc;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeByte(deviceNode);

    remote()->transact(TEST_BOUNDARY_NODE, data, &reply);
    Parcel::ReadableBlob blob;
    uint32_t blob_size;
    reply.readUint32(&blob_size);
    rc = reply.readBlob(blob_size, &blob);
    if (rc != NO_ERROR) {
        // YLOGE("readblob failed, rc:%d", rc);
        return rc;
    }
    memcpy(result, blob.data(), blob_size);
    blob.release();

    reply.readInt32(&rc);

    return rc;
}

int BpSystemUXClient::TestBoundaryPoint(const BoundaryPointGA point, BoundaryTestResultGA* result) {
    int rc;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeFloat(point.point.x);
    data.writeFloat(point.point.y);
    data.writeFloat(point.point.z);

    remote()->transact(TEST_BOUNDARY_POINT, data, &reply);
    Parcel::ReadableBlob blob;
    uint32_t blob_size;
    reply.readUint32(&blob_size);
    rc = reply.readBlob(blob_size, &blob);
    if (rc != NO_ERROR) {
        // YLOGE("readblob failed, rc:%d", rc);
        return rc;
    }
    memcpy(result, blob.data(), blob_size);
    blob.release();

    reply.readInt32(&rc);

    return rc;
}

bool BpSystemUXClient::GetBoundaryDimensions(BoundaryDimensionsGA& dimensions) {
    int result = 0;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    remote()->transact(GET_BOUNDARY_DIMENSIONS, data, &reply);

    Parcel::ReadableBlob blob;
    uint32_t blob_size;
    reply.readUint32(&blob_size);
    result = reply.readBlob(blob_size, &blob);
    if (result != NO_ERROR) {
        // YLOGE("readblob failed, rc:%d", result);
        return result;
    }
    memcpy(&dimensions, blob.data(), blob_size);
    blob.release();

    return reply.readBool();
}

bool BpSystemUXClient::GetBoundaryVisible(BoundaryVisibleControlGA& control) {
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARY_VISIBLE, data, &reply);
    control = (BoundaryVisibleControlGA)reply.readInt32();

    return reply.readBool();
}

bool BpSystemUXClient::SetBoundaryVisible(BoundaryVisibleControlGA control) {
    bool result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeInt32(control);

    remote()->transact(SET_BOUNDARY_VISIBLE, data, &reply);

    reply.readBool(&result);

    return result;
}

int BpSystemUXClient::ClearBoundaryData() {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(CLEAR_BOUNDARY_DATA, data, &reply);

    reply.readInt32(&result);

    return result;
}

int BpSystemUXClient::SetBoundaryType(BoundaryTypeGA type) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeInt32(type);

    remote()->transact(SET_BOUNDARY_TYPE, data, &reply);

    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::SetBoundaryShowStyle(BoundaryShowStyleGA showStyle) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeInt32(showStyle);

    remote()->transact(SET_BOUNDARY_SHOW_STYLE, data, &reply);

    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::SetThisBoundaryTypeVisible(BoundaryTypeGA type, bool quitWith) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeInt32(type);
    data.writeBool(quitWith);

    remote()->transact(SET_THIS_BOUNDARY_TYPE_VISIBLE, data, &reply);

    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::GetBoundaryType(BoundaryTypeGA& type) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARY_TYPE, data, &reply);

    type = (BoundaryTypeGA)reply.readInt32();
    reply.readInt32(&result);

    return result;
}

int BpSystemUXClient::SetLocalBoundarySize(LocalBoundaryRadiusTypeGA radiusType) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    data.writeInt32(radiusType);
    remote()->transact(SET_LOCAL_BOUNDARY_SIZE, data, &reply);
    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::GetLocalBoundarySize(LocalBoundaryRadiusTypeGA& radiusType, float& radiusValue) {
    int result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);
    remote()->transact(GET_LOCAL_BOUNDARY_SIZE, data, &reply);
    radiusType = (LocalBoundaryRadiusTypeGA)reply.readInt32();
    radiusValue = reply.readFloat();
    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::GetBoundaryGeometryPointsCount() {
    int count = 0;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARY_GEOMETRY_POINTS_COUNT, data, &reply);

    reply.readInt32(&count);

    return count;
}

int BpSystemUXClient::GetBoundaryGeometry(yvrVector3f* geometry) {
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARY_GEOMETRY, data, &reply);

    // read geometry
    Parcel::ReadableBlob blob;
    uint32_t blob_size;
    reply.readUint32(&blob_size);
    int result = reply.readBlob(blob_size, &blob);
    if (result != NO_ERROR) {
        // YLOGE("readblob failed, rc:%d", result);
        return result;
    }
    memcpy(geometry, blob.data(), blob_size);
    blob.release();

    reply.readInt32(&result);

    return result;
}

bool BpSystemUXClient::GetBoundaryGameTypeDataStatus() {
    bool result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARYGAMETYPEDATA_STATUS, data, &reply);

    reply.readBool(&result);

    return result;
}
int BpSystemUXClient::OnGuardianFinish() {
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    remote()->transact(ON_GUARDIAN_FINISH, data, &reply);
    return 0;
}

int BpSystemUXClient::OnGuardianEnter() {
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    remote()->transact(ON_GUARDIAN_ENTER, data, &reply);
    return 0;
}

// int BpSystemUXClient::createClient(sp<IBinder>& clientToken, String16& processName) {
//     int result;
//     Parcel data, reply;

//     data.writeInterfaceToken(descriptor);
//     data.writeStrongBinder(clientToken);
//     data.writeString16(processName);

//     remote()->transact(CREATE_CLIENT, data, &reply);

//     reply.readInt32(&result);

//     return result;
// }

int BpSystemUXClient::getSharedMemory(int& fd) {
    int result;
    Parcel data, reply;

    fd = 0;
    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_SHMEM_FD, data, &reply);

    if (reply.readInt32() == 1) {
        fd = dup(reply.readFileDescriptor());
    }

    reply.readInt32(&result);
    return result;
}

bool BpSystemUXClient::GetBoundaryConfigured() {
    bool result;
    Parcel data, reply;

    data.writeInterfaceToken(descriptor);

    remote()->transact(GET_BOUNDARY_CONFIGURED, data, &reply);

    reply.readBool(&result);

    return result;
}

int32_t BpSystemUXClient::GetOriginBoundaryRadius(float* outRadius) {
    int32_t result;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    remote()->transact(GET_ORIGIN_BOUNDARY_RADIUS, data, &reply);
    reply.readFloat(outRadius);
    reply.readInt32(&result);
    return result;
}
int32_t BpSystemUXClient::SetOriginBoundaryRadius(float inRadius) {
    int32_t result;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    data.writeFloat(inRadius);
    remote()->transact(SET_ORIGIN_BOUNDARY_RADIUS, data, &reply);
    reply.readInt32(&result);
    return result;
}

int32_t BpSystemUXClient::ShowGazeImage(bool bShow) {
    int32_t result;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    data.writeBool(bShow);
    remote()->transact(SHOW_DEBUG_IMAGE, data, &reply);
    reply.readInt32(&result);
    return result;
}

int BpSystemUXClient::GetBoundaryEffectDistance(float* outDistance) {
    status_t status;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    status = remote()->transact(GET_BOUNDARY_EFFECT_DISTANCE, data, &reply);
    *outDistance = reply.readFloat();
    reply.readExceptionCode();
    int32_t ret = reply.readInt32();
    return ret;
}

int BpSystemUXClient::SetBoundaryEffectDistance(float distance) {
    status_t status;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    data.writeFloat(distance);
    status = remote()->transact(SET_BOUNDARY_EFFECT_DISTANCE, data, &reply);
    reply.readExceptionCode();
    int32_t ret = reply.readInt32();
    return ret;
}

int32_t BpSystemUXClient::GetBoundaryOutEffectType(BoundarOutEffectTypeGA* type) {
    status_t status;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    // data.writeFloat(distance);
    status = remote()->transact(GET_BOUNDARY_OUT_EFFECT, data, &reply);
    int32_t retType = reply.readInt32();
    *type = (BoundarOutEffectTypeGA)retType;
    reply.readExceptionCode();
    int32_t ret = reply.readInt32();
    return ret;
}
int32_t BpSystemUXClient::SetBoundaryOutEffectType(BoundarOutEffectTypeGA type) {
    status_t status;
    Parcel data, reply;
    data.writeInterfaceToken(descriptor);
    data.writeInt32(type);
    status = remote()->transact(SET_BOUNDARY_OUT_EFFECT, data, &reply);
    reply.readExceptionCode();
    int32_t ret = reply.readInt32();
    return ret;
}

status_t BnSystemUXClient::onTransact(uint32_t code, const Parcel& data, Parcel* reply, uint32_t flags) {
    int32_t rc = -EINVAL;
    switch (code) {
        case SET_BOUNDARY_DATA: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float in[BOUNDARY_DATA_LENGTH];
            int dataLength = data.readInt32();
            if (dataLength > BOUNDARY_DATA_LENGTH) {
                dataLength = BOUNDARY_DATA_LENGTH;
            }
            uint32_t totalSize = data.readUint32();

            android::Parcel::ReadableBlob blob;
            status_t status = data.readBlob(totalSize, &blob);
            if (status) {
                return -EINVAL;
            }

            memcpy(&in[0], blob.data(), totalSize);
            int result = SetBoundaryData(dataLength, in);
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case SHOW_BOUNDARY_DATA: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float in[BOUNDARY_DATA_LENGTH];
            int dataLength = data.readInt32();
            if (dataLength > BOUNDARY_DATA_LENGTH) {
                dataLength = BOUNDARY_DATA_LENGTH;
            }
            uint32_t totalSize = data.readUint32();

            android::Parcel::ReadableBlob blob;
            status_t status = data.readBlob(totalSize, &blob);
            if (status) {
                return -EINVAL;
            }

            memcpy(&in[0], blob.data(), totalSize);
            int result = ShowBoundaryData(dataLength, in);
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case SET_GROUND_DISTANCE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float distance = data.readFloat();
            int result = SetGroundDistance(distance);
            reply->writeFloat(result);
            return NO_ERROR;
        }
        case GET_GROUND_DISTANCE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float result = GetGroundDistance();
            reply->writeFloat(result);
            return NO_ERROR;
        }
        case TEST_BOUNDARY_NODE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            DeviceNodeGA node = (DeviceNodeGA)data.readByte();
            BoundaryTestResultGA result;
            uint32_t param_size = sizeof(BoundaryTestResultGA);
            Parcel::WritableBlob blob;
            int rc = TestBoundaryNode(node, &result);

            reply->writeUint32(param_size);
            status_t status = reply->writeBlob(param_size, false, &blob);
            if (status) {
                // YLOGE("writeblob failed rc:%d", status);
                return status;
            }
            memcpy(blob.data(), &result, param_size);
            reply->writeInt32(rc);
            return NO_ERROR;
        }
        case TEST_BOUNDARY_POINT: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryPointGA point;
            point.point.x = data.readFloat();
            point.point.y = data.readFloat();
            point.point.z = data.readFloat();
            BoundaryTestResultGA result;
            uint32_t param_size = sizeof(BoundaryTestResultGA);
            Parcel::WritableBlob blob;
            int rc = TestBoundaryPoint(point, &result);

            reply->writeUint32(param_size);
            status_t status = reply->writeBlob(param_size, false, &blob);
            if (status) {
                // YLOGE("writeblob failed rc:%d", status);
                return status;
            }
            memcpy(blob.data(), &result, param_size);
            reply->writeInt32(rc);
            return NO_ERROR;
        }
        case GET_BOUNDARY_DIMENSIONS: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryDimensionsGA dimensions;
            Parcel::WritableBlob blob;
            uint32_t param_size;

            param_size = sizeof(BoundaryDimensionsGA);
            int result = GetBoundaryDimensions(dimensions);
            reply->writeUint32(param_size);
            status_t status = reply->writeBlob(param_size, false, &blob);
            if (status) {
                // YLOGE("writeblob failed rc:%d", status);
                return status;
            }
            memcpy(blob.data(), &dimensions, param_size);
            blob.release();
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case GET_BOUNDARY_VISIBLE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryVisibleControlGA control;
            bool result = GetBoundaryVisible(control);
            reply->writeInt32(control);
            reply->writeBool(result);
            return NO_ERROR;
        }
        case SET_BOUNDARY_VISIBLE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryVisibleControlGA control = (BoundaryVisibleControlGA)data.readInt32();
            bool result = SetBoundaryVisible(control);
            reply->writeBool(result);
            return NO_ERROR;
        }
        case GET_BOUNDARY_GEOMETRY_POINTS_COUNT: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            int count = GetBoundaryGeometryPointsCount();
            reply->writeInt32(count);
            return NO_ERROR;
        }
        case GET_BOUNDARY_GEOMETRY: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            int count = GetBoundaryGeometryPointsCount();
            yvrVector3f geometry[count];
            int result = GetBoundaryGeometry(geometry);

            Parcel::WritableBlob blob;
            uint32_t param_size = sizeof(yvrVector3f) * count;
            reply->writeUint32(param_size);
            status_t status = reply->writeBlob(param_size, false, &blob);
            if (status) {
                // YLOGE("writeblob failed rc:%d", status);
                return status;
            }
            memcpy(blob.data(), &geometry, param_size);
            blob.release();
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case CLEAR_BOUNDARY_DATA: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            rc = ClearBoundaryData();
            reply->writeInt32(rc);

            return NO_ERROR;
        }
        case SET_BOUNDARY_TYPE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryTypeGA type = (BoundaryTypeGA)data.readInt32();
            rc = SetBoundaryType(type);
            reply->writeInt32(rc);

            return NO_ERROR;
        }
        case GET_BOUNDARY_TYPE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryTypeGA type;
            bool result = GetBoundaryType(type);
            reply->writeInt32(type);
            reply->writeBool(result);
            return NO_ERROR;
        }
        case SET_LOCAL_BOUNDARY_SIZE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            LocalBoundaryRadiusTypeGA radiusType = (LocalBoundaryRadiusTypeGA)data.readInt32();
            rc = SetLocalBoundarySize(radiusType);
            reply->writeInt32(rc);

            return NO_ERROR;
        }
        case GET_LOCAL_BOUNDARY_SIZE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            LocalBoundaryRadiusTypeGA radiusType;
            float radiusValue;
            bool result = GetLocalBoundarySize(radiusType, radiusValue);
            reply->writeInt32(radiusType);
            reply->writeFloat(radiusValue);
            reply->writeBool(result);
            return NO_ERROR;
        }
        case GET_BOUNDARYGAMETYPEDATA_STATUS: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            bool result = GetBoundaryGameTypeDataStatus();
            reply->writeBool(result);
            return NO_ERROR;
        }
        case ON_GUARDIAN_ENTER: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            OnGuardianEnter();
            return NO_ERROR;
        }

        case ON_GUARDIAN_FINISH: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            OnGuardianFinish();
            return NO_ERROR;
        }
        case SET_THIS_BOUNDARY_TYPE_VISIBLE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryTypeGA type = (BoundaryTypeGA)data.readInt32();
            bool quitWith = data.readBool();
            rc = SetThisBoundaryTypeVisible(type, quitWith);
            reply->writeInt32(rc);
            return NO_ERROR;
        }
        // case CREATE_CLIENT: {
        //     CHECK_INTERFACE(ISystemUXClient, data, reply);
        //     sp<IBinder> client = data.readStrongBinder();
        //     String16 processName = data.readString16();
        //     int rc = createClient(client, processName);
        //     reply->writeInt32(rc);
        //     return NO_ERROR;
        // }
        case GET_BOUNDARY_CONFIGURED: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            bool result = GetBoundaryConfigured();
            reply->writeBool(result);
            return NO_ERROR;
        }
        case SET_BOUNDARY_SHOW_STYLE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            BoundaryShowStyleGA style = (BoundaryShowStyleGA)data.readInt32();
            rc = SetBoundaryShowStyle(style);
            reply->writeInt32(rc);

            return NO_ERROR;
        }

        case GET_SHMEM_FD: {
            int fd = 0;
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            rc = getSharedMemory(fd);
            if (fd > 0) {
                reply->writeInt32(1);
                reply->writeDupFileDescriptor(fd);
            } else {
                reply->writeInt32(0);
            }
            reply->writeInt32(rc);
            return NO_ERROR;
        }
        case GET_ORIGIN_BOUNDARY_RADIUS: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float radius;
            int32_t result = GetOriginBoundaryRadius(&radius);
            reply->writeFloat(radius);
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case SET_ORIGIN_BOUNDARY_RADIUS: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            float radius = data.readFloat();
            int32_t result = SetOriginBoundaryRadius(radius);
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case SHOW_DEBUG_IMAGE: {
            CHECK_INTERFACE(ISystemUXClient, data, reply);
            bool bShow = data.readBool();
            int32_t result = ShowGazeImage(bShow);
            reply->writeInt32(result);
            return NO_ERROR;
        }
        case SET_BOUNDARY_EFFECT_DISTANCE: {
            CHECK_INTERFACE(ITrackingManagerService, data, reply);
            float distance = 0;
            data.readFloat(&distance);
            int32_t ret = SetBoundaryEffectDistance(distance);
            reply->writeNoException();
            reply->writeInt32(ret);
            return NO_ERROR;
        }
        case GET_BOUNDARY_EFFECT_DISTANCE: {
            CHECK_INTERFACE(ITrackingManagerService, data, reply);
            float distance = 0;
            int32_t ret = GetBoundaryEffectDistance(&distance);
            reply->writeFloat(distance);
            reply->writeNoException();
            reply->writeInt32(ret);
            return NO_ERROR;
        }
        case GET_BOUNDARY_OUT_EFFECT: {
            CHECK_INTERFACE(ITrackingManagerService, data, reply);
            BoundarOutEffectTypeGA type = BoundarOutEffectTypeGA::EFFECT_NONE;
            int32_t ret = GetBoundaryOutEffectType(&type);
            reply->writeInt32((int32_t)type);
            reply->writeNoException();
            reply->writeInt32(ret);
            return NO_ERROR;
        }

        case SET_BOUNDARY_OUT_EFFECT: {
            CHECK_INTERFACE(ITrackingManagerService, data, reply);
            int32_t type = 0;
            data.readInt32(&type);
            int32_t ret = SetBoundaryOutEffectType(BoundarOutEffectTypeGA(type));
            reply->writeNoException();
            reply->writeInt32(ret);
            return NO_ERROR;
        }

        default:
            return BBinder::onTransact(code, data, reply, flags);
    }

    return NO_ERROR;
}

}  // namespace systemux
}  // namespace yvr
