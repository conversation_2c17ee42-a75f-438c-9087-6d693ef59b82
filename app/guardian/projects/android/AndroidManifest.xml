<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    android:sharedUserId="android.uid.system"
    package="com.yvr.guardianservice">

    <uses-permission android:name="android.permission.INTERACT_ACROSS_USERS"/>
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED"/>
    <uses-permission android:name="android.permission.FORCE_STOP_PACKAGES"/>
    <uses-permission android:name="com.yvr.permission.HAND_TRACKING" />
    <uses-feature android:name="yvr.software.handtracking" android:required="false"/>


    <application
        android:name=".StartPoint"
        android:allowBackup="false"
        android:hasCode="true"
        android:multiArch="true"
        android:persistent="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@android:style/Theme.Material.Light">

        <service android:name=".VrGuardianService"
            android:exported="true"
            android:enabled="true"
            android:singleUser="true">
        </service>

    </application>

</manifest>
