// Top-level build file where you can add configuration options common to all sub-projects/modules.
buildscript {
    repositories {
        maven { url "https://maven.aliyun.com/repository/google" }
        maven { url "https://maven.aliyun.com/repository/central" }
        google()
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:8.1.0'
    }
}

repositories {
    maven { url "https://maven.aliyun.com/repository/google" }
    maven { url "https://maven.aliyun.com/repository/central" }
    google()
    mavenCentral()
}

apply plugin: 'com.android.application'
apply plugin: 'idea'

android {
  namespace 'com.yvr.guardianservice'
  ndkVersion project.ndk_version
  compileSdk project.sharedCompileSdk

  defaultConfig {
    applicationId "com.yvr.guardianservice"
    minSdkVersion project.sharedMinSdk
    targetSdkVersion project.sharedTargetSdk
    versionCode 1
    versionName "1.0"
  }

  sourceSets {
    main {
      manifest.srcFile 'AndroidManifest.xml'
	    aidl.srcDirs = ['../../aidl']
      java.srcDirs = ['../../src/java']
      assets.srcDirs = ['../../assets']
      res.srcDirs = ['../../res']
    }
  }

  signingConfigs {
      release {
          keyAlias 'wywk'
          keyPassword '123456'
          storeFile file('../../../signkey/platform.keystore')
          storePassword '123456'
      }
  }

  buildTypes {
    debug {
      debuggable true
      externalNativeBuild {
        ndk {
          abiFilters "arm64-v8a", "armeabi-v7a"
          debugSymbolLevel 'symbol_table'
        }
        cmake {
          targets "systemuxclient", "systemux", "fooengine", "wearingdetection", "extModuleWDTest", "ipdAdjustmentCalibration"
          arguments "-DCMAKE_BUILD_TYPE=${rootProject.ext.BUILD_VARIANT}",
                    "-DTARGET_PRODUCT=${project.TARGET_PRODUCT}",
                    "-DTARGET_BUILD_SPEC=${project.TARGET_BUILD_SPEC}"
        }
      }
    }

    release {
      debuggable false
      signingConfig signingConfigs.release
      minifyEnabled false // or false if not using ProGuard
      proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'

      externalNativeBuild {
        ndk {
          abiFilters "arm64-v8a", "armeabi-v7a"
          debugSymbolLevel 'symbol_table'
        }
        cmake {
          targets "systemuxclient", "systemux", "fooengine", "wearingdetection", "extModuleWDTest", "ipdAdjustmentCalibration"
          arguments "-DCMAKE_BUILD_TYPE=${rootProject.ext.BUILD_VARIANT}",
                    "-DTARGET_PRODUCT=${project.TARGET_PRODUCT}",
                    "-DTARGET_BUILD_SPEC=${project.TARGET_BUILD_SPEC}"
        }
      }
    }
  }

  externalNativeBuild {
    cmake {
      path file('../../../CMakeLists.txt')
      version "${cmake_version}"
    }
  }

  buildFeatures {
    aidl = true
  }
}

dependencies {
    compileOnly files("../../../../import/1stparty/os/android${rootProject.ext.AOSP_VERSION}/jar/framework-yvr.jar")
    compileOnly files("../../../../import/1stparty/os/android${rootProject.ext.AOSP_VERSION}/jar/emptysdk-release.aar")
}
