// IVrRuntimeService.aidl
package com.yvr.vrruntimeservice;

import com.yvr.vrruntimeservice.IVrRuntimeServiceCallback;

// Declare any non-default types here with import statements

interface IVrRuntimeService {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
    void registerCallback(IVrRuntimeServiceCallback callback);
    void unregisterCallback(IVrRuntimeServiceCallback callback);
    IBinder createClient(IBinder clientToken, int clientType, int clientFlags);
    boolean setCpuLevel(int cpuL);
    boolean setGpuLevel(int gpuL);
    float getCpuUtilization();
    float getGpuUtilization();
    float getBatteryLevel();
    float getBatteryTemperature();
    int getChargeStatus();
    float getVolumeLevel();
    boolean getPowerSavingState();
    boolean getHmdMountState();
    boolean setSpaceDetectState(boolean enable);
    boolean getSpaceDetectState();
    boolean setSpaceDetectRange(float range);
    float getSpaceDetectRange();
}
