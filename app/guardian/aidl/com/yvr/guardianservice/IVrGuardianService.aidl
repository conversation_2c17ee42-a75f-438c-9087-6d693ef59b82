// IVrGuardianService.aidl
package com.yvr.guardianservice;

import com.yvr.guardianservice.IDoubleTapListener;


// Declare any non-default types here with import statements

interface IVrGuardianService {
    /**
     * Demonstrates some basic types that you can use as parameters
     * and return values in AIDL.
     */
   void onUpgradeNotify(int status);
   int getBoundaryStatus();
   boolean isInWearingDetection();
   void addDoubleTapListener(IDoubleTapListener listener);
}

