<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/iv_frame_animation_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:src="@drawable/biz_maunal_ipd_adjustment">
    </ImageView>

    <LinearLayout
        android:id="@+id/btn_position_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/iv_frame_animation_content"
        android:layout_marginTop="100px"
        android:orientation="horizontal"
        android:background="@drawable/btn_position_background">>

        <LinearLayout
            android:id="@+id/btn_position_layout1"
            android:layout_width="480px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="32px"
            android:layout_marginBottom="32px"
            android:orientation="vertical">

            <TextView
                android:id="@+id/btn_position_1_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8px"
                android:layout_marginBottom="2px"
                android:textSize="22sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:text="@string/maunal_ipd_text_position1_1">
            </TextView>

            <TextView
                android:id="@+id/btn_position_1_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="2px"
                android:layout_marginBottom="8px"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:text="@string/maunal_ipd_text_position1_2">
            </TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_position_layout2"
            android:layout_width="480px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="32px"
            android:layout_marginBottom="32px"
            android:orientation="vertical">

            <TextView
                android:id="@+id/btn_position_2_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8px"
                android:layout_marginBottom="2px"
                android:textSize="22sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:text="@string/maunal_ipd_text_position2_1">
            </TextView>

            <TextView
                android:id="@+id/btn_position_2_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="2px"
                android:layout_marginBottom="8px"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:text="@string/maunal_ipd_text_position2_2">
            </TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_position_layout3"
            android:layout_width="480px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="32px"
            android:layout_marginBottom="32px"
            android:orientation="vertical">

            <TextView
                android:id="@+id/btn_position_3_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8px"
                android:layout_marginBottom="2px"
                android:textSize="22sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:text="@string/maunal_ipd_text_position3_1">
            </TextView>

            <TextView
                android:id="@+id/btn_position_3_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="2px"
                android:layout_marginBottom="8px"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:text="@string/maunal_ipd_text_position3_2">
            </TextView>

        </LinearLayout>

        <LinearLayout
            android:id="@+id/btn_position_layout4"
            android:layout_width="480px"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginTop="32px"
            android:layout_marginBottom="32px"
            android:orientation="vertical">

            <TextView
                android:id="@+id/btn_position_4_1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="8px"
                android:layout_marginBottom="2px"
                android:textSize="22sp"
                android:textColor="#FFFFFF"
                android:textStyle="bold"
                android:text="@string/maunal_ipd_text_position4_1">
            </TextView>

            <TextView
                android:id="@+id/btn_position_4_2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="2px"
                android:layout_marginBottom="8px"
                android:textSize="16sp"
                android:textColor="#FFFFFF"
                android:text="@string/maunal_ipd_text_position4_2">
            </TextView>

        </LinearLayout>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/prompt_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/btn_position_layout"
        android:layout_marginTop="100px"
        android:orientation="horizontal">

        <Space
            android:layout_width="20px"
            android:layout_height="wrap_content">
        </Space>

        <ImageView
            android:id="@+id/iv_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/dial">
        </ImageView>

        <Space
            android:layout_width="40px"
            android:layout_height="wrap_content">
        </Space>

        <TextView
            android:id="@+id/position_prompt"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/maunal_ipd_text_turn_the_dial"
            android:layout_gravity="center_vertical"
            android:textSize="22sp"
            android:textStyle="bold"
            android:textColor="#FFFFFF">
        </TextView>

    </LinearLayout>

    <LinearLayout
        android:id="@+id/help_layout"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/prompt_layout"
        android:layout_marginTop="100px"
        android:orientation="horizontal"
        android:visibility="invisible"
        android:background="@drawable/help_background">

        <Space
            android:layout_width="80px"
            android:layout_height="wrap_content">
        </Space>

        <ImageView
            android:id="@+id/iv_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:src="@drawable/warning">
        </ImageView>

        <Space
            android:layout_width="40px"
            android:layout_height="wrap_content">
        </Space>

        <TextView
            android:id="@+id/position_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/maunal_ipd_text_help"
            android:layout_gravity="center_vertical"
            android:textSize="18sp"
            android:textColor="#FFFFFF">
        </TextView>

    </LinearLayout>

</RelativeLayout>