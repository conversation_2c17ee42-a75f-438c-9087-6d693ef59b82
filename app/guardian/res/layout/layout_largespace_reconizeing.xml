<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:paddingBottom="40dp"
    android:paddingTop="40dp"
    android:background="@drawable/dialog_background">
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:text="@string/large_space_retrieving_map_dialog_title"
        android:textSize="35sp"
        android:textStyle="bold"
        android:textColor="#4F525C"/>

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_marginLeft="40dp"
        android:layout_marginRight="40dp"
        android:layout_centerInParent="true"
        android:textSize="20sp"
        android:textColor="#4F525C"
        android:text="@string/large_space_retrieving_map_dialog_context"/>


    <TextView
        android:id="@+id/close_largespace"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/large_space_retrieving_map_dialog_close"
        android:layout_marginTop="40dp"
        android:padding="10dp"
        android:layout_centerHorizontal="true"
        android:layout_alignParentBottom="true"
        android:textSize="20sp"
        android:textColor="#4F525C"
        android:background="@drawable/dialog_regular_button_background"/>
</RelativeLayout>