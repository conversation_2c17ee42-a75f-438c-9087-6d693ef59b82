<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/boundary_not_find_dialog_width"
    android:layout_height="@dimen/boundary_not_find_dialog_height"
    android:background="@drawable/dialog_background">

    <TextView
        android:id="@+id/content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/boundary_not_find_dialog_context_text_size"
        android:layout_marginTop="@dimen/boundary_not_find_dialog_context_text_margin_top"
        android:textColor = "#4F525C"
        android:layout_centerHorizontal="true"
        android:text="@string/boundary_not_find_body"/>

    <Button
        android:id="@+id/sure_btn"
        android:layout_width="@dimen/boundary_not_find_dialog_btn_width"
        android:layout_height="27dp"
        android:layout_centerHorizontal="true"
        android:layout_gravity="bottom"
        android:layout_alignParentBottom="true"
        android:textSize="9sp"
        android:textColor="@drawable/dialog_button_text_color"
        android:text="@string/boundary_not_find_button_situ_boundary"
        android:background="@drawable/dialog_highlighted_button_background"
        android:layout_marginBottom="16dp"/>

</RelativeLayout>