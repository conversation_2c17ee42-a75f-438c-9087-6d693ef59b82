<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/root"
    android:layout_width="@dimen/location_fail_dialog_width"
    android:layout_height="wrap_content"
    android:background="@drawable/dialog_background"
    android:paddingTop="16dp"
    android:paddingBottom="13dp"
    android:orientation="vertical">

    <ImageView
        android:id="@+id/loading_img"
        android:layout_width="27dp"
        android:layout_height="27dp"
        android:layout_centerHorizontal="true"
        android:src="@mipmap/img_loading"/>

    <TextView
        android:id="@+id/content"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_below="@+id/loading_img"
        android:gravity="center"
        android:textSize="12sp"
        android:textColor="#4F525C"
        android:layout_marginTop="10dp"
        android:text="@string/location_fail_content"/>

    <TextView
        android:id="@+id/reson"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_below="@+id/content"
        android:textSize="10sp"
        android:textColor="#636670"
        android:paddingLeft="20dp"
        android:paddingRight="20dp"
        android:layout_marginTop="5dp"
        android:gravity="center"
        android:layout_centerHorizontal="true"
        android:text="@string/location_fail_content2_hand_disable" />
    <LinearLayout
        android:id="@+id/hand_tips"
        android:layout_width="wrap_content"
        android:layout_height="24dp"
        android:layout_below="@+id/reson"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="6dp"
        android:background="@drawable/dialog_background_ed4fe"
        android:gravity="center_vertical"
        android:paddingLeft="6dp"
        android:paddingRight="6dp"
        android:paddingTop="4dp"
        android:visibility="gone"
        android:paddingBottom="4dp"
        android:orientation="horizontal">
        <ImageView
            android:layout_width="16dp"
            android:layout_height="16dp"
            android:src="@mipmap/hand_icon"/>
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginLeft ="4dp"
            android:textColor="#80879C"
            android:textSize="9sp"
            android:text="@string/location_fail_hand_tips" />
    </LinearLayout>
    <Button
        android:id="@+id/sure_btn"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:layout_marginRight="12dp"
        android:gravity="center"
        android:layout_marginTop="13dp"
        android:layout_marginLeft="12dp"
        android:layout_below="@+id/hand_tips"
        android:textSize="9sp"
        android:fontFamily="sans-serif"
        android:textAllCaps="false"
        android:scaleType="fitCenter"
        android:textColor="#636670"
        android:background="@drawable/dialog_regular_button_background"
        android:text="@string/location_fail_sure" />
</RelativeLayout>