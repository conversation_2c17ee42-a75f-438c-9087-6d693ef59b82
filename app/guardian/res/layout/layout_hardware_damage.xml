<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    android:background="@mipmap/dialog_background"
    android:paddingLeft="18dp"
    android:paddingEnd="18dp">
    <ImageView
        android:layout_width="96dp"
        android:layout_height="86dp"
        android:layout_marginTop="48dp"
        android:src="@mipmap/icon_remind" />

    <TextView
        android:id="@+id/title"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/hardware_damage_text_title"
        android:textSize="38sp"
        android:textColor="#F2FFFF"
        android:layout_marginTop="32dp"/>

    <TextView
        android:id="@+id/description"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:textColor="#CCE6E6"
        android:textSize="28sp"
        android:text="@string/hardware_damage_text_context"/>

</LinearLayout>