<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:orientation="vertical"
    android:layout_width="match_parent"
    android:layout_height="match_parent">
    <ImageView
        android:id="@+id/iv_frame_animation_content"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"/>

    <LinearLayout
        android:id="@+id/llo_frame_animation_combine"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_alignParentTop="true"
        android:layout_marginTop="400px"
        android:orientation="vertical">

        <TextView
            android:id="@+id/tv_frame_animation_content"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="32px"
            android:layout_marginBottom="32px"
            android:textSize="6sp"
            android:textStyle="bold"
            android:textColor="#5E5E5E"/>

        <LinearLayout
            android:id="@+id/llo_frame_animation_help"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_below="@+id/tv_frame_animation_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:visibility="invisible"
            android:background="@drawable/help_background_a">

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content"
                android:layout_gravity="left">
            </Space>

            <ImageView
                android:id="@+id/iv_frame_animation_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="left"
                android:src="@drawable/warning_a">
            </ImageView>

            <Space
                android:layout_width="5dp"
                android:layout_height="wrap_content">
            </Space>

            <TextView
                android:id="@+id/tv_frame_animation_help"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:gravity="center"
                android:text="@string/maunal_ipd_text_help"
                android:textSize="6sp"
                android:textColor="#FFFFFF">
            </TextView>

            <Space
                android:layout_width="10dp"
                android:layout_height="wrap_content"
                android:layout_gravity="right">
            </Space>

        </LinearLayout>

    </LinearLayout>

</RelativeLayout>