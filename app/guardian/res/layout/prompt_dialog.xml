<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="@dimen/prompt_dialog_width"
    android:layout_height="@dimen/prompt_dialog_height"
    android:background="@drawable/dialog_background"
    android:paddingLeft="16dp"
    android:paddingEnd="16dp"
    android:orientation="vertical">


    <FrameLayout
        android:id="@+id/titleContainer"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="16dp"
        android:layout_marginBottom="12dp"
        >

        <TextView
            android:id="@+id/title"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_gravity="center_horizontal"
            android:gravity="center_horizontal"
            android:textColor="#4F525C"
            android:textSize="@dimen/text_bigger_size"
            android:textStyle="bold"
            tools:text="Title" />
    </FrameLayout>


    <TextView
        android:id="@+id/body"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textColor="#7F879E"
        android:layout_marginTop="6dp"
        android:layout_marginBottom="6dp"
        android:textSize="@dimen/text_medium_size"
        android:layout_below="@+id/titleContainer"
        tools:text="Body" />

    <RelativeLayout
        android:id="@+id/buttonsLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_alignParentBottom="true"
        android:layout_centerHorizontal="true"
        android:layout_marginBottom="16dp"
        android:gravity="center">

        <Button
            android:id="@+id/leftButton"
            android:layout_width="match_parent"
            android:layout_height="27dp"
            android:layout_alignParentStart="true"
            android:background="@drawable/dialog_highlighted_button_background"
            android:fontFamily="sans-serif"
            android:scaleType="fitCenter"
            android:textColor="@drawable/dialog_button_text_color"
            android:textStyle="bold"
            android:textSize="9sp"
            android:textAllCaps="false"
            tools:text="Cancel" />
    </RelativeLayout>

</RelativeLayout>
