<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/warning_tracking_off_width"
    android:layout_height="@dimen/warning_tracking_off_height"
    android:orientation="vertical"
    android:background="@drawable/dialog_background">

    <TextView
        android:id="@+id/warn_content"
        android:layout_width="@dimen/warning_tracking_off_context_text_width"
        android:layout_height="wrap_content"
        android:layout_centerHorizontal="true"
        android:textSize="@dimen/warning_tracking_off_context_text_size"
        android:textColor="#4F525C"
        android:gravity="center"
        android:layout_marginTop="20dp"/>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="12dp"
        android:layout_marginRight="12dp"
        android:layout_marginBottom="12dp"
        android:layout_alignParentBottom="true"
        android:orientation="horizontal">

        <Button
            android:id="@+id/cancel_btn"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="@dimen/warning_tracking_off_btn_height"
            android:gravity="center"
            android:fontFamily="sans-serif"
            android:textAllCaps="false"
            android:scaleType="fitCenter"
            android:textSize="@dimen/warning_tracking_off_button_text_size"
            android:textColor="#636670"
            android:background="@drawable/dialog_regular_button_background"/>

        <Button
            android:id="@+id/sure_btn"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_height="@dimen/warning_tracking_off_btn_height"
            android:layout_marginLeft="4dp"
            android:fontFamily="sans-serif"
            android:textAllCaps="false"
            android:scaleType="fitCenter"
            android:textColor="#ED5151"
            android:textSize="@dimen/warning_tracking_off_button_text_size"
            android:background="@drawable/dialog_regular_button_background"/>
    </LinearLayout>

</RelativeLayout>