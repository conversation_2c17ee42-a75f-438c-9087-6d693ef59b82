<?xml version="1.0" encoding="utf-8"?>

<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="@dimen/toast_width"
    android:layout_height="@dimen/toast_height"
    android:background="@drawable/dialog_background"
    android:orientation="vertical">

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/toast_icon_size"
        android:layout_marginTop="12dp">
        <ImageView
            android:layout_width="@dimen/toast_icon_size"
            android:layout_height="@dimen/toast_icon_size"
            android:layout_centerInParent="true"
            android:src="@mipmap/icon_warn_bg"/>
        <ImageView
            android:layout_width="3dp"
            android:layout_height="12dp"
            android:layout_centerInParent="true"
            android:src="@mipmap/icon_warn"/>
    </RelativeLayout>

    <TextView
        android:id="@+id/toast_context"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:layout_marginTop="10dp"
        android:layout_marginStart="6dp"
        android:layout_marginEnd="6dp"
        android:textSize="@dimen/toast_text_size"
        android:gravity="center"
        android:text="@string/waring_text_exit_seethrough"
        android:textColor="#545761" />
</LinearLayout>


