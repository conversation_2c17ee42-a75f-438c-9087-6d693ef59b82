<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<item android:state_pressed="true">
    <shape android:shape="rectangle">
        <corners android:radius="24dp" />
        <gradient
            android:startColor="#4488E7"
            android:endColor="#7757D9"            
            android:angle="45"/>
    </shape>
</item>
<item android:state_hovered="true" android:state_pressed="false">
    <shape android:shape="rectangle">
        <corners android:radius="24dp" />
        <gradient
            android:startColor="#8CBCFF"
            android:endColor="#A085F2"
            android:angle="45"/>
    </shape>
</item>
<item android:state_hovered="false" android:state_pressed="false">
    <shape android:shape="rectangle">
        <corners android:radius="24dp" />
        <gradient
            android:startColor="#66A6FF"
            android:endColor="#8E6DF2"
            android:angle="45"/>
    </shape>
</item>
</selector>