<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
<item android:state_pressed="true">
    <shape android:shape="rectangle">
        <corners android:radius="27dp" />
        <solid android:color="#C4DCF5"/>
    </shape>
</item>
<item android:state_hovered="true" android:state_pressed="false">
    <shape android:shape="rectangle">
        <corners android:radius="27dp" />
        <solid android:color="#EBF4FF"/>
    </shape>
</item>
<item android:state_hovered="false" android:state_pressed="false">
    <shape android:shape="rectangle">
        <corners android:radius="27dp" />
        <solid android:color="#CDE1F7"/>
    </shape>
</item>
</selector>