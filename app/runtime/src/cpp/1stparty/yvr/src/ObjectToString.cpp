#include "ObjectToString.h"

const char* yvrLayerTypeToString(yvrLayerType type) {
    switch (type) {
        case kLayerTypeQuad:
            return "quad";
        case kLayerTypeProjection:
            return "projection";
        case kLayerTypeEquirect:
            return "equirect";
        case kLayerTypeEquirect2:
            return "equirect2";
        case kLayerTypeCylinder:
            return "cylinder";
        case kLayerTypeCube:
            return "cube";
        case kLayerTypeProjectionDepth:
            return "projectionDepth";
        case kLayerTypeProjectionMotionDepth:
            return "projectionMotionDepth";
        case kLayerTypeProjectionFoveation:
            return "projectionFoveation";
        case kLayerTypeProjectionYvrLib:
            return "projectionYvrLib";
        case kLayerTypeCompositionLayerYvrLib:
            return "compositionLayerYvrLib";
        case kLayerTypePassthrough:
            return "passthrough";
        default:
            return "unknown";
    }
}

const char* yvrEyeMaskToString(yvrEyeMask eyeMask) {
    switch (eyeMask) {
        case kEyeMaskNone:
            return "none";
        case kEyeMaskLeft:
            return "left";
        case kEyeMaskRight:
            return "right";
        case kEyeMaskBoth:
            return "both";
        default:
            return "unknown";
    }
}

const char* yvrTextureTypeToString(yvrTextureType type) {
    switch (type) {
        case kTypeTexture:
            return "texture";
        case kTypeTextureArray:
            return "textureArray";
        case kTypeImage:
            return "image";
        case kTypeVulkan:
            return "vulkan";
        case kTypeCamera:
            return "camera";
        default:
            return "unknown";
    }
}

const char* yvrTrackingOptModeToString(TrackingOptModeTA modeTa) {
    switch (modeTa) {
        case TRACKING_OPT_STOP:
            return "stop";
        case TRACKING_OPT_3DOF:
            return "3dof";
        case TRACKING_OPT_6DOF:
            return "6dof";
        default:
            return "unknown";
    }
}

const char* yvrAwarenessStateToString(AwarenessState awarenessState) {
    switch (awarenessState) {
        case AWARENESS_STATE_FULL:
            return "full";
        case AWARENESS_STATE_PANEL:
            return "panel";
        case AWARENESS_STATE_DISMISS:
            return "dismiss";
        case AWARENESS_STATE_TOAST:
            return "toast";
        default:
            return "unknown";
    }
}

const char* yvrFRModeToString(yvrFRMode frMode) {
    switch (frMode) {
        case VR_FR_MODE_FFR:
            return "ffr";
        case VR_FR_MODE_ETFR:
            return "etfr";
        default:
            return "unknown";
    }
}

const char* LayerTypeToString(LayerType type) {
    switch (type) {
        case LAYER_TYPE_PASSTHROUGH:
            return "passthrough";
        case LAYER_TYPE_APP:
            return "app";
        case LAYER_TYPE_BOUNDARY:
            return "boundary";
        case LAYER_TYPE_SYSTEMMENU:
            return "systemmenu";
        case LAYER_TYPE_SYSTEMUI:
            return "systemui";
        case LAYER_TYPE_FOCUS_AWARENESS:
            return "focus_awareness";
        case LAYER_TYPE_VRWIDGET:
            return "vrwidget";
        case LAYER_TYPE_LOADING:
            return "loading";
        case LAYER_TYPE_VST:
            return "vst";
        case LAYER_TYPE_FOV_INDICATOR:
            return "fov_indicator";
        default:
            return "unknown";
    }
}

const char* yvrLayerModeToString(yvrLayerMode layerMode) {
    switch (layerMode) {
        case LAYER_OPAQUE:
            return "opaque";
        case LAYER_TRANSLUCENT:
            return "translucent";
        case LAYER_SHOW_PASSTHROUGH:
            return "passthrough";
        case LAYER_TRANSLUCENT_PASSTHROUGH:
            return "translucent&passthrough";
        default:
            return "unknown";
    }
}

const char* yvrShowFlagToString(int32_t showFlag) {
    switch (showFlag) {
        case SHOW_MAIN_SCREEN:
            return "main_screen";
        case SHOW_SCREEN_CAST:
            return "screen_cast";
        case SHOW_MAIN_AND_CAST:
            return "main_and_cast";
        default:
            return "unknown";
    }
}

const char* yvrFrameStateToString(frame_state frameState) {
    switch (frameState) {
        case FRAME_UNKNOW:
            return "unknown";
        case FRAME_RETIRED:
            return "retired";
        case FRAME_BEGIN:
            return "begin";
        case FRAME_END:
            return "end";
        case FRAME_COMPLETED:
            return "complete";
        default:
            return "unknown";
    }
}
const char* yvrCompositeStateToString(composite_state compositeState) {
    switch (compositeState) {
        case FRAME_COMPOSITE_UNKNOW:
            return "unknown";
        case FRAME_COMPOSITING:
            return "compositing";
        case FRAME_COMPOSITE_RELEASE:
            return "release";
        default:
            return "unknown";
    }
}
const char* yvrCameraStatusTAToString(CameraStatusTA cameraStatusTa) {
    switch (cameraStatusTa) {
        case CAMERA_CLIENT_DISCONNECTED:
            return "disconnected";
        case CAMERA_INITIALISING:
            return "initialising";
        case CAMERA_READY:
            return "ready";
        case CAMERA_STARTING:
            return "starting";
        case CAMERA_STARTED:
            return "started";
        case CAMERA_STOPPING:
            return "stopping";
        case CAMERA_STOPPED:
            return "stopped";
        case CAMERA_ERROR:
            return "error";
        default:
            return "unknown";
    }
}
const char* yvrCameraStateToString(CAMERA_STATE cameraState) {
    switch (cameraState) {
        case YCAMERA_STATE_STOPPED:
            return "stopped";
        case YCAMERA_STATE_STOPPING:
            return "stopping";
        case YCAMERA_STATE_STARTED:
            return "started";
        case YCAMERA_STATE_STARTING:
            return "starting";
        default:
            return "unknown";
    }
}

const char* yvrCameraStreamTypeTAToString(CameraStreamTypeTA cameraStreamTypeTa) {
    switch (cameraStreamTypeTa) {
        case STREAM_TYPE_LOW_LATENCY:
            return "low_latency";
        case STREAM_TYPE_DOWN_SCALE:
            return "down_scale";
        case STREAM_TYPE_HIGH_QUALITY:
            return "high_quality";
        default:
            return "unknown";
    }
}
