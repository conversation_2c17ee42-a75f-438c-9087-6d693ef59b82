#ifndef RT_YVR_OBJECTTOSTRING_H
#define RT_YVR_OBJECTTOSTRING_H
#include "trackingapi/tracking_types.h"
#include "Runtime_Types.h"
#include "comp_base.h"
const char* yvrLayerTypeToString(yvrLayerType type);
const char* yvrEyeMaskToString(yvrEyeMask eyeMask);
const char* yvrTextureTypeToString(yvrTextureType type);
const char* yvrTrackingOptModeToString(TrackingOptModeTA modeTa);
const char* yvrAwarenessStateToString(AwarenessState awarenessState);
const char* yvrFRModeToString(yvrFRMode frMode);
const char* LayerTypeToString(LayerType type);
const char* yvrLayerModeToString(yvrLayerMode layerMode);
const char* yvrShowFlagToString(int32_t showFlag);
const char* yvrFrameStateToString(frame_state frameState);
const char* yvrCompositeStateToString(composite_state compositeState);
const char* yvrCameraStatusTAToString(CameraStatusTA cameraStatusTa);
const char* yvrCameraStateToString(CAMERA_STATE cameraState);
const char* yvrCameraStreamTypeTAToString(CameraStreamTypeTA cameraStreamTypeTa);
#endif  // RT_YVR_OBJECTTOSTRING_H
