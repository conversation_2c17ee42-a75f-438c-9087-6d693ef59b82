#include <vector>
#include "yvrutils/DynamicLog.h"
#include "SingleEyeCast/CastDepthCameraRender.h"
#include "trackingapi/TrackingAPI_Helper.h"
#ifndef GL_TEXTURE_EXTERNAL_OES
#define GL_TEXTURE_EXTERNAL_OES 0x8D65
#endif
namespace yvr {
namespace runtime {
CastDepthCameraRender::CastDepthCameraRender(bool isSingleEye) {}

template <int numX, int numY>
void CastDepthCameraRender::GenerateMeshFromDepth(Svr::SvrGeometry** ppOutGeometry) {
    Svr::SvrGeometry* pOutGeometry = new Svr::SvrGeometry;
    *ppOutGeometry = pOutGeometry;

    constexpr int numVerts = (numX + 1) * (numY + 1);
    constexpr int vertexSize = 3 * sizeof(float);  // XY & Z In Memory
    std::vector<float> vecVertexBufferData(numVerts * 3, 2.0f);

    Svr::SvrProgramAttribute attribs[2];
    attribs[0].index = Svr::kPosition;
    attribs[0].size = 2;
    attribs[0].type = GL_FLOAT;
    attribs[0].normalized = false;
    attribs[0].stride = 2 * sizeof(float);
    attribs[0].offset = 0;

    attribs[1].index = Svr::kDepth;
    attribs[1].size = 1;
    attribs[1].type = GL_FLOAT;
    attribs[1].normalized = false;
    attribs[1].stride = sizeof(float);
    attribs[1].offset = 2 * sizeof(float) * numVerts;

    constexpr int depthSize = numVerts * 2 + 16;
    VSTMetaDataTA metaData;
    TRACKINGAPI_GetVSTMetaData(&metaData);
    memcpy(vecVertexBufferData.data(), &metaData.points, sizeof(VSTMetaDataTA::points));

    mCameraRectifyToIMU =
        YVRMath::mat4(metaData.matrix_imu2cam[0], metaData.matrix_imu2cam[1], metaData.matrix_imu2cam[2], metaData.matrix_imu2cam[3], metaData.matrix_imu2cam[4], metaData.matrix_imu2cam[5],
                      metaData.matrix_imu2cam[6], metaData.matrix_imu2cam[7], metaData.matrix_imu2cam[8], metaData.matrix_imu2cam[9], metaData.matrix_imu2cam[10], metaData.matrix_imu2cam[11],
                      metaData.matrix_imu2cam[12], metaData.matrix_imu2cam[13], metaData.matrix_imu2cam[14], metaData.matrix_imu2cam[15]);

    /*
     * Faces
     */
    unsigned int inner = 0;
    unsigned int a, b, c, d;
    unsigned int numPerRow = numX;

    /*
     * Do indices
     */
    constexpr int numIndices = numX * numY * 6;
    std::vector<unsigned int> vecIndicesBuffer;
    vecIndicesBuffer.reserve(numIndices);

    for (int y = 0; y < numY; y++) {
        for (int x = 0; x < numX; x++) {
            a = inner;
            b = inner + 1;
            c = b + (numPerRow + 1);
            d = a + (numPerRow + 1);
            vecIndicesBuffer.insert(vecIndicesBuffer.end(), {a, b, c, a, c, d});
            inner++;
        }
        inner++;
    }

    pOutGeometry->Initialize(&attribs[0], 2, vecIndicesBuffer.data(), numIndices, (const void*)vecVertexBufferData.data(), vertexSize * numVerts, numVerts, false, Svr::YvrDrawType::YVR_DYNAMIC_DRAW);
}

void CastDepthCameraRender::Init() {
    CameraRenderBase::Init();
    mCameraModelMatrix = YVRMath::rotate(YVRMath::mat4(1.0f), YVRMath::radians(90.0f), YVRMath::vec3(0.0, 0.0, 1.0));
    mProjectionMatrix = YVRMath::frustum(gLeftFrustum_Left, gLeftFrustum_Right, gLeftFrustum_Bottom, gLeftFrustum_Top, gLeftFrustum_Near, gLeftFrustum_Far);

    GenerateMeshFromDepth<YUV_CAMERA_MESH_WIDTH - 1, YUV_CAMERA_MESH_HEIGHT - 1>(&mCameraModel);

    float *lTransform, *rTransform;
    float tempRight[22];  // useless
    float* tempLeftPt = mCameraData;
    float* tempRightPt = tempRight;
    mUndistort->GetCameraData(lTransform, rTransform, tempLeftPt, tempRightPt);
    mCameraModel->InitializeUniformBuffer(mCameraData, 22 * sizeof(float), Svr::YvrDrawType::YVR_STATIC_DRAW);
    mCameraRenderParam.PhysicalMatrix = YVRMath::make_mat4(lTransform) * mCameraRectifyToIMU;
    initCameraCastShader();
}

void CastDepthCameraRender::Destroy() {
    mCameraModel->Destroy();
    delete mCameraModel;
    cameraRenderShader.Destroy();
}

void CastDepthCameraRender::Render(yvrEyeMask whichEye, bool isUpScreen) {
    if (mImageHandle > 0) {
        cameraRenderShader.Bind();
        cameraRenderShader.SetUniformVec4("meshScale", mMeshScale);
        cameraRenderShader.SetUniformMat4("MVPBegin", mCameraRenderParam.MVPStartMatrix);
        cameraRenderShader.SetUniformMat4("MVPMid", mCameraRenderParam.MVPMidMatrix);
        cameraRenderShader.SetUniformMat4("MVPEnd", mCameraRenderParam.MVPEndMatrix);
        cameraRenderShader.SetUniformMat4("PhysicalCameraImuMatrix", mCameraRenderParam.PhysicalMatrix);
        cameraRenderShader.SetUniformSampler("srcTex", mImageHandle, GL_TEXTURE_EXTERNAL_OES, 0);
        mCameraModel->SubmitWithUbo();
        cameraRenderShader.Unbind();
    }
}

void CastDepthCameraRender::Update(const YVRMath::vec3& leftRenderPosition, const YVRMath::quat& leftRenderRotation, const YVRMath::vec3& rightRenderPosition,
                                   const YVRMath::quat& rightRenderRotation) {
    YVRMath::mat4 leftViewMatrix = YVRMath::translate(YVRMath::mat4(1.0f), leftRenderPosition) * YVRMath::mat4_cast(leftRenderRotation);
    YVRMath::mat4 viewMatrix = YVRMath::inverse(leftViewMatrix);

    mMvpMatrix = mProjectionMatrix * viewMatrix * mCameraModelMatrix;
}

void CastDepthCameraRender::setCameraRenderData(const yvrCameraRenderData& cameraRenderData) {
    PoseStateTA poseTA;
    poseTA.pose.position = cameraRenderData.imuBeginPosition;
    poseTA.pose.rotation = cameraRenderData.imuBeginRotation;
    GetIMUMatrix(poseTA, mImuBeginMatrix);
    poseTA.pose.position = cameraRenderData.imuMidPosition;
    poseTA.pose.rotation = cameraRenderData.imuMidRotation;
    GetIMUMatrix(poseTA, mImuMidMatrix);
    poseTA.pose.position = cameraRenderData.imuEndPosition;
    poseTA.pose.rotation = cameraRenderData.imuEndRotation;
    GetIMUMatrix(poseTA, mImuEndMatrix);
    mCameraDepthTimestamp = cameraRenderData.renderDepthTimeNs;
    mImageHandle = cameraRenderData.imageHandle;

    mCameraRenderParam.MVPStartMatrix = mMvpMatrix * mImuBeginMatrix * mCameraRectifyToIMU;
    mCameraRenderParam.MVPMidMatrix = mMvpMatrix * mImuMidMatrix * mCameraRectifyToIMU;
    mCameraRenderParam.MVPEndMatrix = mMvpMatrix * mImuEndMatrix * mCameraRectifyToIMU;

    // Todo : call depth interface to update depth data
    VSTCoundPointTA points;
    TRACKINGAPI_GetHistoricalVSTCloudPoint(cameraRenderData.renderDepthTimeNs, &points);
    constexpr uint32_t offset = YUV_CAMERA_MESH_WIDTH * YUV_CAMERA_MESH_HEIGHT * 2 * sizeof(float);
    constexpr uint32_t bufferSize = offset / 2;
    mCameraModel->UpdateBufferData(Svr::YvrBufferType::YVR_ARRAY_BUFFER, offset, bufferSize, points.points);
}
void CastDepthCameraRender::initCameraCastShader() {
    auto castCameraVert =
#include "SingleEyeCast/DepthCameraShader/singleCastCamera.vert"
        ;
    auto castCameraFrag =
#include "SingleEyeCast/DepthCameraShader/singleCastCamera.frag"
        ;
    const char* vertShaderStrings[1];
    const char* fragShaderStrings[1];
    vertShaderStrings[0] = castCameraVert;
    fragShaderStrings[0] = castCameraFrag;
    cameraRenderShader.Initialize(1, vertShaderStrings, 1, fragShaderStrings, "cast_camera_vertex", "cast_camera_fragment");
}

}  // namespace runtime
}  // namespace yvr