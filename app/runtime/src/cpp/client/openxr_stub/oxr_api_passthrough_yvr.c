#include "oxr_objects.h"
#include "oxr_logger.h"
#include "oxr_handle.h"

#include "util/u_debug.h"

#include "include/oxr_api_funcs.h"
#include "include/oxr_api_verify.h"
#include "include/oxr_chain.h"
#include "include/oxr_subaction.h"

#include <stdio.h>

// Function declaration to fix implicit declaration warning
xrt_result_t OpenXRApi_SetPassthroughVisibility(bool visible);

#ifdef XR_YVR_passthrough

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrPassthroughStartYVR(XrSession session) {
    U_LOG_D("Called %s", __func__);
    xrt_result_t ret = OpenXRApi_SetPassthroughVisibility(true);
    if (ret) {
        return XR_ERROR_RUNTIME_FAILURE;
    }

    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrPassthroughStopYVR(XrSession session) {
    U_LOG_D("Called %s", __func__);
    xrt_result_t ret = OpenXRApi_SetPassthroughVisibility(false);
    if (ret) {
        return XR_ERROR_RUNTIME_FAILURE;
    }
    return XR_SUCCESS;
}

#endif
