#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

#include "openxr/openxr.h"
#include "openxr/openxr_platform.h"
#include "openxr/openxr_reflection.h"

// FB passthrough 扩展测试程序

// 创建一个简单的立方体网格
void create_cube_mesh(XrVector3f **vertices, uint32_t **indices, uint32_t *vertex_count, uint32_t *index_count) {
    // 立方体的 8 个顶点
    *vertex_count = 8;
    *vertices = (XrVector3f *)malloc(sizeof(XrVector3f) * (*vertex_count));

    // 前面 4 个顶点
    (*vertices)[0].x = -0.5f;
    (*vertices)[0].y = -0.5f;
    (*vertices)[0].z = -0.5f;
    (*vertices)[1].x = 0.5f;
    (*vertices)[1].y = -0.5f;
    (*vertices)[1].z = -0.5f;
    (*vertices)[2].x = 0.5f;
    (*vertices)[2].y = 0.5f;
    (*vertices)[2].z = -0.5f;
    (*vertices)[3].x = -0.5f;
    (*vertices)[3].y = 0.5f;
    (*vertices)[3].z = -0.5f;

    // 后面 4 个顶点
    (*vertices)[4].x = -0.5f;
    (*vertices)[4].y = -0.5f;
    (*vertices)[4].z = 0.5f;
    (*vertices)[5].x = 0.5f;
    (*vertices)[5].y = -0.5f;
    (*vertices)[5].z = 0.5f;
    (*vertices)[6].x = 0.5f;
    (*vertices)[6].y = 0.5f;
    (*vertices)[6].z = 0.5f;
    (*vertices)[7].x = -0.5f;
    (*vertices)[7].y = 0.5f;
    (*vertices)[7].z = 0.5f;

    // 立方体的 12 个三角形（6 个面，每个面 2 个三角形）
    *index_count = 36;
    *indices = (uint32_t *)malloc(sizeof(uint32_t) * (*index_count));

    // 前面
    (*indices)[0] = 0;
    (*indices)[1] = 1;
    (*indices)[2] = 2;
    (*indices)[3] = 2;
    (*indices)[4] = 3;
    (*indices)[5] = 0;

    // 右面
    (*indices)[6] = 1;
    (*indices)[7] = 5;
    (*indices)[8] = 6;
    (*indices)[9] = 6;
    (*indices)[10] = 2;
    (*indices)[11] = 1;

    // 后面
    (*indices)[12] = 5;
    (*indices)[13] = 4;
    (*indices)[14] = 7;
    (*indices)[15] = 7;
    (*indices)[16] = 6;
    (*indices)[17] = 5;

    // 左面
    (*indices)[18] = 4;
    (*indices)[19] = 0;
    (*indices)[20] = 3;
    (*indices)[21] = 3;
    (*indices)[22] = 7;
    (*indices)[23] = 4;

    // 上面
    (*indices)[24] = 3;
    (*indices)[25] = 2;
    (*indices)[26] = 6;
    (*indices)[27] = 6;
    (*indices)[28] = 7;
    (*indices)[29] = 3;

    // 下面
    (*indices)[30] = 4;
    (*indices)[31] = 5;
    (*indices)[32] = 1;
    (*indices)[33] = 1;
    (*indices)[34] = 0;
    (*indices)[35] = 4;
}

// 测试 FB passthrough 几何实例功能
int test_fb_passthrough_geometry() {
    // 初始化 OpenXR
    XrInstance instance = XR_NULL_HANDLE;
    XrSystemId systemId = XR_NULL_SYSTEM_ID;
    XrSession session = XR_NULL_HANDLE;

    // 创建 instance
    XrInstanceCreateInfo createInfo = {XR_TYPE_INSTANCE_CREATE_INFO};
    strcpy(createInfo.applicationInfo.applicationName, "FB Passthrough Test");
    createInfo.applicationInfo.apiVersion = XR_CURRENT_API_VERSION;

    const char *extensions[] = {XR_FB_PASSTHROUGH_EXTENSION_NAME, XR_FB_TRIANGLE_MESH_EXTENSION_NAME};
    createInfo.enabledExtensionCount = sizeof(extensions) / sizeof(extensions[0]);
    createInfo.enabledExtensionNames = extensions;

    XrResult result = xrCreateInstance(&createInfo, &instance);
    if (XR_FAILED(result)) {
        printf("Failed to create XR instance: %d\n", result);
        return -1;
    }

    // 获取系统
    XrSystemGetInfo systemGetInfo = {XR_TYPE_SYSTEM_GET_INFO};
    systemGetInfo.formFactor = XR_FORM_FACTOR_HEAD_MOUNTED_DISPLAY;
    result = xrGetSystem(instance, &systemGetInfo, &systemId);
    if (XR_FAILED(result)) {
        printf("Failed to get system: %d\n", result);
        xrDestroyInstance(instance);
        return -1;
    }

    // 创建 session
    XrSessionCreateInfo sessionCreateInfo = {XR_TYPE_SESSION_CREATE_INFO};
    sessionCreateInfo.systemId = systemId;
    result = xrCreateSession(instance, &sessionCreateInfo, &session);
    if (XR_FAILED(result)) {
        printf("Failed to create session: %d\n", result);
        xrDestroyInstance(instance);
        return -1;
    }

    // 创建 passthrough
    XrPassthroughFB passthrough = XR_NULL_HANDLE;
    XrPassthroughCreateInfoFB passthroughCreateInfo = {XR_TYPE_PASSTHROUGH_CREATE_INFO_FB};
    passthroughCreateInfo.flags = XR_PASSTHROUGH_IS_RUNNING_AT_CREATION_BIT_FB;

    result = xrCreatePassthroughFB(session, &passthroughCreateInfo, &passthrough);
    if (XR_FAILED(result)) {
        printf("Failed to create passthrough: %d\n", result);
        xrDestroySession(session);
        xrDestroyInstance(instance);
        return -1;
    }

    // 创建 passthrough layer
    XrPassthroughLayerFB layer = XR_NULL_HANDLE;
    XrPassthroughLayerCreateInfoFB layerCreateInfo = {XR_TYPE_PASSTHROUGH_LAYER_CREATE_INFO_FB};
    layerCreateInfo.passthrough = passthrough;
    layerCreateInfo.flags = 0;
    layerCreateInfo.purpose = XR_PASSTHROUGH_LAYER_PURPOSE_RECONSTRUCTION_FB;

    result = xrCreatePassthroughLayerFB(session, &layerCreateInfo, &layer);
    if (XR_FAILED(result)) {
        printf("Failed to create passthrough layer: %d\n", result);
        xrDestroyPassthroughFB(passthrough);
        xrDestroySession(session);
        xrDestroyInstance(instance);
        return -1;
    }

    // 创建三角网格
    XrVector3f *vertices;
    uint32_t *indices;
    uint32_t vertex_count, index_count;

    create_cube_mesh(&vertices, &indices, &vertex_count, &index_count);

    XrTriangleMeshFB mesh = XR_NULL_HANDLE;
    XrTriangleMeshCreateInfoFB meshCreateInfo = {XR_TYPE_TRIANGLE_MESH_CREATE_INFO_FB};
    meshCreateInfo.vertexCount = vertex_count;
    meshCreateInfo.vertexBuffer = vertices;
    meshCreateInfo.triangleCount = index_count / 3;
    meshCreateInfo.indexBuffer = indices;
    meshCreateInfo.windingOrder = XR_WINDING_ORDER_CLOCKWISE_FB;

    result = xrCreateTriangleMeshFB(session, &meshCreateInfo, &mesh);
    if (XR_FAILED(result)) {
        printf("Failed to create triangle mesh: %d\n", result);
        free(vertices);
        free(indices);
        xrDestroyPassthroughLayerFB(layer);
        xrDestroyPassthroughFB(passthrough);
        xrDestroySession(session);
        xrDestroyInstance(instance);
        return -1;
    }

    // 创建几何实例
    XrGeometryInstanceFB instance1 = XR_NULL_HANDLE;
    XrGeometryInstanceCreateInfoFB instanceCreateInfo = {XR_TYPE_GEOMETRY_INSTANCE_CREATE_INFO_FB};
    instanceCreateInfo.layer = layer;
    instanceCreateInfo.mesh = mesh;
    instanceCreateInfo.baseSpace = XR_NULL_HANDLE;  // 需要一个有效的空间
    instanceCreateInfo.pose.position.x = 0.0f;
    instanceCreateInfo.pose.position.y = 0.0f;
    instanceCreateInfo.pose.position.z = -1.0f;
    instanceCreateInfo.pose.orientation.x = 0.0f;
    instanceCreateInfo.pose.orientation.y = 0.0f;
    instanceCreateInfo.pose.orientation.z = 0.0f;
    instanceCreateInfo.pose.orientation.w = 1.0f;
    instanceCreateInfo.scale.x = 0.2f;
    instanceCreateInfo.scale.y = 0.2f;
    instanceCreateInfo.scale.z = 0.2f;

    result = xrCreateGeometryInstanceFB(session, &instanceCreateInfo, &instance1);
    if (XR_FAILED(result)) {
        printf("Failed to create geometry instance: %d\n", result);
        xrDestroyTriangleMeshFB(mesh);
        free(vertices);
        free(indices);
        xrDestroyPassthroughLayerFB(layer);
        xrDestroyPassthroughFB(passthrough);
        xrDestroySession(session);
        xrDestroyInstance(instance);
        return -1;
    }

    // 更新几何实例的变换
    XrGeometryInstanceTransformFB transform = {XR_TYPE_GEOMETRY_INSTANCE_TRANSFORM_FB};
    transform.baseSpace = instanceCreateInfo.baseSpace;
    transform.pose.position.x = 0.5f;
    transform.pose.position.y = 0.0f;
    transform.pose.position.z = -1.0f;
    transform.pose.orientation.x = 0.0f;
    transform.pose.orientation.y = 0.0f;
    transform.pose.orientation.z = 0.0f;
    transform.pose.orientation.w = 1.0f;
    transform.scale.x = 0.3f;
    transform.scale.y = 0.3f;
    transform.scale.z = 0.3f;

    result = xrGeometryInstanceSetTransformFB(instance1, &transform);
    if (XR_FAILED(result)) {
        printf("Failed to set geometry instance transform: %d\n", result);
    }

    // 清理资源
    xrDestroyGeometryInstanceFB(instance1);
    xrDestroyTriangleMeshFB(mesh);
    free(vertices);
    free(indices);
    xrDestroyPassthroughLayerFB(layer);
    xrDestroyPassthroughFB(passthrough);
    xrDestroySession(session);
    xrDestroyInstance(instance);

    printf("FB passthrough geometry test completed successfully!\n");
    return 0;
}

int main() { return test_fb_passthrough_geometry(); }
