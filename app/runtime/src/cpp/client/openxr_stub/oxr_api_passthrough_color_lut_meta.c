#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "openxr/openxr.h"

#include "math/m_api.h"
#include "util/u_debug.h"
#include "util/u_misc.h"

#include "oxr_objects.h"
#include "oxr_logger.h"
#include "oxr_handle.h"
#include "oxr_api_verify.h"
#include "OpenXRApiWrapper.h"

static XrResult oxr_passthrough_color_lut_meta_get(struct oxr_logger *log, XrPassthroughColorLutMETA colorLut, struct oxr_passthrough_color_lut_meta **out_colorLut) {
    *out_colorLut = XRT_CAST_OXR_HANDLE_TO_PTR(struct oxr_passthrough_color_lut_meta *, colorLut);
    if (*out_colorLut == NULL) {
        return oxr_error(log, XR_ERROR_HANDLE_INVALID, "XrPassthroughColorLutMETA is NULL");
    }
    return XR_SUCCESS;
}

static XrResult oxr_passthrough_color_lut_meta_destroy_cb(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct oxr_passthrough_color_lut_meta *colorLut = (struct oxr_passthrough_color_lut_meta *)hb;
    if (colorLut->data) {
        free(colorLut->data);
        colorLut->data = NULL;
    }

    free(colorLut);
    return XR_SUCCESS;
}

static XrPassthroughColorLutMETA oxr_passthrough_color_lut_meta_to_openxr(struct oxr_passthrough_color_lut_meta *colorLut) { return XRT_CAST_PTR_TO_OXR_HANDLE(XrPassthroughColorLutMETA, colorLut); }

XrResult oxr_xrCreatePassthroughColorLutMETA(XrPassthroughFB passthrough, const XrPassthroughColorLutCreateInfoMETA *createInfo, XrPassthroughColorLutMETA *outColorLut) {
    struct oxr_logger log;
    oxr_log_init(&log, "oxr_xrCreatePassthroughColorLutMETA");
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, createInfo, XR_TYPE_PASSTHROUGH_COLOR_LUT_CREATE_INFO_META);
    OXR_VERIFY_ARG_NOT_NULL(&log, outColorLut);

    struct oxr_passthrough *pt = XRT_CAST_OXR_HANDLE_TO_PTR(struct oxr_passthrough *, passthrough);
    if (pt == NULL) {
        return oxr_error(&log, XR_ERROR_HANDLE_INVALID, "XrPassthroughFB is NULL");
    }

    if (createInfo->resolution == 0) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "resolution cannot be 0");
    }

    if (createInfo->data.buffer == NULL || createInfo->data.bufferSize == 0) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "data buffer cannot be NULL or empty");
    }

    size_t channels_count = (createInfo->channels == XR_PASSTHROUGH_COLOR_LUT_CHANNELS_RGB_META) ? 3 : 4;
    size_t expected_size = createInfo->resolution * createInfo->resolution * createInfo->resolution * channels_count;

    if (createInfo->data.bufferSize != expected_size) {
        return oxr_error(&log, XR_ERROR_PASSTHROUGH_COLOR_LUT_BUFFER_SIZE_MISMATCH_META, "data buffer size (%d) does not match expected size (%d)", (int)createInfo->data.bufferSize,
                         (int)expected_size);
    }

    struct oxr_passthrough_color_lut_meta *colorLut = NULL;
    OXR_ALLOCATE_HANDLE_OR_RETURN(&log, colorLut, OXR_XR_DEBUG_PASSTHROUGH_COLOR_LUT, oxr_passthrough_color_lut_meta_destroy_cb, &pt->sess->handle);

    colorLut->sess = pt->sess;
    colorLut->passthrough = pt;
    colorLut->channels = createInfo->channels;
    colorLut->resolution = createInfo->resolution;
    colorLut->data_size = createInfo->data.bufferSize;
    colorLut->data = (uint8_t *)malloc(colorLut->data_size);
    if (!colorLut->data) {
        oxr_handle_destroy(&log, &colorLut->handle);
        return oxr_error(&log, XR_ERROR_OUT_OF_MEMORY, "failed to allocate memory for LUT data");
    }
    memcpy(colorLut->data, createInfo->data.buffer, colorLut->data_size);

    int32_t ret = OpenXRApi_writeColorLutData(colorLut->data, colorLut->data_size, colorLut->resolution, channels_count);
    if (ret != 0) {
        oxr_handle_destroy(&log, &colorLut->handle);
        return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to write color lut data");
    }

    *outColorLut = oxr_passthrough_color_lut_meta_to_openxr(colorLut);
    return XR_SUCCESS;
}

XrResult oxr_xrDestroyPassthroughColorLutMETA(XrPassthroughColorLutMETA colorLut) {
    struct oxr_logger log;
    oxr_log_init(&log, "oxr_xrDestroyPassthroughColorLutMETA");

    int32_t ret = OpenXRApi_setLutActive(false);
    if (ret != 0) {
        return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to set lut active");
    }

    struct oxr_passthrough_color_lut_meta *cl = NULL;
    XrResult result = oxr_passthrough_color_lut_meta_get(&log, colorLut, &cl);
    if (result != XR_SUCCESS) {
        return result;
    }

    return oxr_handle_destroy(&log, &cl->handle);
}

XrResult oxr_xrUpdatePassthroughColorLutMETA(XrPassthroughColorLutMETA colorLut, const XrPassthroughColorLutUpdateInfoMETA *updateInfo) {
    struct oxr_logger log;
    oxr_log_init(&log, "oxr_xrUpdatePassthroughColorLutMETA");

    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, updateInfo, XR_TYPE_PASSTHROUGH_COLOR_LUT_UPDATE_INFO_META);

    struct oxr_passthrough_color_lut_meta *cl = NULL;
    XrResult result = oxr_passthrough_color_lut_meta_get(&log, colorLut, &cl);
    if (result != XR_SUCCESS) {
        return result;
    }

    if (updateInfo->data.buffer == NULL || updateInfo->data.bufferSize == 0) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "data buffer cannot be NULL or empty");
    }

    if (updateInfo->data.bufferSize != cl->data_size) {
        return oxr_error(&log, XR_ERROR_PASSTHROUGH_COLOR_LUT_BUFFER_SIZE_MISMATCH_META, "data buffer size (%d) does not match expected size (%d)", (int)updateInfo->data.bufferSize,
                         (int)cl->data_size);
    }

    memcpy(cl->data, updateInfo->data.buffer, cl->data_size);

    int32_t ret = OpenXRApi_writeColorLutData(cl->data, cl->data_size, cl->resolution, cl->channels);
    if (ret != 0) {
        return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to update color lut data");
    }

    return XR_SUCCESS;
}
