#include "oxr_objects.h"
#include "oxr_logger.h"
#include "oxr_handle.h"

#include "util/u_debug.h"

#include "include/oxr_api_funcs.h"
#include "include/oxr_api_verify.h"
#include "include/oxr_chain.h"
#include "include/oxr_subaction.h"

#include <stdio.h>
#include "OpenXRApiWrapper.h"

XrResult oxr_xrCreatePassthroughFB(XrSession session, const XrPassthroughCreateInfoFB *createInfo, XrPassthroughFB *outPassthrough) {
    struct oxr_session *sess;
    struct oxr_logger log;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "oxr_xrCreatePassthroughFB");
    OXR_VERIFY_SESSION_NOT_LOST(&log, sess);
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, createInfo, XR_TYPE_PASSTHROUGH_CREATE_INFO_FB);
    OXR_VERIFY_PASSTHROUGH_FLAGS(&log, createInfo->flags);

    struct oxr_passthrough *pt;
    XrResult ret = oxr_passthrough_create(&log, sess, createInfo, &pt);
    if (ret != XR_SUCCESS) {
        return ret;
    }

    *outPassthrough = oxr_passthrough_to_openxr(pt);

    return oxr_session_success_result(sess);
}

XrResult oxr_xrPassthroughPauseFB(XrPassthroughFB passthrough) {
    struct oxr_passthrough *pt;
    struct oxr_logger log;

    OXR_VERIFY_PASSTHROUGH_AND_INIT_LOG(&log, passthrough, pt, "oxr_xrPassthroughPauseFB");

    if (OpenXRApi_setLutActive(false) != 0) {
        return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to set lut active");
    }
    return oxr_passthrough_pause(pt);
}

XrResult oxr_xrPassthroughStartFB(XrPassthroughFB passthrough) {
    struct oxr_passthrough *pt;
    struct oxr_logger log;

    OXR_VERIFY_PASSTHROUGH_AND_INIT_LOG(&log, passthrough, pt, "oxr_xrPassthroughStartFB");

    return oxr_passthrough_start(pt);
}

XrResult oxr_xrDestroyPassthroughFB(XrPassthroughFB passthrough) {
    struct oxr_passthrough *pt;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_AND_INIT_LOG(&log, passthrough, pt, "oxr_xrDestroyPassthroughFB");

    return oxr_passthrough_destroy(&log, pt);
}

XrResult oxr_xrCreatePassthroughLayerFB(XrSession session, const XrPassthroughLayerCreateInfoFB *createInfo, XrPassthroughLayerFB *outLayer) {
    struct oxr_session *sess;
    struct oxr_logger log;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "oxr_xrCreatePassthroughLayerFB");
    OXR_VERIFY_SESSION_NOT_LOST(&log, sess);
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, createInfo, XR_TYPE_PASSTHROUGH_LAYER_CREATE_INFO_FB);
    OXR_VERIFY_ARG_NOT_NULL(&log, (void *)createInfo->passthrough);
    OXR_VERIFY_PASSTHROUGH_FLAGS(&log, createInfo->flags);
    OXR_VERIFY_PASSTHROUGH_LAYER_PURPOSE(&log, createInfo->purpose);

    struct oxr_passthrough_layer *passthroughLayer;
    XrResult ret = oxr_passthrough_layer_create(&log, sess, createInfo, &passthroughLayer);
    if (ret != XR_SUCCESS) {
        return ret;
    }

    *outLayer = oxr_passthrough_layer_to_openxr(passthroughLayer);
    return oxr_session_success_result(sess);
}

XrResult oxr_xrDestroyPassthroughLayerFB(XrPassthroughLayerFB layer) {
    struct oxr_passthrough_layer *pl;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_LAYER_AND_INIT_LOG(&log, layer, pl, "oxr_xrDestroyPassthroughLayerFB");

    return oxr_passthrough_layer_destroy(&log, pl);
}

XrResult oxr_xrPassthroughLayerPauseFB(XrPassthroughLayerFB layer) {
    struct oxr_passthrough_layer *pl;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_LAYER_AND_INIT_LOG(&log, layer, pl, "oxr_xrPassthroughLayerPauseFB");

    if (OpenXRApi_setLutActive(false) != 0) {
        return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to set lut active");
    }
    return oxr_passthrough_layer_pause(pl);
}

XrResult oxr_xrPassthroughLayerResumeFB(XrPassthroughLayerFB layer) {
    struct oxr_passthrough_layer *pl;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_LAYER_AND_INIT_LOG(&log, layer, pl, "oxr_xrPassthroughLayerResumeFB");

    return oxr_passthrough_layer_resume(pl);
}

XrResult oxr_xrPassthroughLayerSetStyleFB(XrPassthroughLayerFB layer, const XrPassthroughStyleFB *style) {
    struct oxr_passthrough_layer *pl;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_LAYER_AND_INIT_LOG(&log, layer, pl, "oxr_xrPassthroughLayerSetStyleFB");
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, style, XR_TYPE_PASSTHROUGH_STYLE_FB);
    OXR_VERIFY_PASSTHROUGH_LAYER_STYLE(&log, style);

    pl->style = *style;
    XrPassthroughStyleFB *next = (XrPassthroughStyleFB *)style->next;
    while (next) {
        switch (next->type) {
            case XR_TYPE_PASSTHROUGH_BRIGHTNESS_CONTRAST_SATURATION_FB:
                pl->brightnessContrastSaturation = *(XrPassthroughBrightnessContrastSaturationFB *)next;
                break;
            case XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_MONO_FB:
                pl->monoToMono = *(XrPassthroughColorMapMonoToMonoFB *)next;
                break;
            case XR_TYPE_PASSTHROUGH_COLOR_MAP_MONO_TO_RGBA_FB:
                pl->monoToRgba = *(XrPassthroughColorMapMonoToRgbaFB *)next;
                break;
            case XR_TYPE_PASSTHROUGH_COLOR_MAP_LUT_META: {
                if (OpenXRApi_setLutActive(true) != 0) {
                    return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to set lut active");
                }

                XrPassthroughColorMapLutMETA *colorMapLut = (XrPassthroughColorMapLutMETA *)next;
                if (OpenXRApi_setLutWeight(colorMapLut->weight) != 0) {
                    return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, "failed to set lut weight");
                }
                break;
            }
            default:
                // Ignore other extension types
                break;
        }

        next = (XrPassthroughStyleFB *)next->next;
    }

    return XR_SUCCESS;
}

XrResult oxr_xrCreateGeometryInstanceFB(XrSession session, const XrGeometryInstanceCreateInfoFB *createInfo, XrGeometryInstanceFB *outGeometryInstance) {
    struct oxr_session *sess;
    struct oxr_logger log;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "oxr_xrCreateGeometryInstanceFB");
    OXR_VERIFY_SESSION_NOT_LOST(&log, sess);
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, createInfo, XR_TYPE_GEOMETRY_INSTANCE_CREATE_INFO_FB);

    if (createInfo->layer == XR_NULL_HANDLE) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "layer cannot be XR_NULL_HANDLE");
    }
    if (createInfo->mesh == XR_NULL_HANDLE) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "mesh cannot be XR_NULL_HANDLE");
    }
    if (createInfo->baseSpace == XR_NULL_HANDLE) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "baseSpace cannot be XR_NULL_HANDLE");
    }

    struct oxr_geometry_instance *instance;
    XrResult ret = oxr_geometry_instance_create(&log, sess, createInfo, &instance);
    if (ret != XR_SUCCESS) {
        return ret;
    }

    *outGeometryInstance = oxr_geometry_instance_to_openxr(instance);
    return oxr_session_success_result(sess);
}

XrResult oxr_xrDestroyGeometryInstanceFB(XrGeometryInstanceFB instance) {
    struct oxr_geometry_instance *gi;
    struct oxr_logger log;
    OXR_VERIFY_GEOMETRY_INSTANCE_AND_INIT_LOG(&log, instance, gi, "oxr_xrDestroyGeometryInstanceFB");

    return oxr_geometry_instance_destroy(&log, gi);
}

XrResult oxr_xrGeometryInstanceSetTransformFB(XrGeometryInstanceFB instance, const XrGeometryInstanceTransformFB *transformation) {
    struct oxr_geometry_instance *gi;
    struct oxr_logger log;
    OXR_VERIFY_GEOMETRY_INSTANCE_AND_INIT_LOG(&log, instance, gi, "oxr_xrGeometryInstanceSetTransformFB");
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, transformation, XR_TYPE_GEOMETRY_INSTANCE_TRANSFORM_FB);

    if (transformation->baseSpace == XR_NULL_HANDLE) {
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "baseSpace cannot be XR_NULL_HANDLE");
    }

    return oxr_geometry_instance_set_transform(&log, gi, transformation);
}

XrResult oxr_xrPassthroughLayerSetKeyboardHandsIntensityFB(XrPassthroughLayerFB layer, const XrPassthroughKeyboardHandsIntensityFB *intensity) {
    struct oxr_logger log;
    oxr_log_init(&log, "oxr_xrPassthroughLayerSetKeyboardHandsIntensityFB");
    return oxr_error(&log, XR_ERROR_RUNTIME_FAILURE, " not implemented");
}
