#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "openxr/openxr.h"

#include "math/m_api.h"
#include "math/m_space.h"
#include "util/u_debug.h"
#include "util/u_misc.h"

#include "oxr_objects.h"
#include "oxr_logger.h"
#include "oxr_handle.h"
#include "oxr_chain.h"

// Function declaration to fix implicit declaration warning
xrt_result_t OpenXRApi_AddGeometryInstance(const struct xrt_geometry_instance_fb *instance);
xrt_result_t OpenXRApi_UpdateGeometryInstanceTransform(uint64_t instance_id, const struct xrt_pose *pose, const struct xrt_vec3 *scale);
xrt_result_t OpenXRApi_RemoveGeometryInstance(uint64_t instance_id);

// 实现辅助函数
XrResult oxr_passthrough_layer_get(struct oxr_logger *log, XrPassthroughLayerFB layer, struct oxr_passthrough_layer **out_layer) {
    // 简单实现：直接将句柄转换为指针
    // 注意：这在实际代码中可能不安全，应该使用适当的句柄验证
    *out_layer = (struct oxr_passthrough_layer *)layer;
    return XR_SUCCESS;
}

XrResult oxr_triangle_mesh_get(struct oxr_logger *log, XrTriangleMeshFB mesh, struct oxr_triangle_mesh_fb **out_mesh) {
    // 简单实现：直接将句柄转换为指针
    // 注意：这在实际代码中可能不安全，应该使用适当的句柄验证
    *out_mesh = (struct oxr_triangle_mesh_fb *)mesh;
    return XR_SUCCESS;
}

static XrResult oxr_geometry_instance_destroy_cb(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct oxr_geometry_instance *instance = (struct oxr_geometry_instance *)hb;

    // 确保在销毁句柄之前从渲染系统中移除几何实例
    if (!instance->is_destroyed) {
        OpenXRApi_RemoveGeometryInstance(instance->instance_id);
        instance->is_destroyed = true;
    }

    free(instance);
    return XR_SUCCESS;
}

XrResult oxr_geometry_instance_create(struct oxr_logger *log, struct oxr_session *sess, const XrGeometryInstanceCreateInfoFB *createInfo, struct oxr_geometry_instance **out_instance) {
    struct oxr_geometry_instance *instance = NULL;
    OXR_ALLOCATE_HANDLE_OR_RETURN(log, instance, OXR_XR_DEBUG_GEOMETRY_INSTANCE, oxr_geometry_instance_destroy_cb, &sess->handle);

    instance->sess = sess;

    // 获取透视层
    struct oxr_passthrough_layer *layer = NULL;
    XrResult result = oxr_passthrough_layer_get(log, createInfo->layer, &layer);
    if (result != XR_SUCCESS) {
        oxr_log(log, "Invalid passthrough layer handle in oxr_geometry_instance_create");
        return result;
    }
    instance->layer = layer;

    // 获取三角网格
    struct oxr_triangle_mesh_fb *mesh = NULL;
    result = oxr_triangle_mesh_get(log, createInfo->mesh, &mesh);
    if (result != XR_SUCCESS) {
        oxr_log(log, "Invalid triangle mesh handle in oxr_geometry_instance_create");
        return result;
    }
    instance->mesh = mesh;

    // 保存其他参数
    instance->baseSpace = createInfo->baseSpace;
    instance->pose = createInfo->pose;
    instance->scale = createInfo->scale;
    instance->is_destroyed = false;

    // 创建几何实例并添加到渲染系统
    struct xrt_geometry_instance_fb xrt_instance;

    // 设置几何实例的网格数据
    xrt_instance.mesh.vertexCount = mesh->triangle_mesh_Fb.vertexCount;
    xrt_instance.mesh.triangleCount = mesh->triangle_mesh_Fb.triangleCount;
    xrt_instance.mesh.windingOrder = mesh->triangle_mesh_Fb.windingOrder;

    // 复制顶点和索引数据
    memcpy(xrt_instance.mesh.vertexBuffer, mesh->triangle_mesh_Fb.vertexBuffer, sizeof(struct xrt_vec3) * mesh->triangle_mesh_Fb.vertexCount);
    memcpy(xrt_instance.mesh.indexBuffer, mesh->triangle_mesh_Fb.indexBuffer, sizeof(uint32_t) * mesh->triangle_mesh_Fb.triangleCount * 3);

    // 设置变换数据
    xrt_instance.pose.position.x = createInfo->pose.position.x;
    xrt_instance.pose.position.y = createInfo->pose.position.y;
    xrt_instance.pose.position.z = createInfo->pose.position.z;
    xrt_instance.pose.orientation.x = createInfo->pose.orientation.x;
    xrt_instance.pose.orientation.y = createInfo->pose.orientation.y;
    xrt_instance.pose.orientation.z = createInfo->pose.orientation.z;
    xrt_instance.pose.orientation.w = createInfo->pose.orientation.w;

    xrt_instance.scale.x = createInfo->scale.x;
    xrt_instance.scale.y = createInfo->scale.y;
    xrt_instance.scale.z = createInfo->scale.z;

    // 设置层信息
    xrt_instance.layer_id = (uint64_t)layer;

    // 添加到渲染系统
    xrt_result_t xret = OpenXRApi_AddGeometryInstance(&xrt_instance);
    if (xret != XRT_SUCCESS) {
        oxr_log(log, "Failed to add geometry instance to rendering system");
        free(instance);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    // 保存实例ID，用于后续更新和删除操作
    instance->instance_id = xrt_instance.instance_id;

    U_LOG_D("Created geometry instance for layer %p with mesh %p, instance_id: %llu", (void *)layer, (void *)mesh, (unsigned long long)instance->instance_id);

    *out_instance = instance;
    return XR_SUCCESS;
}

XrResult oxr_geometry_instance_destroy(struct oxr_logger *log, struct oxr_geometry_instance *instance) {
    if (instance->is_destroyed) {
        return XR_ERROR_HANDLE_INVALID;
    }

    // 从渲染系统中移除几何实例
    xrt_result_t xret = OpenXRApi_RemoveGeometryInstance(instance->instance_id);
    if (xret != XRT_SUCCESS) {
        oxr_log(log, "Failed to remove geometry instance from rendering system");
        // 继续执行，尝试清理句柄
    }

    U_LOG_D("Destroyed geometry instance for layer %p with mesh %p, instance_id: %llu", (void *)instance->layer, (void *)instance->mesh, (unsigned long long)instance->instance_id);

    instance->is_destroyed = true;
    return oxr_handle_destroy(log, &instance->handle);
}

XrResult oxr_geometry_instance_set_transform(struct oxr_logger *log, struct oxr_geometry_instance *instance, const XrGeometryInstanceTransformFB *transform) {
    if (instance->is_destroyed) {
        return XR_ERROR_HANDLE_INVALID;
    }

    // 更新几何实例的变换
    instance->baseSpace = transform->baseSpace;
    instance->pose = transform->pose;
    instance->scale = transform->scale;

    // 更新渲染系统中的几何实例变换
    struct xrt_pose pose;
    pose.position.x = transform->pose.position.x;
    pose.position.y = transform->pose.position.y;
    pose.position.z = transform->pose.position.z;
    pose.orientation.x = transform->pose.orientation.x;
    pose.orientation.y = transform->pose.orientation.y;
    pose.orientation.z = transform->pose.orientation.z;
    pose.orientation.w = transform->pose.orientation.w;

    struct xrt_vec3 scale;
    scale.x = transform->scale.x;
    scale.y = transform->scale.y;
    scale.z = transform->scale.z;

    xrt_result_t xret = OpenXRApi_UpdateGeometryInstanceTransform(instance->instance_id, &pose, &scale);
    if (xret != XRT_SUCCESS) {
        oxr_log(log, "Failed to update geometry instance transform in rendering system");
        return XR_ERROR_RUNTIME_FAILURE;
    }

    U_LOG_D("Updated geometry instance transform for layer %p with mesh %p, instance_id: %llu", (void *)instance->layer, (void *)instance->mesh, (unsigned long long)instance->instance_id);

    return XR_SUCCESS;
}
