#include "include/oxr_objects.h"
#include "include/oxr_logger.h"
#include "oxr_handle.h"
#include "util/u_debug.h"
#include "m_api.h"
#include "yvrutils/DynamicTrace.h"
#include "include/oxr_api_funcs.h"
#include "include/oxr_api_verify.h"
#include "OpenXRApiWrapper.h"
#include "yvrutils/DynamicLog.h"
#include "android_globals.h"
#include <android/asset_manager.h>
#include <android/asset_manager_jni.h>
#include <jni.h>
#include <string.h>
#include <strings.h>
#include <stdio.h>
#include <stdlib.h>
#include "trackingapi/TrackingAPI_Helper.h"
#include <thread>

// 从APK的assets目录加载图像文件
static XrResult loadImageFromAssets(struct oxr_logger *log, JNIEnv *env, jobject context, const char *assetPath, const void **outBuffer, off_t *outLength) {
    // 获取AssetManager
    jclass contextClass = env->GetObjectClass(context);
    if (!contextClass) {
        oxr_warn(log, "Failed to get Context class");
        return XR_ERROR_RUNTIME_FAILURE;
    }

    jmethodID getAssetsMethod = env->GetMethodID(contextClass, "getAssets", "()Landroid/content/res/AssetManager;");
    if (!getAssetsMethod) {
        oxr_warn(log, "Failed to get getAssets method");
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    jobject assetManager = env->CallObjectMethod(context, getAssetsMethod);
    if (!assetManager) {
        oxr_warn(log, "Failed to get AssetManager");
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    AAssetManager *mgr = AAssetManager_fromJava(env, assetManager);
    if (!mgr) {
        oxr_warn(log, "Failed to get AAssetManager from Java AssetManager");
        env->DeleteLocalRef(assetManager);
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    AAsset *asset = AAssetManager_open(mgr, assetPath, AASSET_MODE_BUFFER);
    if (!asset) {
        oxr_warn(log, "Failed to open asset file: %s", assetPath);
        env->DeleteLocalRef(assetManager);
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_IMAGE_PATH_NOT_FOUND_YVR;
    }

    const void *buffer = AAsset_getBuffer(asset);
    off_t length = AAsset_getLength(asset);

    if (!buffer || length <= 0) {
        oxr_warn(log, "Failed to get asset buffer or buffer is empty");
        AAsset_close(asset);
        env->DeleteLocalRef(assetManager);
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    // 复制buffer内容，因为AAsset_close后buffer将无效
    void *tempBuffer = malloc(length);
    if (!tempBuffer) {
        oxr_warn(log, "Failed to allocate memory for image data");
        AAsset_close(asset);
        env->DeleteLocalRef(assetManager);
        env->DeleteLocalRef(contextClass);
        return XR_ERROR_RUNTIME_FAILURE;
    }
    memcpy(tempBuffer, buffer, length);
    *outBuffer = tempBuffer;
    *outLength = length;

    AAsset_close(asset);

    env->DeleteLocalRef(assetManager);
    env->DeleteLocalRef(contextClass);

    return XR_SUCCESS;
}

// 从设备文件系统加载图像文件
static XrResult loadImageFromFilesystem(struct oxr_logger *log, const char *filePath, const void **outBuffer, off_t *outLength) {
    // 打开文件
    FILE *file = fopen(filePath, "rb");
    if (!file) {
        oxr_warn(log, "Failed to open file: %s", filePath);
        return XR_ERROR_IMAGE_PATH_NOT_FOUND_YVR;
    }

    // 获取文件大小
    fseek(file, 0, SEEK_END);
    off_t length = ftell(file);
    fseek(file, 0, SEEK_SET);

    if (length <= 0) {
        oxr_warn(log, "File is empty: %s", filePath);
        fclose(file);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    // 分配内存并读取文件内容
    void *tempBuffer = malloc(length);
    if (!tempBuffer) {
        oxr_warn(log, "Failed to allocate memory for image data");
        fclose(file);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    size_t bytesRead = fread(tempBuffer, 1, length, file);
    fclose(file);

    if (bytesRead != (size_t)length) {
        oxr_warn(log, "Failed to read entire file: %s", filePath);
        free(tempBuffer);
        return XR_ERROR_RUNTIME_FAILURE;
    }

    *outBuffer = tempBuffer;
    *outLength = length;

    return XR_SUCCESS;
}

XrResult oxr_xrSetImageTrackingYVR(XrSession session, XrBool32 enabled) {
    struct oxr_session *sess;
    struct oxr_logger log;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "xrSetImageTrackingYVR");
    OpenXRApi_SetImageTrackingEnable(enabled);
    return XR_SUCCESS;
}

XrResult oxr_xrRegisterImageTemplateYVR(XrSession session, XrImageTemplateInfoYVR info) {
    struct oxr_session *sess;
    struct oxr_logger log;
    XrImageTemplateInfoYVR *infoPtr = &info;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "xrRegisterImageTemplateYVR");
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, infoPtr, XR_TYPE_IMAGE_TEMPLATE_INFO_YVR);

    OXR_VERIFY_ARG_NOT_NULL(&log, info.imageId);
    OXR_VERIFY_ARG_NOT_NULL(&log, info.templatePath);
    // OXR_VERIFY_POSITIVE_FLOAT(&log, info.physicalWidth, "info.physicalWidth");
    // OXR_VERIFY_POSITIVE_FLOAT(&log, info.physicalHeight, "info.physicalHeight");
    OXR_VERIFY_PATH_PREFIX_EITHER(&log, info.templatePath, "apk://", "file://");

    // 定义路径前缀常量
    const char *APK_PREFIX = "apk://";
    const char *FILE_PREFIX = "file://";
    const void *buffer = nullptr;
    off_t length = 0;

    // 路径前缀已在验证宏中检查，这里直接处理
    if (strncmp(info.templatePath, APK_PREFIX, strlen(APK_PREFIX)) == 0) {
        // 获取JavaVM和Context
        _JavaVM *vm = android_globals_get_vm();
        jobject context = (jobject)android_globals_get_context();

        if (!vm || !context) {
            U_LOG_E("Failed to get JavaVM or Context");
            return XR_ERROR_VALIDATION_FAILURE;
        }

        // 获取JNIEnv
        JNIEnv *env = nullptr;
        vm->AttachCurrentThread(&env, nullptr);
        if (!env) {
            U_LOG_E("Failed to attach current thread to JVM");
            return XR_ERROR_RUNTIME_FAILURE;
        }
        // 从APK的assets目录加载文件
        const char *assetPath = info.templatePath + strlen(APK_PREFIX);
        XrResult result = loadImageFromAssets(&log, env, context, assetPath, &buffer, &length);
        if (result != XR_SUCCESS) {
            return result;
        }
    } else if (strncmp(info.templatePath, FILE_PREFIX, strlen(FILE_PREFIX)) == 0) {
        // 从设备文件系统加载文件 (file:// 前缀)
        const char *filePath = info.templatePath + strlen(FILE_PREFIX);
        XrResult result = loadImageFromFilesystem(&log, filePath, &buffer, &length);
        if (result != XR_SUCCESS) {
            return result;
        }
    } else {
        return XR_ERROR_VALIDATION_FAILURE;
    }

    std::thread([info, buffer, length]() {
        OpenXRApi_RegisterImageTemplate(info.imageId, info.physicalWidth, info.physicalHeight, buffer, length);
        if (buffer) {
            free((void *)buffer);
        }
    }).detach();

    return XR_SUCCESS;
}

XrResult oxr_xrUnRegisterImageTemplateYVR(XrSession session, char *imageId) {
    struct oxr_session *sess;
    struct oxr_logger log;

    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "xrUnRegisterImageTemplateYVR");
    OXR_VERIFY_ARG_NOT_NULL(&log, imageId);
    OpenXRApi_UnRegisterImageTemplate(imageId);
    // xrt_result ret =
    // if (ret != XR_SUCCESS) {
    //     return ret;
    // }
    return XR_SUCCESS;
}

static XrResult oxr_space_destroy(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct oxr_space *spc = (struct oxr_space *)hb;
    free(spc);
    return XR_SUCCESS;
}

XrResult oxr_xrCreateImageSpaceYVR(XrSession session, const XrImageSpaceCreateInfoYVR *createInfo, XrSpace *outSpace) {
    struct oxr_session *sess;
    struct oxr_logger log;

    PROFILE_ENTER("%s", __func__);

    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "xrCreateImageSpaceYVR");
    OXR_VERIFY_ARG_NOT_NULL(&log, outSpace);
    OXR_VERIFY_ARG_TYPE_AND_NOT_NULL(&log, createInfo, XR_TYPE_IMAGE_SPACE_CREATE_INFO_YVR);

    // 创建空间对象
    struct oxr_space *spc = NULL;
    OXR_ALLOCATE_HANDLE_OR_RETURN(&log, spc, OXR_XR_DEBUG_SPACE, oxr_space_destroy, &sess->handle);
    spc->sess = sess;
    spc->space_type = OXR_SPACE_TYPE_IMAGE_YVR;
    memcpy(&spc->pose, &createInfo->poseInImageSpace, sizeof(spc->pose));
    strncpy(spc->image_id, createInfo->imageId, sizeof(spc->image_id));

    // 确保字符串以null结尾
    spc->image_id[sizeof(spc->image_id) - 1] = '\0';

    U_LOG_E("oxr_xrCreateImageSpaceYVR %s", spc->image_id);

    // TODO: 验证imageId是否有效，如果无效返回XR_ERROR_IMAGE_ID_INVALID_YVR

    *outSpace = oxr_space_to_openxr(spc);

    PROFILE_EXIT();
    return XR_SUCCESS;
}
