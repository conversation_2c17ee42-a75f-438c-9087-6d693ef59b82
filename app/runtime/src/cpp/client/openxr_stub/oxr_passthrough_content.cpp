#include "include/oxr_objects.h"
#include "include/oxr_logger.h"
#include "include/oxr_two_call.h"
#include "include/oxr_api_funcs.h"
#include "yvrutils/DynamicTrace.h"
#include "include/oxr_extension_support.h"
#include "include/oxr_api_verify.h"
#include "util/u_debug.h"

#include "OpenXRApiWrapper.h"
#include "oxr_handle.h"
#include "include/oxr_chain.h"
#include "Runtime_Helper.h"

#include "math/m_api.h"
#include "math/m_mathinclude.h"
#include "math/m_space.h"
#include "yvrutils/TimeUtils.h"
#include "yvrutils/DynamicLog.h"

#define DEBUG_TAG "YVR_PASSTHROUGH_FB:"

// must be same with PassthroughContentProvider::kContentWidth&kContentHeight
#define PASSTHROUGH_CONTENT_WIDTH 512
#define PASSTHROUGH_CONTENT_HEIGHT 478

#ifdef OXR_HAVE_YVR_passthrough_content
static XrResult oxr_passthrough_context_destroy_cb(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct oxr_passthrough_context *context = (struct oxr_passthrough_context *)hb;
    if (context != NULL) {
        free(context);
        context = NULL;
        OpenXRApi_DestroyPassthroughProvider();
    }
    U_LOG_D("destroy passthrough context");
    return XR_SUCCESS;
}

static XrResult oxr_passthrough_swapchain_destroy_cb(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct oxr_passthrough_swapchain *swapchain = (struct oxr_passthrough_swapchain *)hb;
    if (swapchain != NULL) {
        free(swapchain);
        swapchain = NULL;
    }
    U_LOG_D("destroy passthrough swapchain");
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrCreatePassthroughProviderYVR(XrSession session, XrPassthroughProviderYVR *passthroughProvider) {
    U_LOG_D("in");
    struct oxr_session *sess;
    struct oxr_logger log;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "oxr_xrCreatePassthroughProviderYVR");
    struct oxr_passthrough_context *context = NULL;
    OXR_ALLOCATE_HANDLE_OR_RETURN(&log, context, OXR_XR_DEBUG_PASSTHROUGH_CONTENT, oxr_passthrough_context_destroy_cb, &sess->handle);
    context->sess = sess;
    context->active = false;
    context->swapchain_create = false;
    OpenXRApi_CreatePassthroughProvider();
    *passthroughProvider = XRT_CAST_PTR_TO_OXR_HANDLE(XrPassthroughProviderYVR, context);
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrDestroyPassthroughProviderYVR(XrPassthroughProviderYVR passthroughProvider) {
    U_LOG_D("in");
    struct oxr_passthrough_context *context = NULL;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_CONTENT_AND_INIT_LOG(&log, passthroughProvider, context, "oxr_xrDestroyPassthroughProviderYVR");

    if (context->swapchain_create == true) {
        return oxr_error(&log, XR_ERROR_CALL_ORDER_INVALID, "XrPassthroughProviderYVR not destroy");
    }

    return oxr_handle_destroy(&log, &context->handle);
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrStartPassthroughProviderYVR(XrPassthroughProviderYVR passthroughProvider) {
    U_LOG_D("in");
    struct oxr_passthrough_context *context = NULL;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_CONTENT_AND_INIT_LOG(&log, passthroughProvider, context, "oxr_xrStartPassthroughProviderYVR");
    if (context->active == true) {
        return XR_SUCCESS;
    }

    OpenXRApi_StartPassthroughProvider();

    context->active = true;
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrStopPassthroughProviderYVR(XrPassthroughProviderYVR passthroughProvider) {
    U_LOG_D("in");
    struct oxr_passthrough_context *context = NULL;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_CONTENT_AND_INIT_LOG(&log, passthroughProvider, context, "oxr_xrStopPassthroughProviderYVR");
    if (context->active == false) {
        return XR_SUCCESS;
    }

    OpenXRApi_StopPassthroughProvider();

    context->active = false;
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrCreatePassthroughSwapchainYVR(XrPassthroughProviderYVR passthroughProvider, XrPassthroughSwapchainYVR *swapchain) {
    U_LOG_D("in");
    struct oxr_passthrough_context *context = NULL;
    struct oxr_logger log;
    struct oxr_session *sess;
    OXR_VERIFY_PASSTHROUGH_CONTENT_AND_INIT_LOG(&log, passthroughProvider, context, "oxr_xrCreatePassthroughSwapchainYVR");
    sess = context->sess;
    if (context->swapchain_create == true) {
        return oxr_error(&log, XR_ERROR_LIMIT_REACHED, "passthroughProvider already create swapchain!!");
    }

    XrSwapchainCreateInfo xrSwapchainCreateInfo{};
    xrSwapchainCreateInfo.type = XR_TYPE_SWAPCHAIN_CREATE_INFO;
    xrSwapchainCreateInfo.next = NULL;
    xrSwapchainCreateInfo.usageFlags = XR_SWAPCHAIN_USAGE_SAMPLED_BIT | XR_SWAPCHAIN_USAGE_COLOR_ATTACHMENT_BIT;

    xrSwapchainCreateInfo.format = GL_RGBA8;
    xrSwapchainCreateInfo.arraySize = 2;
    xrSwapchainCreateInfo.mipCount = 1;
    xrSwapchainCreateInfo.faceCount = 1;
    xrSwapchainCreateInfo.sampleCount = 1;
    xrSwapchainCreateInfo.width = PASSTHROUGH_CONTENT_WIDTH;
    xrSwapchainCreateInfo.height = PASSTHROUGH_CONTENT_HEIGHT;
    struct oxr_passthrough_swapchain *passthrough_swapchain;
    OXR_ALLOCATE_HANDLE_OR_RETURN(&log, passthrough_swapchain, OXR_XR_DEBUG_PASSTHROUGH_CONTENT, oxr_passthrough_swapchain_destroy_cb, &context->handle);
    passthrough_swapchain->sess = sess;
    passthrough_swapchain->context = NULL;
    XrResult ret = sess->create_swapchain(&log, sess, &xrSwapchainCreateInfo, &passthrough_swapchain->swap_chain, XRT_SWAPCHAIN_USAGE_PASSTHROUGH_CONTENT);
    if (ret != XR_SUCCESS) {
        oxr_handle_destroy(&log, &passthrough_swapchain->handle);
        return oxr_error(&log, XR_ERROR_VALIDATION_FAILURE, "create failed");
    }

    context->swapchain_create = true;
    passthrough_swapchain->context = context;
    *swapchain = XRT_CAST_PTR_TO_OXR_HANDLE(XrPassthroughSwapchainYVR, passthrough_swapchain);
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrDestroyPassthroughSwapchainYVR(XrPassthroughSwapchainYVR swapchain) {
    U_LOG_D("in");
    struct oxr_passthrough_swapchain *passthrough_swapchain = NULL;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_SWAPCHAIN_AND_INIT_LOG(&log, swapchain, passthrough_swapchain, "oxr_xrDestroyPassthroughSwapchainYVR");

    passthrough_swapchain->context->swapchain_create = false;
    oxr_handle_destroy(&log, &passthrough_swapchain->swap_chain->handle);
    oxr_handle_destroy(&log, &passthrough_swapchain->handle);
    return XR_SUCCESS;
}

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrEnumeratePassthroughSwapchainImagesYVR(XrPassthroughSwapchainYVR swapchain, uint32_t imageCapacityInput, uint32_t *imageCountOutput,
                                                                            XrSwapchainImageBaseHeader *images) {
    U_LOG_D("in");
    struct oxr_passthrough_swapchain *passthrough_swapchain = NULL;
    struct oxr_logger log;
    OXR_VERIFY_PASSTHROUGH_SWAPCHAIN_AND_INIT_LOG(&log, swapchain, passthrough_swapchain, "oxr_xrEnumeratePassthroughSwapchainImagesYVR");
    struct oxr_swapchain *oxrSwapchain = passthrough_swapchain->swap_chain;
    uint32_t image_count = oxrSwapchain->swapchain->image_count;
    if (imageCountOutput != NULL) {
        *imageCountOutput = image_count;
    }

    if (imageCapacityInput == 0) {
        return XR_SUCCESS;
    }

    if (imageCapacityInput < image_count) {
        return oxr_error(&log, XR_ERROR_SIZE_INSUFFICIENT, "(imageCapacityInput == %u)", imageCapacityInput);
    }

    return oxrSwapchain->enumerate_images(&log, oxrSwapchain, image_count, images);
}

extern XrResult xrt_get_view_space_relations(const struct xrt_space_relation &T_base_head, const struct xrt_pose &view_pose, uint32_t index, struct xrt_space_relation &out_result_base,
                                             struct xrt_space_relation &out_result_offset, bool needFrustumRotation);

XRAPI_ATTR XrResult XRAPI_CALL oxr_xrAcquirePassthroughImageYVR(XrPassthroughProviderYVR passthroughProvider, const XrPassthroughAcquireInfoYVR *acquireInfo, XrPassthroughImageYVR *passthroughImage) {
    struct oxr_logger log;
    struct oxr_passthrough_context *context = NULL;
    struct oxr_session *session = NULL;
    struct oxr_space *spc = NULL;
    struct xrt_device *xdev = NULL;
    int32_t swapChainIndex = 0;
    uint64_t img_timestamp = 0;      // boot time
    int64_t img_timestamp_mono = 0;  // mono time

    OXR_VERIFY_ARG_NOT_NULL(&log, acquireInfo);
    OXR_VERIFY_ARG_NOT_NULL(&log, passthroughImage);
    OXR_VERIFY_PASSTHROUGH_CONTENT_AND_INIT_LOG(&log, passthroughProvider, context, "oxr_xrAcquirePassthroughImageYVR");

    spc = XRT_CAST_OXR_HANDLE_TO_PTR(struct oxr_space *, acquireInfo->space);
    session = context->sess;
    xdev = GET_XDEV_BY_ROLE(session->sys, head);

    if (!context->active) {
        return oxr_error(&log, XR_ERROR_CALL_ORDER_INVALID, "passthroughProvider not active");
    }

    if (!session->frame_started) {
        return oxr_error(&log, XR_ERROR_CALL_ORDER_INVALID, "call must in [xrBeginFrame xrEndFrame]");
    }
    // img_timestamp is bootTime
    xrt_result_t result = OpenXRApi_AcquirePassthroughSwapChain(&swapChainIndex, &img_timestamp);
    if (result != XRT_SUCCESS) {
        return oxr_error(&log, XR_ERROR_LAYER_INVALID, "passthrough content is not ready");
    }
    passthroughImage->swapchainIndex = swapChainIndex;
    // 返回给应用的需要转换成mono time
    OpenXRApi_ConvertBootToMonoTime(img_timestamp, &img_timestamp_mono);
    passthroughImage->imageTime = img_timestamp_mono;
    // YLOGD("bttime:%f img_timestamp_mono:%f",NSEC_TO_MSEC(img_timestamp),NSEC_TO_MSEC(img_timestamp_mono));
    //  获取IPD值
    float ipd_meters[3] = {0.0f, 0.0f, 0.0f};
    struct xrt_fov fovs[2] = {};
    struct xrt_pose eye_poses[2] = {};
    struct xrt_space_relation T_xdev_head = XRT_SPACE_RELATION_ZERO;
    RuntimeHelper::getIpdMeters(ipd_meters);
    const struct xrt_vec3 eye_relation = {
        ipd_meters[0],
        ipd_meters[1],
        ipd_meters[2],
    };

    // 下面都使用bootTime
    //  拿到眼中心的pose T_xdev_head和每个眼睛的pose eye_poses
    xrt_device_get_view_poses(xdev, &eye_relation, img_timestamp, 2, &T_xdev_head, fovs, eye_poses);

    // The xdev pose in the base space.
    struct xrt_space_relation T_base_xdev = XRT_SPACE_RELATION_ZERO;
    XrResult ret = oxr_space_locate_device(&log, xdev, spc, img_timestamp, &T_base_xdev);
    if (ret != XR_SUCCESS || T_base_xdev.relation_flags == 0) {
        U_LOG_E("return invalid pose");
        return ret;
    }

    struct xrt_space_relation T_base_head;
    struct xrt_relation_chain xrc = {};
    m_relation_chain_push_relation(&xrc, &T_xdev_head);
    m_relation_chain_push_relation(&xrc, &T_base_xdev);
    m_relation_chain_resolve(&xrc, &T_base_head);

    for (int32_t i = 0; i < 2; i++) {
        // apply rot offset on per eye
        struct xrt_space_relation result_offset = {};
        struct xrt_space_relation result = {};
        const struct xrt_pose view_pose = eye_poses[i];
        // head坐标系下的pose转base坐标系下pose 然后转8°输出
        xrt_get_view_space_relations(T_base_head, view_pose, i, result, result_offset, true);
        OXR_XRT_POSE_TO_XRPOSEF(result_offset.pose, passthroughImage->views[i].pose);
    }

    U_LOG_D("spaceType:%d, passthroughImage->views[0].pose: (%f %f %f %f)(%f %f %f) views[1].pose: (%f %f %f %f)(%f %f %f)", spc->space_type, passthroughImage->views[0].pose.orientation.x,
            passthroughImage->views[0].pose.orientation.y, passthroughImage->views[0].pose.orientation.z, passthroughImage->views[0].pose.orientation.w, passthroughImage->views[0].pose.position.x,
            passthroughImage->views[0].pose.position.y, passthroughImage->views[0].pose.position.z, passthroughImage->views[1].pose.orientation.x, passthroughImage->views[1].pose.orientation.y,
            passthroughImage->views[1].pose.orientation.z, passthroughImage->views[1].pose.orientation.w, passthroughImage->views[1].pose.position.x, passthroughImage->views[1].pose.position.y,
            passthroughImage->views[1].pose.position.z);
    return XR_SUCCESS;
}
#endif  // OXR_HAVE_YVR_passthrough_content
