#include "oxr_objects.h"
#include "oxr_logger.h"
#include "oxr_handle.h"
#include "oxr_api_verify.h"
#include "util/u_debug.h"
#include "math/m_api.h"

#define DEBUG_TAG "TriangleMeshFB:"

// 将 XrVector3f 转换为 xrt_vec3
static inline void convert_xr_vec3_to_xrt_vec3(const XrVector3f *src, struct xrt_vec3 *dst) {
    dst->x = src->x;
    dst->y = src->y;
    dst->z = src->z;
}
static XrResult oxr_trianglemesh_fb_destroy_cb(struct oxr_logger *log, struct oxr_handle_base *hb) {
    struct triangle_mesh_fb *triangleMeshFb = (struct triangle_mesh_fb *)hb;
    if (triangleMeshFb != NULL) {
        free(triangleMeshFb);
        triangleMeshFb = NULL;
    }
    U_LOG_D("%s free triangleMeshFb", DEBUG_TAG);
    return XR_SUCCESS;
}

XrResult oxr_xrCreateTriangleMeshFB(XrSession session, const XrTriangleMeshCreateInfoFB *createInfo, XrTriangleMeshFB *outTriangleMesh) {
    struct oxr_session *sess;
    struct oxr_logger log;
    struct oxr_triangle_mesh_fb *triangleMeshFb = NULL;
    OXR_VERIFY_SESSION_AND_INIT_LOG(&log, session, sess, "xrCreateTriangleMeshFB");
    OXR_ALLOCATE_HANDLE_OR_RETURN(&log, triangleMeshFb, OXR_XR_DEBUG_TRIANGLEMESH, oxr_trianglemesh_fb_destroy_cb, &sess->handle);
    if (createInfo->vertexCount > TRIANGLE_MESH_FB_MAX_BUFFER_SIZE || createInfo->triangleCount > TRIANGLE_MESH_FB_MAX_BUFFER_SIZE) {
        return oxr_error(&log, XR_ERROR_INDEX_OUT_OF_RANGE, "vertexCount is too large");
    }
    triangleMeshFb->sess = sess;
    triangleMeshFb->meshFlagsFb = createInfo->flags;
    triangleMeshFb->triangle_mesh_Fb.windingOrder = createInfo->windingOrder;
    triangleMeshFb->triangle_mesh_Fb.vertexCount = createInfo->vertexCount;

    // 转换顶点数据
    for (uint32_t i = 0; i < createInfo->vertexCount; i++) {
        convert_xr_vec3_to_xrt_vec3(&createInfo->vertexBuffer[i], &triangleMeshFb->triangle_mesh_Fb.vertexBuffer[i]);
    }

    triangleMeshFb->triangle_mesh_Fb.triangleCount = createInfo->triangleCount;

    // 复制索引数据
    memcpy(triangleMeshFb->triangle_mesh_Fb.indexBuffer, createInfo->indexBuffer, sizeof(uint32_t) * createInfo->triangleCount * 3);
    *outTriangleMesh = XRT_CAST_PTR_TO_OXR_HANDLE(XrTriangleMeshFB, triangleMeshFb);
    U_LOG_D("%s oxr_xrCreateTriangleMeshFB vertexCount:%d,triangleCount:%d", DEBUG_TAG, triangleMeshFb->triangle_mesh_Fb.vertexCount, triangleMeshFb->triangle_mesh_Fb.triangleCount);
    return XR_SUCCESS;
}

XrResult oxr_xrDestroyTriangleMeshFB(XrTriangleMeshFB mesh) {
    struct oxr_triangle_mesh_fb *triangleMeshFb = NULL;
    struct oxr_logger log;
    OXR_VERIFY_TRIANGLEMESH_AND_INIT_LOG(&log, mesh, triangleMeshFb, "oxr_xrDestroyTriangleMeshFB");
    U_LOG_D("%s oxr_xrDestroyTriangleMeshFB flags:%" PRId64 "", DEBUG_TAG, triangleMeshFb->meshFlagsFb);
    return oxr_handle_destroy(&log, &triangleMeshFb->handle);
}

XrResult oxr_xrTriangleMeshGetIndexBufferFB(XrTriangleMeshFB mesh, uint32_t **outIndexBuffer) {
    struct oxr_triangle_mesh_fb *triangleMeshFb = NULL;
    struct oxr_logger log;
    OXR_VERIFY_TRIANGLEMESH_AND_INIT_LOG(&log, mesh, triangleMeshFb, "oxr_xrTriangleMeshGetIndexBufferFB");
    memcpy(*outIndexBuffer, triangleMeshFb->triangle_mesh_Fb.indexBuffer, sizeof(uint32_t) * triangleMeshFb->triangle_mesh_Fb.triangleCount);
    U_LOG_D("%s oxr_xrTriangleMeshGetIndexBufferFB triangleCount:%d", DEBUG_TAG, triangleMeshFb->triangle_mesh_Fb.triangleCount);
    return XR_SUCCESS;
}

XrResult oxr_xrTriangleMeshGetVertexBufferFB(XrTriangleMeshFB mesh, XrVector3f **outVertexBuffer) {
    struct oxr_triangle_mesh_fb *triangleMeshFb = NULL;
    struct oxr_logger log;
    OXR_VERIFY_TRIANGLEMESH_AND_INIT_LOG(&log, mesh, triangleMeshFb, "oxr_xrTriangleMeshGetVertexBufferFB");

    // 将 xrt_vec3 转换回 XrVector3f
    for (uint32_t i = 0; i < triangleMeshFb->triangle_mesh_Fb.vertexCount; i++) {
        (*outVertexBuffer)[i].x = triangleMeshFb->triangle_mesh_Fb.vertexBuffer[i].x;
        (*outVertexBuffer)[i].y = triangleMeshFb->triangle_mesh_Fb.vertexBuffer[i].y;
        (*outVertexBuffer)[i].z = triangleMeshFb->triangle_mesh_Fb.vertexBuffer[i].z;
    }

    U_LOG_D("%s oxr_xrTriangleMeshGetVertexBufferFB vertexCount:%d", DEBUG_TAG, triangleMeshFb->triangle_mesh_Fb.vertexCount);
    return XR_SUCCESS;
}

XrResult oxr_xrTriangleMeshBeginUpdateFB(XrTriangleMeshFB mesh) {
    U_LOG_D("kidi::in");
    return XR_SUCCESS;
}

XrResult oxr_xrTriangleMeshEndUpdateFB(XrTriangleMeshFB mesh, uint32_t vertexCount, uint32_t triangleCount) {
    U_LOG_D("kidi::in");
    return XR_SUCCESS;
}

XrResult oxr_xrTriangleMeshBeginVertexBufferUpdateFB(XrTriangleMeshFB mesh, uint32_t *outVertexCount) {
    U_LOG_D("kidi::in");
    return XR_SUCCESS;
}

XrResult oxr_xrTriangleMeshEndVertexBufferUpdateFB(XrTriangleMeshFB mesh) {
    U_LOG_D("kidi::in");
    return XR_SUCCESS;
}
