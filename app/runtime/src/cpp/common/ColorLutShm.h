#pragma once

#include <atomic>
#include <cstdint>
#include <cstddef>
#include <memory>
#include <string>
#include <optional>
#include <string_view>
#include <array>
#include <vector>
#include <semaphore.h>  // POSIX 信号量

#include <android/sharedmem.h>

namespace yvr {
namespace runtime {

constexpr uint32_t kColorLutShmMagic = 0x4C555453;  // "LUTS"
constexpr std::string_view kColorLutShmName = "color_lut_shm";
constexpr uint32_t kColorLutMaxResolution = 64;
constexpr uint8_t kColorRgb = 3;
constexpr uint8_t kColorRgba = 4;
constexpr size_t kColorLutMaxDataSize = kColorLutMaxResolution * kColorLutMaxResolution * kColorLutMaxResolution * kColorRgba;

struct alignas(64) ColorLutShmHeader {       // 确保缓存行对齐
    uint32_t magic;                          // 魔数，用于验证共享内存有效性
    std::atomic<bool> isActive;              // LutData是否激活
    std::atomic<uint32_t> version;           // 数据版本号，每次更新时递增
    std::atomic<uint32_t> processedVersion;  // 已处理的版本号
};

struct ColorLutShmData {
    ColorLutShmHeader header;
    float weight;                                       // LUT 权重，范围为 [0, 1]
    uint32_t resolution;                                // LUT 分辨率
    uint32_t channels;                                  // LUT 通道数 (3 for RGB, 4 for RGBA)
    uint64_t dataSize;                                  // LUT 数据大小
    std::array<uint8_t, kColorLutMaxDataSize> lutData;  // 颜色查找表数据
    sem_t semaphore;                                    // POSIX 信号量，用于进程间同步
    std::atomic<bool> semInitialized{false};            // 信号量初始化标志
};

class ColorLutShm {
 public:
    struct LutMetadata {
        bool isActive;
        float weight;
        uint32_t resolution;
        uint32_t channels;
        size_t size;
        uint32_t version;
    };

    struct LutData : LutMetadata {
        std::vector<uint8_t> data;
    };

    ColorLutShm();
    ~ColorLutShm();

    // 禁止拷贝和移动
    ColorLutShm(const ColorLutShm&) = delete;
    ColorLutShm& operator=(const ColorLutShm&) = delete;
    ColorLutShm(ColorLutShm&&) = delete;
    ColorLutShm& operator=(ColorLutShm&&) = delete;

    // 初始化共享内存（服务器端）
    bool initServer();

    // 初始化共享内存（客户端）
    bool initClient(int fd);

    // 获取共享内存文件描述符
    int getFd() const noexcept;

    // 写入 LUT 数据
    bool writeColorLutData(const void* data, size_t size, uint32_t resolution, uint32_t channels);

    // 设置 LUT 权重
    bool setColorLutWeight(float weight);

    // 读取 LUT 数据（返回完整数据）
    std::optional<LutData> readColorLutData() const noexcept;

    // 读取 LUT 元数据（不包括实际数据）
    std::optional<LutMetadata> readLutMetadata() const noexcept;

    // 设置 LUT 激活状态
    bool setLutActive(bool active);

    // 获取 LUT 激活状态
    bool isLutActive() const noexcept;

    // 检查是否有新数据需要处理
    bool hasNewData() const noexcept;

    // 标记当前数据已处理
    bool markDataProcessed();

 private:
    class ShmResource {
     public:
        ShmResource(int fd, void* addr, size_t size);
        ~ShmResource();

        ShmResource(const ShmResource&) = delete;
        ShmResource& operator=(const ShmResource&) = delete;
        ShmResource(ShmResource&&) = delete;
        ShmResource& operator=(ShmResource&&) = delete;

        int fd() const noexcept { return fd_; }
        void* addr() const noexcept { return addr_; }
        size_t size() const noexcept { return size_; }

     private:
        int fd_;
        void* addr_;
        size_t size_;
    };

    std::unique_ptr<ShmResource> shmResource_;
    bool isServer_;

    // 获取共享内存数据结构
    ColorLutShmData* getData_() noexcept;
    const ColorLutShmData* getData_() const noexcept;

    // 验证共享内存有效性
    bool validateShm_() const noexcept;

    bool acquireLock_() const noexcept;
    bool releaseLock_() const noexcept;

    // RAII锁守卫，确保锁的自动释放
    class ScopedLock {
     public:
        explicit ScopedLock(const ColorLutShm* shm) : shm_(shm), locked_(false) {
            if (shm_) locked_ = shm_->acquireLock_();
        }
        ~ScopedLock() {
            if (locked_) shm_->releaseLock_();
        }
        bool isLocked() const noexcept { return locked_; }

     private:
        const ColorLutShm* shm_;
        bool locked_;
    };

    ColorLutShmData* getValidData_() noexcept;
    const ColorLutShmData* getValidData_() const noexcept;

    bool isValid() const noexcept;

    uint32_t getCurrentVersion() const noexcept;
    uint32_t getProcessedVersion() const noexcept;

    std::pair<std::optional<LutMetadata>, const ColorLutShmData*> readLutMetadataWithLock_() const noexcept;
};

}  // namespace runtime
}  // namespace yvr
