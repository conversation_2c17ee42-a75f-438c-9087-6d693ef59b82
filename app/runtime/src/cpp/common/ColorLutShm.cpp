#include "ColorLutShm.h"

#include <sys/mman.h>
#include <unistd.h>
#include <fcntl.h>
#include <string.h>
#include <errno.h>
#include <system_error>

#include "yvrutils/DynamicLog.h"

namespace yvr {
namespace runtime {

ColorLutShm::ShmResource::ShmResource(int fd, void* addr, size_t size) : fd_(fd), addr_(addr), size_(size) {}

ColorLutShm::ShmResource::~ShmResource() {
    if (addr_ != nullptr && addr_ != MAP_FAILED) {
        munmap(addr_, size_);
    }

    if (fd_ >= 0) {
        close(fd_);
    }
}

ColorLutShm::ColorLutShm() : shmResource_(nullptr), isServer_(false) {}

ColorLutShm::~ColorLutShm() {
    if (isServer_ && shmResource_) {
        ColorLutShmData* data = getData_();
        if (data != nullptr && data->semInitialized.load(std::memory_order_acquire)) {
            // 只有在服务器端才销毁信号量
            if (sem_destroy(&data->semaphore) != 0) {
                YLOGW("Failed to destroy semaphore: %s", strerror(errno));
            }
        }
    }

    shmResource_.reset();
}

// server端调用此接口初始化共享内存
bool ColorLutShm::initServer() {
    int fd = ASharedMemory_create(kColorLutShmName.data(), sizeof(ColorLutShmData));
    if (fd < 0) {
        YLOGE("Failed to create shared memory: %s", strerror(errno));
        return false;
    }

    void* addr = mmap(nullptr, sizeof(ColorLutShmData), PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (addr == MAP_FAILED) {
        YLOGE("Failed to map shared memory: %s", strerror(errno));
        close(fd);
        return false;
    }

    shmResource_ = std::make_unique<ShmResource>(fd, addr, sizeof(ColorLutShmData));
    isServer_ = true;

    // 初始化共享内存数据
    ColorLutShmData* data = getData_();
    if (data == nullptr) {
        YLOGE("Failed to get shared memory data");
        shmResource_.reset();
        return false;
    }
    data->header.magic = kColorLutShmMagic;
    data->header.version.store(0, std::memory_order_release);
    data->header.processedVersion.store(0, std::memory_order_release);
    data->header.isActive.store(false, std::memory_order_release);
    data->weight = 1.0f;
    data->resolution = 0;
    data->channels = 0;
    data->dataSize = 0;
    bool expected = false;
    if (data->semInitialized.compare_exchange_strong(expected, true, std::memory_order_acq_rel)) {
        if (sem_init(&data->semaphore, 1, 1) != 0) {  // 第二个参数为1表示进程间共享
            YLOGE("Failed to initialize semaphore: %s", strerror(errno));
            data->semInitialized.store(false, std::memory_order_release);
            shmResource_.reset();
            return false;
        }
    }

    // YLOGD("[server] color lut shm init success");
    return true;
}

// client端调用此接口映射共享内存
bool ColorLutShm::initClient(int fd) {
    void* addr = mmap(nullptr, sizeof(ColorLutShmData), PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (addr == MAP_FAILED) {
        YLOGE("Failed to map shared memory: %s", strerror(errno));
        close(fd);
        return false;
    }

    shmResource_ = std::make_unique<ShmResource>(fd, addr, sizeof(ColorLutShmData));
    isServer_ = false;

    if (!validateShm_()) {
        YLOGE("Invalid shared memory data");
        shmResource_.reset();
        return false;
    }

    // YLOGD("[client] color lut shm init success");
    return true;
}

int ColorLutShm::getFd() const noexcept { return shmResource_ ? shmResource_->fd() : -1; }

bool ColorLutShm::isValid() const noexcept { return shmResource_ != nullptr && validateShm_(); }

bool ColorLutShm::validateShm_() const noexcept {
    const ColorLutShmData* data = getData_();
    return data != nullptr && data->header.magic == kColorLutShmMagic;
}

ColorLutShmData* ColorLutShm::getData_() noexcept {
    if (!shmResource_) return nullptr;
    return static_cast<ColorLutShmData*>(shmResource_->addr());
}

const ColorLutShmData* ColorLutShm::getData_() const noexcept {
    if (!shmResource_) return nullptr;
    return static_cast<const ColorLutShmData*>(shmResource_->addr());
}

bool ColorLutShm::acquireLock_() const noexcept {
    const ColorLutShmData* shmData = getData_();
    if (!shmData || !shmData->semInitialized.load(std::memory_order_acquire)) {
        YLOGW("Cannot acquire lock: semaphore not initialized");
        return false;
    }

    if (sem_wait(const_cast<sem_t*>(&shmData->semaphore)) != 0) {
        YLOGW("Failed to acquire lock: %s", strerror(errno));
        return false;
    }
    return true;
}

bool ColorLutShm::releaseLock_() const noexcept {
    const ColorLutShmData* shmData = getData_();
    if (!shmData || !shmData->semInitialized.load(std::memory_order_acquire)) {
        YLOGW("Cannot release lock: semaphore not initialized");
        return false;
    }

    if (sem_post(const_cast<sem_t*>(&shmData->semaphore)) != 0) {
        YLOGW("Failed to release lock: %s", strerror(errno));
        return false;
    }
    return true;
}

ColorLutShmData* ColorLutShm::getValidData_() noexcept {
    if (!isValid()) return nullptr;
    return getData_();
}

const ColorLutShmData* ColorLutShm::getValidData_() const noexcept {
    if (!isValid()) return nullptr;
    return getData_();
}

// client端会调用此接口更新lut数据
bool ColorLutShm::writeColorLutData(const void* data, size_t size, uint32_t resolution, uint32_t channels) {
    if (data == nullptr) {
        YLOGE("Invalid parameter: data is null");
        return false;
    }

    if (size > kColorLutMaxDataSize) {
        YLOGE("Data size too large: %zu > %zu", size, kColorLutMaxDataSize);
        return false;
    }

    if (resolution > kColorLutMaxResolution) {
        YLOGE("Resolution too large: %u > %u", resolution, kColorLutMaxResolution);
        return false;
    }

    if (channels != kColorRgb && channels != kColorRgba) {
        YLOGE("Invalid channels: %u (must be 3 or 4)", channels);
        return false;
    }

    ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        YLOGE("Invalid shared memory state");
        return false;
    }

    ScopedLock lock(this);
    if (!lock.isLocked()) {
        YLOGE("Failed to acquire lock for writing color lut data");
        return false;
    }
    shmData->resolution = resolution;
    shmData->channels = channels;
    shmData->dataSize = size;
    memcpy(shmData->lutData.data(), data, size);

    // 最后更新版本号，确保所有数据都已写入
    shmData->header.version.fetch_add(1, std::memory_order_release);

    YLOGD("[client]:(%d) write color lut data (resolution=%u, channels=%u, size=%zu, version=%u) success", getpid(), resolution, channels, size,
          shmData->header.version.load(std::memory_order_relaxed));
    return true;
}

std::pair<std::optional<ColorLutShm::LutMetadata>, const ColorLutShmData*> ColorLutShm::readLutMetadataWithLock_() const noexcept {
    const ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        YLOGW("Invalid shared memory state");
        return {std::nullopt, nullptr};
    }

    ScopedLock lock(this);
    if (!lock.isLocked()) {
        YLOGW("Failed to acquire lock for reading lut metadata");
        return {std::nullopt, nullptr};
    }

    LutMetadata result;
    result.isActive = shmData->header.isActive.load(std::memory_order_acquire);
    result.weight = shmData->weight;
    result.resolution = shmData->resolution;
    result.channels = shmData->channels;
    result.size = shmData->dataSize;
    result.version = shmData->header.version.load(std::memory_order_acquire);

    return {std::optional<LutMetadata>(result), shmData};
}

// 合成端会调用此接口获取LutData
std::optional<ColorLutShm::LutData> ColorLutShm::readColorLutData() const noexcept {
    auto [metadataOpt, shmData] = readLutMetadataWithLock_();
    if (!metadataOpt || !shmData) {
        return std::nullopt;
    }

    LutData result;
    static_cast<LutMetadata&>(result) = *metadataOpt;

    if (result.size > 0 && result.size <= kColorLutMaxDataSize) {
        try {
            result.data.resize(result.size);
            memcpy(result.data.data(), shmData->lutData.data(), result.size);
        } catch (const std::exception& e) {
            YLOGW("Exception during data copy: %s", e.what());
            return std::nullopt;
        }
    }

    return result;
}

// 合成端会调用此接口获取LutMetadata
std::optional<ColorLutShm::LutMetadata> ColorLutShm::readLutMetadata() const noexcept { return readLutMetadataWithLock_().first; }

// 设置lut激活状态, client端调用
bool ColorLutShm::setLutActive(bool active) {
    ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        YLOGE("Invalid shared memory state");
        return false;
    }

    ScopedLock lock(this);
    if (!lock.isLocked()) {
        YLOGE("Failed to acquire lock for setting lut active state");
        return false;
    }

    shmData->header.isActive.store(active, std::memory_order_release);

    // 更新版本号
    shmData->header.version.fetch_add(1, std::memory_order_release);

    YLOGD("[client]:(%d) set color lut active(%d) success, version=%u", getpid(), active, shmData->header.version.load(std::memory_order_relaxed));
    return true;
}

// 合成端会调用此接口获取LutData激活状态
bool ColorLutShm::isLutActive() const noexcept {
    const ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        return false;
    }

    return shmData->header.isActive.load(std::memory_order_acquire);
}

// 合成端会调用此接口获取是否有新数据需要处理
bool ColorLutShm::hasNewData() const noexcept {
    const ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        return false;
    }

    uint32_t current = shmData->header.version.load(std::memory_order_acquire);
    uint32_t processed = shmData->header.processedVersion.load(std::memory_order_acquire);
    return current > processed;
}

// 合成端会调用此接口标记当前数据已处理
bool ColorLutShm::markDataProcessed() {
    ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        YLOGE("Invalid shared memory state");
        return false;
    }

    ScopedLock lock(this);
    if (!lock.isLocked()) {
        YLOGE("Failed to acquire lock for marking data processed");
        return false;
    }

    uint32_t current = shmData->header.version.load(std::memory_order_acquire);
    shmData->header.processedVersion.store(current, std::memory_order_release);

    YLOGD("[server]:(%d) marked data processed success, version=%u", getpid(), current);
    return true;
}

// 获取当前版本号
uint32_t ColorLutShm::getCurrentVersion() const noexcept {
    const ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        return 0;
    }

    return shmData->header.version.load(std::memory_order_acquire);
}

// 获取已处理的版本号
uint32_t ColorLutShm::getProcessedVersion() const noexcept {
    const ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        return 0;
    }

    return shmData->header.processedVersion.load(std::memory_order_acquire);
}

// client端调用此接口来设置lut权重
bool ColorLutShm::setColorLutWeight(float weight) {
    ColorLutShmData* shmData = getValidData_();
    if (shmData == nullptr) {
        YLOGE("Invalid shared memory state");
        return false;
    }

    ScopedLock lock(this);
    if (!lock.isLocked()) {
        YLOGE("Failed to acquire lock for setting lut weight");
        return false;
    }

    shmData->weight = weight;

    // 更新版本号
    shmData->header.version.fetch_add(1, std::memory_order_release);

    YLOGD("[client]:(%d) set color lut weight(%f) success, version=%u", getpid(), weight, shmData->header.version.load(std::memory_order_relaxed));
    return true;
}

}  // namespace runtime
}  // namespace yvr
