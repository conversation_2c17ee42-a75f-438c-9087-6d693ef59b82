#!/bin/sh

set -e

develop_webtoken="2bc6f8ed2b3387ddf65f3041cf2c3a90e22f9057f116c448a92c1e6f81f75b98"
develop_secret="SEC8c4d600cf6d83334d8de93383258c9b22677e10f71509a5e361534a107c5001a"
main_webtoken="fce9d8624c75ca4e2009de33ae52b86fae14c44a3c2b2eccd8abe949653fd322"
main_secret="SEC6c2ebd00d5d9070198ebb7e9d4effd4cdadec8cc316ea5503b514fb8260db289"

notify_ding_talk() {
    local note="$1"
    local change_info="$2"
    local build_version="$3"
    local newest_commit_id="$4"
    local branch_name="$5"
    local package_url="$6"
    local job_id="$7"
    local product_type="$8"

    local start_time
    start_time=$(date "+%Y-%m-%d %H:%M:%S")

    local content
content=$(cat <<EOF
# [XRRuntime | ${product_type} | ${build_version}](http://*************:8001/system_group/xrruntime/-/pipelines/${job_id})
---
> **任务**: #${job_id}\n
> **时间**: ${start_time}\n
> **版本**: ${build_version}\n
> **提交**: ${newest_commit_id}\n
> **分支**: ${branch_name}\n
> **目标**: ${package_url}\n
> **修改**:\n
$(echo "${change_info}" | sed 's/^/> - /')\n
EOF
    )

    if [ -n "${note}" ]; then
        content="${content}\n> **备注**:\n$(echo "${note}" | sed 's/^/> - /')\n"
    fi

    local access_token
    local secret
    if [ "${branch_name}" = "develop" ]; then
        access_token="${develop_webtoken}"
        secret="${develop_secret}"
    else
        access_token="${main_webtoken}"
        secret="${main_secret}"
    fi

    echo "Access token: ${access_token}"
    echo "Secret: ${secret}"
    local timestamp=$(date +%s%3N)
    # `base64 -w 0`
    #     `-w` 选项指定行宽为0，避免输出换行符
    # `jq -sRr @uri`
    #     `-s`: 将输入作为单个字符串处理。
    #     `-R`: 将输入作为原始字符串读入。
    #     `-r`: 以原始格式输出结果，而不是 JSON 格式。
    #     `@uri`: 是 jq 的一个过滤器，用于将字符串进行 URL 编码。
	local sign=$(echo -ne "$timestamp\n$secret" | openssl dgst -sha256 -hmac "$secret" -binary | base64 -w 0 | jq -sRr @uri)

    curl \
        -H 'Content-Type: application/json' \
        -d "{
              \"msgtype\": \"markdown\",
              \"markdown\": {
                \"title\": \"【版本发布】【${job_name} #${job_id}】\",
                \"text\": \"${content}\"
              }
            }" \
        "https://oapi.dingtalk.com/robot/send?access_token=${access_token}&timestamp=${timestamp}&sign=${sign}"
}

notify_ding_talk "$1" "$2" "$3" "$4" "$5" "$6" "$7" "$8"
