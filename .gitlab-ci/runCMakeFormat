#!/bin/sh
# Copyright 2019-2024, Collabora, Ltd.
# SPDX-License-Identifier: BSL-1.0
# Author: <PERSON><PERSON><PERSON> <<EMAIL>>

# Formats all the CMake files in this project

set -e

CMAKE_FORMAT=cmake-format
if ! command -v ${CMAKE_FORMAT} > /dev/null; then
        echo "cmake-format not found, do pipx install cmakelang" 1>&2
        exit 1
fi
(
    echo "${CMAKE_FORMAT}"
    "${CMAKE_FORMAT}" -v

    cd "$(dirname $0)/.."
    find \
        tracking \
        yvrutils \
        cmake \
        app \
        export \
        \( -name "CMakeLists.txt" -o -name "*.cmake" \) \
        -and -not \( -ipath \*/1stparty/\* \) \
        -and -not \( -ipath \*/3rdparty/\* \) \
        -and -not \( -ipath \*/.cxx/\* \) \
        -and -not \( -ipath \*/build/\* \) \
        -print0 | \
        xargs -0 "cmake-format" -c "$(pwd)/.gitlab-ci/.cmake-format.py" -i
)
