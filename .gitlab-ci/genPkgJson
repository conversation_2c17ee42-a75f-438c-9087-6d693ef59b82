#!/bin/bash

set -e

ROOT=$(dirname $0)/..

JSON_DEST_DIR=${ROOT}/prebuilt/prebuilt_yvr_vendor/vendor/etc/yvr

start_time=$(date "+%Y-%m-%d %H:%M:%S")

json_output=$(jq -n \
    --arg name "xrruntime" \
    --arg version "$1" \
    --arg branch "$2" \
    --arg commit "$3" \
    --arg variant "$4" \
    --arg time "$start_time" \
    '{name: $name, version: $version, branch: $branch, commit: $commit, variant: $variant, time: $time}')

echo "$json_output" > ${JSON_DEST_DIR}/xrruntime_release.json
